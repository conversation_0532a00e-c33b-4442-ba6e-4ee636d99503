<?php

function check_and_add_display_name_column(PDO $pdo): void
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files';");
        $table_exists = $stmt->fetchColumn();

        if ($table_exists) {
            
            $stmt = $pdo->query("PRAGMA table_info(digital_files);");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

            if (!in_array('display_name', $columns)) {
                $pdo->exec("ALTER TABLE digital_files ADD COLUMN display_name TEXT;");
            }
        } else {
        }
    } catch (PDOException $e) {
        
    }
}
?>
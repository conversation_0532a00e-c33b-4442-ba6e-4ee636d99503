<?php

function migrate_drop_digital_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        $fk_enabled_before = $pdo->query("PRAGMA foreign_keys")->fetchColumn();
        
        
        $pdo->exec("PRAGMA foreign_keys = OFF");
        
        $pdo->beginTransaction();

        
        $pdo->exec("DROP TABLE IF EXISTS digital_file_type_associations");

        
        if ($fk_enabled_before) {
            $pdo->exec("PRAGMA foreign_keys = ON");
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

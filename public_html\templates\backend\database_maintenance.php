<?php

require_once __DIR__ . '/../../includes/db_maintenance.php';

$maintenance_results = null;
if (isset($_GET['run_maintenance']) && $_GET['run_maintenance'] === '1') {
    $maintenance_results = run_database_maintenance();
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Manutenção da Base de Dados</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="admin.php?section=dashboard&<?= get_session_id_param() ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Manutenção da Base de Dados</li>
    </ol>

    <?php if ($maintenance_results): ?>
        <div class="alert alert-<?= $maintenance_results['success'] ? 'success' : 'danger' ?> alert-dismissible fade show" role="alert">
            <strong><?= $maintenance_results['message'] ?></strong>
            <ul>
                <?php foreach ($maintenance_results['details'] as $detail): ?>
                    <li><?= htmlspecialchars($detail) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-database me-1"></i>
            Operações de Manutenção
        </div>
        <div class="card-body">
            <p>Esta página permite executar operações de manutenção na base de dados para garantir a integridade dos dados.</p>
            
            <div class="alert alert-info">
                <h5>Operações disponíveis:</h5>
                <ul>
                    <li><strong>Limpeza de registos órfãos</strong> - Remove registos que perderam a sua ligação a outros registos (por exemplo, campos personalizados de itens de encomenda sem encomenda associada)</li>
                    <li><strong>Remoção de duplicados</strong> - Remove registos duplicados em tabelas críticas</li>
                </ul>
            </div>
            
            <div class="alert alert-warning">
                <strong>Atenção!</strong> Recomenda-se fazer uma cópia de segurança da base de dados antes de executar estas operações.
            </div>
            
            <a href="admin.php?section=database_maintenance&run_maintenance=1&<?= get_session_id_param() ?>" class="btn btn-primary">
                <i class="fas fa-cogs me-1"></i> Executar Manutenção
            </a>
        </div>
    </div>
</div>

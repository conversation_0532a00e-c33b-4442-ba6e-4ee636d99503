<?php

function handle_custom_field_form($action, $field_id = null) {
    
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $field_type_id = filter_input(INPUT_POST, 'field_type_id', FILTER_VALIDATE_INT);
    $min_chars = filter_input(INPUT_POST, 'min_chars', FILTER_VALIDATE_INT) ?: 0;
    $max_chars = filter_input(INPUT_POST, 'max_chars', FILTER_VALIDATE_INT) ?: 255;
    $price_modifier = filter_input(INPUT_POST, 'price_modifier', FILTER_VALIDATE_FLOAT) ?: 0.0;
    $is_required = isset($_POST['is_required']) ? (int)$_POST['is_required'] : 0;
    $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1;
    $config = $_POST['config'] ?? [];
    
    
    $errors = [];
    if (empty($name)) {
        $errors[] = "Nome do campo é obrigatório.";
    }
    
    if (!$field_type_id) {
        $errors[] = "Tipo de campo é obrigatório.";
    }
    
    if (!empty($errors)) {
        foreach ($errors as $error) {
            add_flash_message($error, 'danger');
        }
        $_SESSION['form_data'] = $_POST;
        $redirect_url = 'admin.php?section=custom_fields&action=';
        $redirect_url .= ($action === 'create') ? 'new' : 'edit&id=' . $field_id;
        header('Location: ' . $redirect_url . '&' . get_session_id_param());
        exit;
    }
    
    try {
        
        $field_data = [
            'name' => $name,
            'description' => $description,
            'field_type_id' => $field_type_id,
            'min_chars' => $min_chars,
            'max_chars' => $max_chars,
            'price_modifier' => $price_modifier,
            'is_required' => $is_required,
            'is_active' => $is_active
        ];
        
        
        if (!empty($config)) {
            $field_data['config'] = $config;
        }
        
        
        if ($action === 'create') {
            $result = create_custom_field($field_data);
            if ($result) {
                
                if (isset($_POST['dropdown_options']) && is_array($_POST['dropdown_options'])) {
                    handle_dropdown_options($result, $_POST['dropdown_options']);
                }
                add_flash_message('Campo personalizado criado com sucesso!', 'success');
            } else {
                throw new Exception("Falha ao criar campo personalizado.");
            }
        } else { 
            if (!$field_id) {
                throw new Exception("ID do campo inválido.");
            }
            
            $result = update_custom_field($field_id, $field_data);
            if ($result) {
                
                if (isset($_POST['dropdown_options']) && is_array($_POST['dropdown_options'])) {
                    handle_dropdown_options($field_id, $_POST['dropdown_options']);
                }
                add_flash_message('Campo personalizado atualizado com sucesso!', 'success');
            } else {
                throw new Exception("Falha ao atualizar campo personalizado.");
            }
        }
        
        header('Location: admin.php?section=custom_fields&' . get_session_id_param());
        exit;
        
    } catch (Exception $e) {
        add_flash_message('Erro ao guardar campo personalizado: ' . $e->getMessage(), 'danger');
        $_SESSION['form_data'] = $_POST;
        $redirect_url = 'admin.php?section=custom_fields&action=';
        $redirect_url .= ($action === 'create') ? 'new' : 'edit&id=' . $field_id;
        header('Location: ' . $redirect_url . '&' . get_session_id_param());
        exit;
    }
}

function handle_dropdown_options($field_id, $dropdown_options) {
    
    $field = get_custom_field($field_id);
    if (!$field) {
        return false;
    }
    
    $field_type = get_custom_field_type($field['field_type_id']);
    if (!$field_type || $field_type['slug'] !== 'texto-dropdown') {
        return true; 
    }
    
    
    $existing_options = get_custom_field_dropdown_options($field_id, false);
    $existing_option_ids = array_column($existing_options, 'id');
    $processed_option_ids = [];
    
    
    foreach ($dropdown_options as $option_data) {
        if (empty($option_data['option_text']) || empty($option_data['option_value'])) {
            continue; 
        }
        
        $option_info = [
            'custom_field_id' => $field_id,
            'option_text' => trim($option_data['option_text']),
            'option_value' => trim($option_data['option_value']),
            'price_modifier' => floatval($option_data['price_modifier'] ?? 0),
            'sort_order' => intval($option_data['sort_order'] ?? 0),
            'is_active' => isset($option_data['is_active']) ? 1 : 0
        ];
        
        if (isset($option_data['id']) && !empty($option_data['id'])) {
            
            $option_id = intval($option_data['id']);
            update_custom_field_dropdown_option($option_id, $option_info);
            $processed_option_ids[] = $option_id;
        } else {
            
            $new_option_id = create_custom_field_dropdown_option($option_info);
            if ($new_option_id) {
                $processed_option_ids[] = $new_option_id;
            }
        }
    }
    
    
    $options_to_delete = array_diff($existing_option_ids, $processed_option_ids);
    foreach ($options_to_delete as $option_id) {
        delete_custom_field_dropdown_option($option_id);
    }
    
    return true;
}

# Digital Product Transaction Error Fix

## Problem
When editing a digital product in the admin panel, users encountered the error message: "Erro ao guardar o produto digital: There is no active transaction". This prevented saving changes to digital products.

## Root Cause Analysis
The issue was caused by nested transactions in the code:

1. In `admin.php`, when saving a digital product, a transaction was started.
2. During this process, the `update_digital_file_file_types` function in `digital_files_functions.php` was called.
3. This function also started its own transaction, which conflicted with the already active transaction from `admin.php`.
4. When the inner transaction tried to commit, it failed because it wasn't properly nested, resulting in the error message.

## Solution
1. Modified `update_digital_file_file_types` function in `digital_files_functions.php` to:
   - Accept a new parameter `$manage_transaction` (default: true)
   - Only start a transaction if `$manage_transaction` is true and no transaction is already active
   - Only commit or rollback if it started the transaction itself

2. Updated calls to `update_digital_file_file_types` in `digital_product_functions.php` to:
   - Pass `false` for the `$manage_transaction` parameter when called from within another transaction

3. Updated transaction handling in `admin.php` to:
   - Check if a transaction is already active before starting a new one
   - Only commit if a transaction is active

## Code Changes
1. `digital_files_functions.php`: Added transaction management parameter to `update_digital_file_file_types`
2. `digital_product_functions.php`: Updated calls to pass `false` for transaction management
3. `admin.php`: Added checks for active transactions before starting/committing

## Prevention
- Always check if a transaction is already active before starting a new one
- Design functions that may be called within transactions to accept a parameter controlling transaction management
- Use proper transaction nesting or avoid nested transactions altogether
- Implement thorough error handling for database operations

## Status
Fixed. Digital products can now be saved without transaction errors.

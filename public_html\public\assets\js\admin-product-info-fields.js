document.addEventListener('DOMContentLoaded', function() {
    const infoFieldsContainer = document.getElementById('product-info-fields-container');
    const addInfoFieldBtn = document.getElementById('add-info-field-btn');

    if (!infoFieldsContainer || !addInfoFieldBtn) return;

    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    const isEditing = urlParams.get('action') === 'edit' && productId;

    let isFormSubmitting = false;

    const productForm = document.querySelector('form');
    if (productForm) {
        productForm.addEventListener('submit', function() {
            isFormSubmitting = true;
        });
    }

    function createInfoField(id = null, icon = '', text = '') {
        
        const fieldId = id || 'new_' + Date.now() + '_' + Math.floor(Math.random() * 1000);

        
        if (!id && (icon || text)) {
            const existingFields = document.querySelectorAll('.product-info-field');
            for (const field of existingFields) {
                const fieldIcon = field.querySelector('.info-field-icon')?.value || '';
                const fieldText = field.querySelector('.info-field-text')?.value || '';

                if (fieldIcon === icon && fieldText === text) {
                    return null;
                }
            }
        }

        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'product-info-field row mb-2 align-items-center';
        fieldDiv.dataset.fieldId = fieldId;

        fieldDiv.innerHTML = `
            <div class="col-md-3">
                <input type="text" class="form-control info-field-icon" name="info_fields[${fieldId}][icon]" value="${escapeHtml(icon)}" placeholder="ri-truck-line">
            </div>
            <div class="col-md-7">
                <input type="text" class="form-control info-field-text" name="info_fields[${fieldId}][text]" value="${escapeHtml(text)}" placeholder="Texto da informação">
            </div>
            <div class="col-md-2 text-center">
                <button type="button" class="btn btn-danger btn-sm remove-info-field" title="Remover campo">
                    <i class="ri-delete-bin-line"></i>
                </button>
                <input type="hidden" name="info_fields[${fieldId}][id]" value="${fieldId}">
                <input type="hidden" class="info-field-delete" name="info_fields[${fieldId}][delete]" value="0">
                <input type="hidden" name="info_fields[${fieldId}][unique_key]" value="${Date.now()}_${Math.floor(Math.random() * 1000)}">
            </div>
        `;

        const removeBtn = fieldDiv.querySelector('.remove-info-field');
        removeBtn.addEventListener('click', function() {
            const deleteInput = fieldDiv.querySelector('.info-field-delete');
            if (deleteInput) {
                deleteInput.value = '1';
            }
            fieldDiv.style.display = 'none';

            if (isEditing && !isNaN(parseInt(fieldId))) {
                deleteInfoFieldAjax(fieldId);
            }
        });

        return fieldDiv;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    
    

    function deleteInfoFieldAjax(fieldId) {
        if (!isEditing || isFormSubmitting) return;

        const url = `admin.php?section=products&action=delete_product_info_field&field_id=${fieldId}&${getSessionIdParam()}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                
            })
            .catch(error => {
                
            });
    }

    function getSessionIdParam() {
        const sessionIdMatch = document.location.search.match(/sid=([^&]+)/);
        return sessionIdMatch ? `sid=${sessionIdMatch[1]}` : '';
    }

    
    
    
    function setupFieldEventListeners(field) {
        
        
        
        
             
        
    }

    
    let isAddingField = false;

    addInfoFieldBtn.addEventListener('click', function() {
        
        if (isAddingField) {
            return;
        }

        isAddingField = true;

        const newField = createInfoField();

        
        if (!newField) {
            isAddingField = false;
            return;
        }

        infoFieldsContainer.appendChild(newField);

        
        const newIconInput = newField.querySelector('.info-field-icon');
        if (newIconInput && window.AdminIconSelector && typeof window.AdminIconSelector.createIconSelector === 'function') {
            
            
            setTimeout(function() {
                window.AdminIconSelector.createIconSelector(newIconInput);
            }, 0);
        }

        
        

        
        const iconInput = newField.querySelector('.info-field-icon');
        const textInput = newField.querySelector('.info-field-text');
        if (iconInput && textInput) {
             iconInput.addEventListener('input', function() {
                 checkForDuplicates(newField);
             });
             textInput.addEventListener('input', function() {
                 checkForDuplicates(newField);
             });
        }

        isAddingField = false; 
    });

    
    function checkForDuplicates(currentField) {
        if (!currentField) return;

        const currentIcon = currentField.querySelector('.info-field-icon')?.value || '';
        const currentText = currentField.querySelector('.info-field-text')?.value || '';

        
        if (!currentIcon && !currentText) return;

        const existingFields = document.querySelectorAll('.product-info-field');
        let duplicateFound = false;

        existingFields.forEach(field => {
            if (field === currentField) return; 

            const fieldIcon = field.querySelector('.info-field-icon')?.value || '';
            const fieldText = field.querySelector('.info-field-text')?.value || '';

            if (fieldIcon === currentIcon && fieldText === currentText) {
                duplicateFound = true;

                
                currentField.classList.add('border', 'border-danger');

                
                setTimeout(() => {
                    currentField.classList.remove('border', 'border-danger');
                }, 2000);
            }
        });

        return duplicateFound;
    }

    document.querySelectorAll('.remove-info-field').forEach(btn => {
        btn.addEventListener('click', function() {
            const fieldDiv = this.closest('.product-info-field');
            const fieldId = fieldDiv.dataset.fieldId;
            const deleteInput = fieldDiv.querySelector('.info-field-delete');
            if (deleteInput) {
                deleteInput.value = '1';
            }
            fieldDiv.style.display = 'none';

            if (isEditing && !isNaN(parseInt(fieldId))) {
                deleteInfoFieldAjax(fieldId);
            }
        });
    });

    
    
    
    
    
    
    
    
});

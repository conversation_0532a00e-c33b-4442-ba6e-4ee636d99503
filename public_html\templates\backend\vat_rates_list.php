<?php

if (!defined('ADMIN_CONTEXT') || !ADMIN_CONTEXT) {
    die('Access Denied');
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Taxas de IVA</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="admin.php?<?= get_session_id_param() ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Taxas de IVA</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
             <i class="fas fa-percentage me-1"></i> Gestão de Taxas de IVA
        </div>
        <div class="card-body">
             <p class="mb-3">Configure as diferentes taxas de IVA disponíveis para seleção nos produtos.</p>

             <!-- Inline Form for Adding VAT Rate -->
             <form id="add-vat-rate-form" class="row g-3 align-items-end mb-4 border p-3 rounded bg-light">
                 <input type="hidden" name="is_ajax" value="1">
                 <input type="hidden" name="section" value="vat_rates">
                 <input type="hidden" name="action" value="create_vat">
                 <input type="hidden" name="csrf_token" value="<?= get_csrf_token() ?>">

                 <div class="col-md-3">
                     <label for="add_vat_rate" class="form-label">Nova Taxa (%)</label>
                     <input type="number" class="form-control form-control-sm" id="add_vat_rate" name="rate" min="0" max="100" step="0.1" required>
                 </div>
                 <div class="col-md-5">
                     <label for="add_vat_description" class="form-label">Descrição</label>
                     <input type="text" class="form-control form-control-sm" id="add_vat_description" name="description" required>
                 </div>
                 <div class="col-md-2">
                     <div class="form-check mb-2">
                         <input type="checkbox" class="form-check-input" id="add_vat_is_default" name="is_default" value="1">
                         <label class="form-check-label" for="add_vat_is_default">Padrão?</label>
                     </div>
                 </div>
                 <div class="col-md-2">
                     <button type="submit" class="btn btn-primary btn-sm w-100">Adicionar</button>
                 </div>
                 <div class="col-12">
                    <div class="form-text">Ex: "IVA Normal (23%)", "IVA Reduzido (6%)", "Isento de IVA (0%)"</div>
                 </div>
             </form>
             <!-- End Inline Form -->

            <?php if (empty($vat_rates)): ?>
                <div class="alert alert-info mt-3">
                    Não existem taxas de IVA definidas. Utilize o formulário acima para adicionar.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Taxa (%)</th>
                                <th>Descrição</th>
                                <th>Padrão</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="vat-rates-table-body">
                            <?php foreach ($vat_rates as $rate): ?>
                                <tr>
                                    <td><?= number_format($rate['rate'], 1, ',', '.') ?>%</td>
                                    <td><?= sanitize_input($rate['description']) ?></td>
                                    <td>
                                        <?php if ($rate['is_default']): ?>
                                            <span class="badge bg-success">Sim</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Não</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if (!$rate['is_default']): ?>
                                                <button type="button" class="btn btn-sm btn-success set-default-vat"
                                                        title="Definir como Padrão"
                                                        data-id="<?= $rate['id'] ?>" data-csrf="<?= get_csrf_token() ?>">
                                                    <i class="fas fa-check"></i> Definir Padrão
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-vat-rate"
                                                        title="Eliminar Taxa"
                                                        data-id="<?= $rate['id'] ?>" data-csrf="<?= get_csrf_token() ?>">
                                                    <i class="fas fa-trash"></i> Eliminar
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modals Removed -->

<script>
// Inline functionality will be handled by admin-vat-rates.js
</script>

<?php
// Debug meta tags for Pinterest sharing
header('Content-Type: text/html; charset=utf-8');

// Get product slug from URL parameter
$product_slug = $_GET['product'] ?? '';

if (empty($product_slug)) {
    echo '<h1>Meta Tag Debugger</h1>';
    echo '<p>Usage: meta_debug.php?product=product-slug</p>';
    echo '<p>This tool shows what meta tags Pinterest would see when scraping a product URL.</p>';
    exit;
}

// Construct the product URL
$product_url = "https://www.joaocesarsilva.com/index.php?product=" . urlencode($product_slug);

echo '<h1>Meta Tag Debug for Product: ' . htmlspecialchars($product_slug) . '</h1>';
echo '<p><strong>Product URL:</strong> <a href="' . htmlspecialchars($product_url) . '" target="_blank">' . htmlspecialchars($product_url) . '</a></p>';

// Fetch the HTML content
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'User-Agent: Mozilla/5.0 (compatible; Pinterest/0.2; +http://www.pinterest.com/bot.html)',
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        ],
        'timeout' => 30
    ]
]);

$html = @file_get_contents($product_url, false, $context);

if ($html === false) {
    echo '<div style="color: red;"><strong>Error:</strong> Could not fetch the product page. Check if the URL is accessible.</div>';
    exit;
}

// Parse HTML to extract meta tags
$dom = new DOMDocument();
@$dom->loadHTML($html);
$xpath = new DOMXPath($dom);

// Extract relevant meta tags
$meta_tags = [
    'title' => $dom->getElementsByTagName('title')->item(0)?->textContent ?? 'Not found',
    'description' => '',
    'og:title' => '',
    'og:description' => '',
    'og:image' => '',
    'og:url' => '',
    'og:type' => '',
    'twitter:card' => '',
    'twitter:title' => '',
    'twitter:description' => '',
    'twitter:image' => ''
];

// Get meta description
$meta_desc = $xpath->query('//meta[@name="description"]')->item(0);
if ($meta_desc) {
    $meta_tags['description'] = $meta_desc->getAttribute('content');
}

// Get Open Graph tags
$og_tags = $xpath->query('//meta[starts-with(@property, "og:")]');
foreach ($og_tags as $tag) {
    $property = $tag->getAttribute('property');
    $content = $tag->getAttribute('content');
    if (isset($meta_tags[$property])) {
        $meta_tags[$property] = $content;
    }
}

// Get Twitter tags
$twitter_tags = $xpath->query('//meta[starts-with(@name, "twitter:")]');
foreach ($twitter_tags as $tag) {
    $name = $tag->getAttribute('name');
    $content = $tag->getAttribute('content');
    if (isset($meta_tags[$name])) {
        $meta_tags[$name] = $content;
    }
}

// Display results
echo '<h2>Meta Tags Found:</h2>';
echo '<table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
echo '<tr><th>Tag</th><th>Content</th><th>Status</th></tr>';

foreach ($meta_tags as $tag => $content) {
    $status = empty($content) ? '<span style="color: red;">Missing</span>' : '<span style="color: green;">Found</span>';
    $display_content = empty($content) ? '<em>Not set</em>' : htmlspecialchars($content);
    
    echo '<tr>';
    echo '<td><strong>' . htmlspecialchars($tag) . '</strong></td>';
    echo '<td>' . $display_content . '</td>';
    echo '<td>' . $status . '</td>';
    echo '</tr>';
}

echo '</table>';

// Pinterest-specific recommendations
echo '<h2>Pinterest Sharing Analysis:</h2>';
echo '<ul>';

if (empty($meta_tags['og:title']) && empty($meta_tags['title'])) {
    echo '<li style="color: red;">❌ No title found - Pinterest needs a title</li>';
} else {
    echo '<li style="color: green;">✅ Title available for Pinterest</li>';
}

if (empty($meta_tags['og:description']) && empty($meta_tags['description'])) {
    echo '<li style="color: red;">❌ No description found - Pinterest will have no description</li>';
} else {
    echo '<li style="color: green;">✅ Description available for Pinterest</li>';
}

if (empty($meta_tags['og:image'])) {
    echo '<li style="color: red;">❌ No image found - Pinterest needs an image URL</li>';
} else {
    echo '<li style="color: green;">✅ Image available for Pinterest: ' . htmlspecialchars($meta_tags['og:image']) . '</li>';
}

echo '</ul>';

// Test Pinterest URL
$pinterest_title = $meta_tags['og:title'] ?: $meta_tags['title'];
$pinterest_desc = $meta_tags['og:description'] ?: $meta_tags['description'];
$pinterest_image = $meta_tags['og:image'];

if ($pinterest_title && $pinterest_desc && $pinterest_image) {
    $pinterest_url = 'https://www.pinterest.com/pin/create/button/?' .
        'url=' . urlencode($product_url) .
        '&media=' . urlencode($pinterest_image) .
        '&description=' . urlencode($pinterest_title . ' - ' . $pinterest_desc);
    
    echo '<h2>Test Pinterest Share:</h2>';
    echo '<p><a href="' . htmlspecialchars($pinterest_url) . '" target="_blank" style="background: #bd081c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">📌 Test Pinterest Share</a></p>';
    echo '<p><small>URL: ' . htmlspecialchars($pinterest_url) . '</small></p>';
}
?>

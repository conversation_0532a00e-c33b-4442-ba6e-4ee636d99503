<?php

function get_order_statuses() {
    return [
        'pending' => [
            'name_pt' => 'Pendente',
            'badge_class' => 'bg-warning text-dark',
            'description' => 'Encomenda recebida, aguardando confirmação de pagamento'
        ],
        'processing' => [
            'name_pt' => 'Em Processamento',
            'badge_class' => 'bg-info text-dark',
            'description' => 'Pagamento confirmado, encomenda em preparação'
        ],
        'shipped' => [
            'name_pt' => 'Enviada',
            'badge_class' => 'bg-primary',
            'description' => 'Encomenda enviada para o cliente'
        ],
        'completed' => [
            'name_pt' => 'Concluída',
            'badge_class' => 'bg-success',
            'description' => 'Encomenda entregue e concluída'
        ],
        'cancelled' => [
            'name_pt' => 'Cancelada',
            'badge_class' => 'bg-danger',
            'description' => 'Encomenda cancelada'
        ],
        'refunded' => [
            'name_pt' => 'Reembolsada',
            'badge_class' => 'bg-dark',
            'description' => 'Encomenda reembolsada ao cliente'
        ]
    ];
}

function get_status_name_pt($status_code) {
    $statuses = get_order_statuses();
    return $statuses[$status_code]['name_pt'] ?? $status_code;
}

function get_status_badge_class($status_code) {
    $statuses = get_order_statuses();
    return $statuses[$status_code]['badge_class'] ?? 'bg-secondary';
}

function get_status_codes() {
    return array_keys(get_order_statuses());
}

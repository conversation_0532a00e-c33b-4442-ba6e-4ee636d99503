<?php

error_reporting(E_ALL);
define('DEBUG_MODE', false);

if (DEBUG_MODE) {
    ini_set('display_errors', '1');
} else {
    ini_set('display_errors', '0');
}

ini_set('error_log', __DIR__ . '/logs/error.log');

date_default_timezone_set('Europe/Lisbon');

define('DB_PATH', __DIR__ . '/../dbjcs2112ew.sqlite');

define('SESSION_LIFETIME_SECONDS', 86400); 
define('SESSION_PARAM_NAME', 'sid');
define('SESSION_FINGERPRINT_SALT', '6f8c7d12b5c3ea8ba4f72e9064f0d9bb');

$default_settings = [
    'store_name' => 'JCS eStore VV4.0',
    'admin_email' => '<EMAIL>', 
    'admin_password_hash' => '', 
    'store_description' => 'Descubra nossa coleção de itens premium.',
    'footer_info_line1' => '', 
    'footer_info_line2' => '', 
    'default_currency' => 'EUR',
    'currency_symbol' => '€',
    'items_per_page' => 12, 
    
];

define('CSRF_SECRET', 'c1a9f9e3f43d56e8a0bc4176d2b8a61e');

define('BASE_URL', 'https://joaocesarsilva.com');

define('PROJECT_ROOT', __DIR__);
define('PUBLIC_PATH', PROJECT_ROOT . '/public');
define('ASSETS_URL', BASE_URL . '/public/assets');
define('IMAGES_URL', BASE_URL . '/public/assets/images');
define('PRODUCT_IMAGES_URL', IMAGES_URL . '/products');
define('PRODUCT_IMAGES_PATH', PUBLIC_PATH . '/assets/images/products');
define('WATERMARK_IMAGE_PATH', PUBLIC_PATH . '/assets/images/watermark/watermark.png');

$default_email_settings = [
    'smtp_host' => 'mail.joaocesarsilva.com',
    'smtp_username' => '<EMAIL>',
    'smtp_password' => '', 
    'smtp_port' => 587,
    'smtp_secure' => 'tls', 
    'from_email' => '<EMAIL>',
    'from_name' => $default_settings['store_name'],
    'reply_to_email' => '<EMAIL>',
    'reply_to_name' => 'Info ' . $default_settings['store_name'],
];

define('APP_LANG', 'pt_PT');
?>



(function() {
    'use strict';

    
    document.addEventListener('DOMContentLoaded', initModalHandler);
    
    
    if (window.AdminUtils) {
        const originalInitPageSpecificFeatures = window.initPageSpecificFeatures || function() {};
        window.initPageSpecificFeatures = function() {
            originalInitPageSpecificFeatures.apply(this, arguments);
            initModalHandler();
        };
    }

    

    function initModalHandler() {
        
        
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            
            if (modal.dataset.modalHandlerInitialized === 'true') {
                return;
            }
            
            
            modal.dataset.modalHandlerInitialized = 'true';
            
            
            modal.addEventListener('hidden.bs.modal', cleanupModal);
            
            
            const deleteButtons = modal.querySelectorAll('.btn-danger[id*="confirm"], .btn-danger[id*="delete"]');
            
            deleteButtons.forEach(button => {
                
                if (button.dataset.deleteHandlerInitialized === 'true') {
                    return;
                }
                
                
                button.dataset.deleteHandlerInitialized = 'true';
                
                
                button.addEventListener('click', function(e) {
                    
                    if (this.tagName.toLowerCase() === 'a' && this.href) {
                        e.preventDefault();
                        
                        
                        const deleteUrl = this.href;
                        
                        
                        const modalElement = this.closest('.modal');
                        if (modalElement) {
                            const bsModal = bootstrap.Modal.getInstance(modalElement);
                            if (bsModal) {
                                bsModal.hide();
                            }
                        }
                        
                        
                        cleanupModal();
                        
                        
                        setTimeout(function() {
                            window.location.href = deleteUrl;
                        }, 100);
                    }
                });
            });
        });
    }

    

    function cleanupModal() {
        
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
        
        
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }
})();

# Memory Bank File: projectbrief.md

## Purpose
This project is a PHP-based e-commerce platform with a focus on product customization, comprehensive digital product management, a feature-rich blog system, and streamlined order management. The system handles product catalog management (including physical and digital goods), order processing, payment integration, and customer customization features.

## Scope
The platform includes:
*   Product catalog management with customizable product attributes, supporting physical (simple, variation) and digital products.
*   Comprehensive digital product and license management system.
*   Feature-rich blog system with multiple content types and SEO optimization.
*   Order management system with status tracking and automated stock handling.
*   Payment method integration.
*   Customer customization options for products.
*   Admin dashboard for system configuration, content management, and maintenance.
*   Frontend UI/UX enhancements prioritizing readability, simplicity, and responsiveness.
*   Security and privacy enhancements including data encryption and secure token management.

## Core Goals
1.  **Maintain a robust and scalable codebase:** Emphasize quality, security, and adherence to best practices.
2.  **Ensure secure handling of customer data and payments:** Implement encryption, secure tokens, and follow security best practices.
3.  **Provide flexible customization options:** For products, site appearance, and system behavior.
4.  **Optimize performance for high traffic scenarios:** Through efficient code and database management.
5.  **Implement clear error handling and logging:** For both user feedback and developer debugging.
6.  **Deliver a superior User Experience (UX):** Align with specific user preferences (simplicity, clarity, responsiveness).
7.  **Offer Advanced Digital Product Capabilities:** Secure delivery, robust licensing, and comprehensive management.
8.  **Streamline Administrative Tasks:** Provide efficient tools for content, order, and system management.

## Technical Requirements
1.  **Cookieless Operation**: The entire application must function without using cookies. Session management and user tracking must use alternative methods such as database tracking (e.g., `order_visits` table) and potentially URL parameters where appropriate and secure.
2.  **PHP 8.2+ Adherence**: Utilize features and best practices of modern PHP.
3.  **PSR-12 Coding Standards**: Maintain code consistency and readability.
4.  **Database**: SQLite, with the database file stored outside the web root for security.
5.  **Security Focus**: Prioritize secure coding practices, input validation, CSRF protection, and protection against common web vulnerabilities.
<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/security.php';

function get_decrypted_license_name(array $license): string
{
    if (empty($license['customer_name'])) {
        return '';
    }

    $is_encrypted = isset($license['is_encrypted']) && $license['is_encrypted'] == 1;

    if (!$is_encrypted && is_likely_encrypted($license['customer_name'])) {
        $is_encrypted = true;
    }

    if ($is_encrypted) {
        $decrypted = decrypt_sensitive_data($license['customer_name']);
        return $decrypted !== false ? $decrypted : $license['customer_name'];
    }

    return $license['customer_name'];
}

function get_decrypted_license_email(array $license): string
{
    if (empty($license['customer_email'])) {
        return '';
    }

    $is_encrypted = isset($license['is_encrypted']) && $license['is_encrypted'] == 1;

    if (!$is_encrypted && is_likely_encrypted($license['customer_email'])) {
        $is_encrypted = true;
    }

    if ($is_encrypted) {
        $decrypted = decrypt_sensitive_data($license['customer_email']);
        return $decrypted !== false ? $decrypted : $license['customer_email'];
    }

    return $license['customer_email'];
}

function get_license_censored_name(string $name, string $license_status = 'active'): string
{

    if (in_array($license_status, ['canceled', 'disabled'])) {
        if (strlen($name) <= 2) {
            return $name[0] . '*';
        }

        return $name[0] . str_repeat('*', strlen($name) - 2) . $name[strlen($name) - 1];
    }

    $name_parts = explode(' ', $name);

    if (count($name_parts) == 1) {

        if (strlen($name) <= 3) {
            return $name;
        }
        return substr($name, 0, 2) . str_repeat('*', strlen($name) - 3) . substr($name, -1);
    }

    $first_name = $name_parts[0];
    $last_name = end($name_parts);

    return $first_name . ' ' . substr($last_name, 0, 1) . '.';
}

function get_license_censored_email(string $email, string $license_status = 'active', bool $user_verified = false): string
{
    
    if ($user_verified) {
        $parts = explode('@', $email);
        if (count($parts) != 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 2) {
            $censored_username = $username;
        } else {
            $censored_username = $username[0] . str_repeat('*', strlen($username) - 2) . $username[strlen($username) - 1];
        }

        $domain_parts = explode('.', $domain);
        $tld = end($domain_parts);
        $domain_name = implode('.', array_slice($domain_parts, 0, -1));

        if (strlen($domain_name) <= 2) {
            $censored_domain = $domain_name;
        } else {
            $censored_domain = $domain_name[0] . str_repeat('*', strlen($domain_name) - 2) . $domain_name[strlen($domain_name) - 1];
        }

        return $censored_username . '@' . $censored_domain . '.' . $tld;
    }

    
    $parts = explode('@', $email);
    if (count($parts) != 2) {
        return str_repeat('*', strlen($email));
    }

    $username = $parts[0];
    $domain = $parts[1];

    
    $censored_username = $username[0] . str_repeat('*', max(1, strlen($username) - 1));

    
    $domain_parts = explode('.', $domain);
    $tld = end($domain_parts);

    
    $censored_domain = str_repeat('*', max(1, strlen(implode('.', array_slice($domain_parts, 0, -1)))));

    
    if (strlen($tld) <= 2) {
        $censored_tld = $tld[0] . '*';
    } else {
        $censored_tld = $tld[0] . str_repeat('*', strlen($tld) - 2) . $tld[strlen($tld) - 1];
    }

    return $censored_username . '@' . $censored_domain . '.' . $censored_tld;
}

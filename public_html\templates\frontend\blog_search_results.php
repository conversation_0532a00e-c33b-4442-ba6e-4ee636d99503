<?php

$blog_posts = $blog_posts ?? [];
$total_posts = $total_posts ?? 0;
$current_page = $current_page ?? 1;
$per_page = $per_page ?? 5; 
$total_pages = ceil($total_posts / $per_page);
$search_term = $search_term ?? '';

?>

<h1 class="text-3xl font-bold mb-2 text-white">Pesquisa no Blog</h1>

<!-- Blog Search Form -->
<form action="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" method="GET" class="mb-8">
    <input type="hidden" name="view" value="blog_search">
    <div class="flex items-center max-w-lg mx-auto bg-gray-800 rounded-lg overflow-hidden px-2 py-1 justify-between">
        <input class="text-base bg-gray-800 text-white flex-grow border-none focus:outline-none focus:ring-0 pl-2"
               type="search" id="blog-search-input" name="q"
               placeholder="Pesquisar no blog..."
               value="<?php echo htmlspecialchars($search_term); ?>">
        <button type="submit" class="bg-primary text-white rounded-md px-3 py-1 ml-2 hover:bg-secondary transition">
            <i class="ri-search-line"></i> <span class="hidden sm:inline">Pesquisar</span>
        </button>
    </div>
</form>
<!-- End Blog Search Form -->

<?php if (!empty($search_term)): ?>
    <h2 class="text-xl mb-6 text-gray-300">
        Resultados para: <span class="font-semibold text-white">"<?php echo htmlspecialchars($search_term); ?>"</span> (<?php echo $total_posts; ?> encontrado<?php echo $total_posts !== 1 ? 's' : ''; ?>)
    </h2>
<?php endif; ?>

<?php if (empty($blog_posts)): ?>
    <?php if (!empty($search_term)): ?>
        <p class="text-gray-400">Nenhum post encontrado para o termo pesquisado.</p>
    <?php else: ?>
        <p class="text-gray-400">Digite um termo para pesquisar no blog.</p>
    <?php endif; ?>
<?php else: ?>
    <!-- Masonry Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="blog-masonry-grid">
        <?php foreach ($blog_posts as $post):
            $post_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog_post&slug=' . $post['slug']);
            $image_full_path = !empty($post['image_path']) ? PROJECT_ROOT . '/' . $post['image_path'] : null;
            $image_url = ($image_full_path && file_exists($image_full_path))
                         ? BASE_URL . '/' . $post['image_path'] . '?' . filemtime($image_full_path) 
                         : null;

            
             if (!$image_url && $post['post_type'] === 'link') {
                  $placeholder_icon = '<i class="ri-link ri-4x text-gray-500"></i>';
             } else {
                 $placeholder_icon = null;
             }
        ?>
             <div class="bg-gray-800 rounded-lg overflow-hidden shadow-lg flex flex-col transition duration-300 hover:shadow-primary/50">
                 <?php if ($post['post_type'] === 'link'): ?>
                     <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer" class="block">
                         <div class="relative">
                             <?php if ($image_url): ?>
                                 <img src="<?php echo htmlspecialchars($image_url); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="w-full h-48 object-cover blog-post-image rounded-t-lg">
                                 <?php if (!empty($post['image_description'])): ?>
                                     <div class="absolute bottom-0 right-0 bg-black bg-opacity-60 text-white text-xs italic p-1 rounded-tl-md">
                                         <?php echo htmlspecialchars($post['image_description']); ?>
                                     </div>
                                 <?php endif; ?>
                             <?php elseif ($placeholder_icon): ?>
                                 <div class="w-full h-48 flex items-center justify-center bg-gray-700 rounded-t-lg">
                                     <?php echo $placeholder_icon; ?>
                                 </div>
                             <?php else: ?>
                                 <div class="w-full h-48 bg-gray-700 rounded-t-lg"></div>
                             <?php endif; ?>
                         </div>
                     </a>
                 <?php else: ?>
                     <a href="<?php echo $post_url; ?>" class="block">
                         <div class="relative">
                             <?php if ($image_url): ?>
                                 <img src="<?php echo htmlspecialchars($image_url); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="w-full h-48 object-cover blog-post-image rounded-t-lg">
                                 <?php if (!empty($post['image_description'])): ?>
                                     <div class="absolute bottom-0 right-0 bg-black bg-opacity-60 text-white text-xs italic p-1 rounded-tl-md">
                                         <?php echo htmlspecialchars($post['image_description']); ?>
                                     </div>
                                 <?php endif; ?>
                             <?php else: ?>
                                 <div class="w-full h-48 bg-gray-700 rounded-t-lg"></div>
                             <?php endif; ?>
                         </div>
                     </a>
                 <?php endif; ?>

                 <div class="p-4 flex flex-col flex-grow">
                     <h2 class="text-xl font-semibold mb-2 text-white">
                          <?php if ($post['post_type'] === 'link'): ?>
                              <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer" class="hover:text-primary transition">
                                  <?php echo htmlspecialchars($post['title']); ?> <i class="ri-external-link-line text-sm align-middle"></i>
                              </a>
                          <?php else: ?>
                              <a href="<?php echo $post_url; ?>" class="hover:text-primary transition">
                                  <?php echo htmlspecialchars($post['title']); ?>
                              </a>
                          <?php endif; ?>
                     </h2>
                     <p class="text-sm text-gray-400 mb-3">
                         Publicado em <?php echo format_date($post['published_at']); ?>
                         <?php if (!empty($post['categories'])):
                             $cat_links = array_map(function($cat) {
                                 $cat_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog&category=' . $cat['slug']);
                                 return '<a href="' . $cat_url . '" class="hover:text-primary">' . htmlspecialchars($cat['name']) . '</a>';
                             }, $post['categories']);
                             echo ' em ' . implode(', ', $cat_links);
                         endif; ?>
                     </p>
                      <?php if ($post['post_type'] === 'link' && !empty($post['link_description'])): ?>
                          <p class="text-gray-300 text-sm mb-4 flex-grow"><?php echo nl2br(htmlspecialchars($post['link_description'])); ?></p>
                      <?php elseif ($post['post_type'] === 'article'): ?>
                          <p class="text-gray-300 text-sm mb-4 flex-grow">
                              <?php echo htmlspecialchars(limit_words(strip_tags($post['content']), 25)); ?>...
                          </p>
                           <a href="<?php echo $post_url; ?>" class="text-primary hover:underline text-sm mt-auto">Ler Mais</a>
                      <?php else: ?>
                           <div class="flex-grow"></div> <!-- Ensure footer aligns -->
                           <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline text-sm mt-auto">Visitar Link <i class="ri-external-link-line text-xs align-middle"></i></a>
                      <?php endif; ?>
                  </div>
             </div>
        <?php endforeach; ?>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1):
        
        $base_pagination_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog_search&q=' . urlencode($search_term));
    ?>
        <nav aria-label="Page navigation" class="mt-8">
             <ul class="flex justify-center items-center space-x-1">
                 <!-- Previous Button -->
                 <li>
                     <a href="<?php echo $current_page > 1 ? $base_pagination_url . '&p=' . ($current_page - 1) : '#'; ?>"
                        class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 rounded-l-lg hover:bg-gray-700 hover:text-white <?php echo $current_page <= 1 ? 'opacity-50 cursor-not-allowed' : ''; ?>">
                         <span class="sr-only">Anterior</span>
                         <i class="ri-arrow-left-s-line"></i>
                     </a>
                 </li>

                 <?php
                 
                 $start_page = max(1, $current_page - 2);
                 $end_page = min($total_pages, $current_page + 2);

                 if ($start_page > 1) {
                     echo '<li><a href="' . $base_pagination_url . '&p=1' .'" class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white">1</a></li>';
                     if ($start_page > 2) {
                          echo '<li><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700">...</span></li>';
                     }
                 }

                 for ($i = $start_page; $i <= $end_page; $i++): ?>
                     <li>
                         <a href="<?php echo $base_pagination_url . '&p=' . $i; ?>"
                            class="px-3 py-2 leading-tight border border-gray-700 <?php echo ($i == $current_page) ? 'text-white bg-primary border-primary' : 'text-gray-400 bg-gray-800 hover:bg-gray-700 hover:text-white'; ?>">
                            <?php echo $i; ?></a>
                     </li>
                 <?php endfor;

                 if ($end_page < $total_pages) {
                     if ($end_page < $total_pages - 1) {
                          echo '<li><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700">...</span></li>';
                     }
                     echo '<li><a href="' . $base_pagination_url . '&p=' . $total_pages .'" class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white">' . $total_pages . '</a></li>';
                 }
                 ?>

                  <!-- Next Button -->
                 <li>
                     <a href="<?php echo $current_page < $total_pages ? $base_pagination_url . '&p=' . ($current_page + 1) : '#'; ?>"
                        class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 rounded-r-lg hover:bg-gray-700 hover:text-white <?php echo $current_page >= $total_pages ? 'opacity-50 cursor-not-allowed' : ''; ?>">
                        <span class="sr-only">Próximo</span>
                        <i class="ri-arrow-right-s-line"></i>
                     </a>
                 </li>
             </ul>
         </nav>
    <?php endif; ?>

<?php endif; ?>
<?php

require_once __DIR__ . '/../db.php';

function migrate_add_banner_types() {
    $pdo = get_db_connection();
    if (!$pdo) {
        throw new Exception('Database connection failed');
    }
    
    try {
        $pdo->beginTransaction();
        
        
        $sql_create_new = "
            CREATE TABLE banners_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                image_filename TEXT NOT NULL,
                link_type TEXT NOT NULL CHECK(link_type IN ('internal', 'external', 'none')),
                link_value TEXT,
                link_target TEXT NOT NULL CHECK(link_target IN ('_blank', '_self')) DEFAULT '_self',
                banner_type TEXT NOT NULL CHECK(banner_type IN ('homepage_wide', 'product_small', 'homepage_content', 'footer')),
                is_active INTEGER NOT NULL DEFAULT 1,
                visitor_count INTEGER NOT NULL DEFAULT 0,
                display_order INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            )
        ";
        $pdo->exec($sql_create_new);
        
        
        $sql_copy = "
            INSERT INTO banners_new 
            SELECT * FROM banners
        ";
        $pdo->exec($sql_copy);
        
        
        $pdo->exec('DROP TABLE banners');
        
        
        $pdo->exec('ALTER TABLE banners_new RENAME TO banners');
        
        $pdo->commit();
        
        echo "Migration completed successfully: Added 'homepage_content' and 'footer' banner types\n";
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('Migration failed: ' . $e->getMessage());
    }
}

if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        migrate_add_banner_types();
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
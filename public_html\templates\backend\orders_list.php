<?php

$currency_symbol = get_setting('currency_symbol', '€');

require_once __DIR__ . '/../../includes/order_functions.php';
require_once __DIR__ . '/../../includes/order_statuses.php';

$new_orders = get_orders_by_status('pending');
$processing_orders = get_orders_by_status('processing');
$completed_orders = get_orders_by_status('completed');
$shipped_orders = get_orders_by_status('shipped');
$cancelled_orders = get_orders_by_status('cancelled');
$refunded_orders = get_orders_by_status('refunded');

$orders_by_status = [
    'new' => $new_orders,
    'processing' => array_merge($processing_orders, $shipped_orders),
    'completed' => $completed_orders,
    'cancelled' => array_merge($cancelled_orders, $refunded_orders)
];

$active_tab = isset($_GET['tab']) ? sanitize_input($_GET['tab']) : 'new';
if (!array_key_exists($active_tab, $orders_by_status)) {
    $active_tab = 'new';
}
?>

<h1>Gerir Encomendas</h1>

<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <!-- Security Tools -->
        <button id="migrate-tokens-btn" class="btn btn-outline-primary btn-sm">
            <i class="bi bi-shield-lock"></i> Migrar Tokens de Acesso
        </button>
    </div>
</div>

<!-- Migration Modal -->
<div class="modal fade" id="migrationResultModal" tabindex="-1" aria-labelledby="migrationResultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="migrationResultModalLabel">Resultado da Migração</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="migrationResultContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">A processar...</span>
                    </div>
                    <p>A migrar tokens de acesso...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const migrateTokensBtn = document.getElementById('migrate-tokens-btn');
    const migrationModal = new bootstrap.Modal(document.getElementById('migrationResultModal'));
    const migrationResultContent = document.getElementById('migrationResultContent');

    migrateTokensBtn.addEventListener('click', function() {
        // Show the modal with loading state
        migrationResultContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">A processar...</span>
                </div>
                <p>A migrar tokens de acesso...</p>
            </div>
        `;
        migrationModal.show();

        // Get CSRF token
        const csrfToken = document.querySelector('input[name="csrf_token"]')?.value;

        // Make the AJAX request
        fetch('admin.php?section=orders&action=migrate_tokens&' + getSessionIdParam(), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'csrf_token=' + csrfToken
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                migrationResultContent.innerHTML = `
                    <div class="alert alert-success">
                        <h5>Migração concluída com sucesso!</h5>
                        <p>Total de tokens: ${data.total}</p>
                        <p>Tokens migrados: ${data.migrated}</p>
                        <p>Tokens já migrados: ${data.already_migrated}</p>
                        <p>Erros: ${data.errors}</p>
                    </div>
                `;
            } else {
                migrationResultContent.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Erro na migração</h5>
                        <p>${data.message || 'Ocorreu um erro durante a migração dos tokens.'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            migrationResultContent.innerHTML = `
                <div class="alert alert-danger">
                    <h5>Erro na migração</h5>
                    <p>Ocorreu um erro durante a migração dos tokens: ${error.message}</p>
                </div>
            `;
        });
    });

    // Helper function to get session ID parameter
    function getSessionIdParam() {
        // Try to extract from URL
        const urlParams = new URLSearchParams(window.location.search);
        const sidParam = urlParams.get('sid');
        if (sidParam) {
            return 'sid=' + sidParam;
        }
        return '';
    }
});
</script>

<!-- Order Status Tabs -->
<ul class="nav nav-tabs mb-3">
    <li class="nav-item">
        <a class="nav-link <?= $active_tab === 'new' ? 'active' : '' ?>" href="admin.php?section=orders&tab=new&<?= get_session_id_param() ?>">
            Novas Encomendas
            <?php if (count($orders_by_status['new']) > 0): ?>
                <span class="badge bg-danger"><?= count($orders_by_status['new']) ?></span>
            <?php endif; ?>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?= $active_tab === 'processing' ? 'active' : '' ?>" href="admin.php?section=orders&tab=processing&<?= get_session_id_param() ?>">
            Em Processamento
            <?php if (count($orders_by_status['processing']) > 0): ?>
                <span class="badge bg-info text-dark"><?= count($orders_by_status['processing']) ?></span>
            <?php endif; ?>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?= $active_tab === 'completed' ? 'active' : '' ?>" href="admin.php?section=orders&tab=completed&<?= get_session_id_param() ?>">
            Concluídas
            <?php if (count($orders_by_status['completed']) > 0): ?>
                <span class="badge bg-success"><?= count($orders_by_status['completed']) ?></span>
            <?php endif; ?>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?= $active_tab === 'cancelled' ? 'active' : '' ?>" href="admin.php?section=orders&tab=cancelled&<?= get_session_id_param() ?>">
            Canceladas/Reembolsadas
            <?php if (count($orders_by_status['cancelled']) > 0): ?>
                <span class="badge bg-secondary"><?= count($orders_by_status['cancelled']) ?></span>
            <?php endif; ?>
        </a>
    </li>
</ul>

<!-- Orders listing table -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">
            <?php
            switch ($active_tab) {
                case 'new': echo 'Novas Encomendas'; break;
                case 'processing': echo 'Encomendas em Processamento'; break;
                case 'completed': echo 'Encomendas Concluídas'; break;
                case 'cancelled': echo 'Encomendas Canceladas/Reembolsadas'; break;
                default: echo 'Lista de Encomendas';
            }
            ?>
        </h5>

        <?php $current_orders = $orders_by_status[$active_tab] ?? []; ?>

        <?php if (!empty($current_orders)): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Ref.</th>
                            <th>Data</th>
                            <th>Cliente</th>
                            <th>Método de Pagamento</th>
                            <th>Total</th>
                            <th>Estado</th>
                            <th class="text-end">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($current_orders as $order):
                            
                            $customer_info = json_decode($order['customer_info_json'] ?? '{}', true);
                            $customer_name = $customer_info['customer_name'] ?? 'N/D';
                            $is_anonymized = isset($customer_info['anonymized']) && $customer_info['anonymized'];
                        ?>
                            <tr>
                                <td><?= sanitize_input($order['id']) ?></td>
                                <td><?= sanitize_input($order['order_ref']) ?></td>
                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                <td>
                                    <?php if ($is_anonymized): ?>
                                        <span class="badge bg-secondary">Anonimizado</span>
                                    <?php else: ?>
                                        <?= sanitize_input($customer_name) ?>
                                    <?php endif; ?>
                                </td>
                                <td><?= sanitize_input($order['payment_method']) ?></td>
                                <td><?= format_price((float)$order['total_amount'], $currency_symbol) ?></td>
                                <td>
                                    <?php
                                    $status = sanitize_input($order['status']);
                                    $badge_class = get_status_badge_class($status);
                                    $status_name = get_status_name_pt($status);
                                    ?>
                                    <span class="badge <?= $badge_class ?>"><?= $status_name ?></span>
                                </td>
                                <td class="text-end">
                                    <a href="admin.php?section=orders&action=detail&id=<?= (int)$order['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-info" title="Ver Detalhes">
                                        <i class="bi bi-eye"></i> Ver
                                    </a>
                                    <form method="POST" action="admin.php?section=orders&<?= get_session_id_param() ?>" class="d-inline delete-order-form" onsubmit="return confirm('Tem a certeza que deseja eliminar a encomenda #<?= sanitize_input($order['order_ref']) ?>? Esta ação não pode ser desfeita.');">
                                        <?= csrf_input_field() ?>
                                        <input type="hidden" name="delete_order" value="1">
                                        <input type="hidden" name="id" value="<?= (int)$order['id'] ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" title="Eliminar Encomenda">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info" role="alert">
                Nenhuma encomenda encontrada nesta categoria.
            </div>
        <?php endif; ?>
    </div>
</div>


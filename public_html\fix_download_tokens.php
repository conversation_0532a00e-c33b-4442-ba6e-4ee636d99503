<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/order_functions.php';

function fix_duplicate_tokens() {
    $pdo = get_db_connection();
    if (!$pdo) {
        echo "Error: Could not connect to database\n";
        return false;
    }

    try {
        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            echo "Table order_access_tokens does not exist\n";
            return true;
        }

        
        $sql = "SELECT order_id, COUNT(*) as count FROM order_access_tokens GROUP BY order_id HAVING count > 1";
        $stmt = $pdo->query($sql);
        $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($duplicates)) {
            echo "No duplicate tokens found\n";
            return true;
        }

        echo "Found " . count($duplicates) . " orders with duplicate tokens\n";

        foreach ($duplicates as $duplicate) {
            $order_id = $duplicate['order_id'];
            echo "Fixing duplicates for order ID: $order_id\n";

            
            $sql = "DELETE FROM order_access_tokens 
                    WHERE order_id = :order_id 
                    AND id NOT IN (
                        SELECT id FROM order_access_tokens 
                        WHERE order_id = :order_id 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    )";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([':order_id' => $order_id]);
            
            echo "Removed " . $stmt->rowCount() . " duplicate tokens for order $order_id\n";
        }

        return true;
    } catch (Exception $e) {
        echo "Error fixing duplicates: " . $e->getMessage() . "\n";
        return false;
    }
}

function create_improved_order_access_token(int $order_id): string|false {
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        
        $pdo->beginTransaction();

        
        $order_check = $pdo->prepare("SELECT id FROM orders WHERE id = :order_id");
        $order_check->execute([':order_id' => $order_id]);
        if (!$order_check->fetch()) {
            $pdo->rollBack();
            return false;
        }

        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            $create_table_sql = "CREATE TABLE order_access_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                access_token TEXT NOT NULL,
                created_at TEXT NOT NULL,
                expires_at TEXT NOT NULL,
                UNIQUE(order_id, access_token)
            )";
            $pdo->exec($create_table_sql);

            $pdo->exec("CREATE INDEX idx_order_access_tokens_order_id ON order_access_tokens(order_id)");
            $pdo->exec("CREATE INDEX idx_order_access_tokens_access_token ON order_access_tokens(access_token)");
        }

        
        $access_token = generate_order_access_token($order_id);
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));

        
        $delete_sql = "DELETE FROM order_access_tokens WHERE order_id = :order_id";
        $delete_stmt = $pdo->prepare($delete_sql);
        $delete_stmt->execute([':order_id' => $order_id]);

        
        $insert_sql = "INSERT INTO order_access_tokens (
                    order_id, access_token, created_at, expires_at
                ) VALUES (
                    :order_id, :access_token, datetime('now', 'localtime'), :expires_at
                )";

        $insert_stmt = $pdo->prepare($insert_sql);
        $insert_stmt->execute([
            ':order_id' => $order_id,
            ':access_token' => $access_token,
            ':expires_at' => $expires_at
        ]);

        
        $pdo->commit();
        
        return $access_token;
    } catch (Exception $e) {
        
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function test_token_regeneration() {
    echo "Testing token regeneration...\n";
    
    
    $pdo = get_db_connection();
    if (!$pdo) {
        echo "Error: Could not connect to database\n";
        return false;
    }

    $stmt = $pdo->query("SELECT id FROM orders LIMIT 1");
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        echo "No orders found in database\n";
        return false;
    }

    $order_id = $order['id'];
    echo "Testing with order ID: $order_id\n";

    
    $token = create_improved_order_access_token($order_id);
    
    if ($token) {
        echo "✓ Token generated successfully: $token\n";
        
        
        $verify_sql = "SELECT * FROM order_access_tokens WHERE order_id = :order_id";
        $verify_stmt = $pdo->prepare($verify_sql);
        $verify_stmt->execute([':order_id' => $order_id]);
        $tokens = $verify_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✓ Found " . count($tokens) . " token(s) in database\n";
        
        if (count($tokens) === 1) {
            echo "✓ No duplicates found\n";
            return true;
        } else {
            echo "✗ Unexpected number of tokens\n";
            return false;
        }
    } else {
        echo "✗ Token generation failed\n";
        return false;
    }
}

function main() {
    echo "=== Download Token Fix Script ===\n\n";
    
    echo "Step 1: Fixing duplicate tokens...\n";
    if (fix_duplicate_tokens()) {
        echo "✓ Duplicate tokens fixed\n\n";
    } else {
        echo "✗ Failed to fix duplicate tokens\n\n";
        return;
    }
    
    echo "Step 2: Testing token regeneration...\n";
    if (test_token_regeneration()) {
        echo "✓ Token regeneration test passed\n\n";
    } else {
        echo "✗ Token regeneration test failed\n\n";
        return;
    }
    
    echo "=== Fix completed successfully ===\n";
    echo "\nThe token regeneration issue should now be resolved.\n";
    echo "You can test it in the admin panel by regenerating a token for any order.\n";
}

if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    main();
}
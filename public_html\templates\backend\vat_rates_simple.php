<?php

if (!defined('ADMIN_CONTEXT') || !ADMIN_CONTEXT) {
    die('Access Denied');
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestão de Taxas de IVA</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="admin.php?<?= get_session_id_param() ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Taxas de IVA</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div><i class="fas fa-percentage me-1"></i> Gestão de Taxas de IVA</div>
        </div>
        <div class="card-body">
            <p class="mb-3">Configure as diferentes taxas de IVA disponíveis para seleção nos produtos.</p>
            
            <!-- Add New VAT Rate Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-plus me-1"></i> Adicionar Nova Taxa de IVA
                </div>
                <div class="card-body">
                    <form id="add-vat-form" method="post" action="admin.php?section=vat_rates&action=add&<?= get_session_id_param() ?>">
                        <?= csrf_input_field() ?>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="rate" class="form-label">Taxa (%)</label>
                                <input type="number" class="form-control" id="rate" name="rate" min="0" max="100" step="0.1" required>
                            </div>
                            <div class="col-md-6">
                                <label for="description" class="form-label">Descrição</label>
                                <input type="text" class="form-control" id="description" name="description" required>
                                <div class="form-text">Ex: "IVA Normal (23%)", "IVA Reduzido (6%)", "Isento de IVA (0%)"</div>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <div class="form-check mb-2 me-3">
                                    <input type="checkbox" class="form-check-input" id="is_default" name="is_default" value="1">
                                    <label class="form-check-label" for="is_default">Definir como padrão</label>
                                </div>
                                <button type="submit" class="btn btn-primary">Adicionar</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- VAT Rates Table -->
            <?php if (empty($vat_rates)): ?>
                <div class="alert alert-info">
                    Não existem taxas de IVA definidas. Use o formulário acima para adicionar.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Taxa (%)</th>
                                <th>Descrição</th>
                                <th>Padrão</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($vat_rates as $rate): ?>
                                <tr id="vat-rate-row-<?= $rate['id'] ?>">
                                    <td>
                                        <span class="rate-display"><?= number_format($rate['rate'], 1, ',', '.') ?>%</span>
                                        <input type="number" class="form-control rate-edit" style="display:none;" 
                                               value="<?= $rate['rate'] ?>" min="0" max="100" step="0.1">
                                    </td>
                                    <td>
                                        <span class="description-display"><?= htmlspecialchars($rate['description']) ?></span>
                                        <input type="text" class="form-control description-edit" style="display:none;" 
                                               value="<?= htmlspecialchars($rate['description']) ?>">
                                    </td>
                                    <td>
                                        <?php if ($rate['is_default']): ?>
                                            <span class="badge bg-success">Sim</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Não</span>
                                            <form method="post" action="admin.php?section=vat_rates&action=set_default&<?= get_session_id_param() ?>" 
                                                  class="d-inline set-default-form">
                                                <?= csrf_input_field() ?>
                                                <input type="hidden" name="id" value="<?= $rate['id'] ?>">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary btn-edit" data-id="<?= $rate['id'] ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-success btn-save" data-id="<?= $rate['id'] ?>" style="display:none;">
                                                <i class="fas fa-save"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-secondary btn-cancel" data-id="<?= $rate['id'] ?>" style="display:none;">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <?php if (!$rate['is_default']): ?>
                                                <form method="post" action="admin.php?section=vat_rates&action=delete&<?= get_session_id_param() ?>" 
                                                      class="d-inline delete-form" onsubmit="return confirm('Tem certeza que deseja eliminar esta taxa de IVA?');">
                                                    <?= csrf_input_field() ?>
                                                    <input type="hidden" name="id" value="<?= $rate['id'] ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit button click handler
    document.querySelectorAll('.btn-edit').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const row = document.getElementById('vat-rate-row-' + id);
            
            // Hide display elements, show edit elements
            row.querySelector('.rate-display').style.display = 'none';
            row.querySelector('.rate-edit').style.display = 'block';
            
            row.querySelector('.description-display').style.display = 'none';
            row.querySelector('.description-edit').style.display = 'block';
            
            // Hide edit button, show save and cancel buttons
            this.style.display = 'none';
            row.querySelector('.btn-save').style.display = 'inline-block';
            row.querySelector('.btn-cancel').style.display = 'inline-block';
        });
    });
    
    // Cancel button click handler
    document.querySelectorAll('.btn-cancel').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const row = document.getElementById('vat-rate-row-' + id);
            
            // Show display elements, hide edit elements
            row.querySelector('.rate-display').style.display = 'inline';
            row.querySelector('.rate-edit').style.display = 'none';
            
            row.querySelector('.description-display').style.display = 'inline';
            row.querySelector('.description-edit').style.display = 'none';
            
            // Show edit button, hide save and cancel buttons
            row.querySelector('.btn-edit').style.display = 'inline-block';
            row.querySelector('.btn-save').style.display = 'none';
            this.style.display = 'none';
        });
    });
    
    // Save button click handler
    document.querySelectorAll('.btn-save').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const row = document.getElementById('vat-rate-row-' + id);
            
            const rate = row.querySelector('.rate-edit').value;
            const description = row.querySelector('.description-edit').value;
            
            // Validate inputs
            if (!rate || rate < 0 || rate > 100) {
                alert('Taxa de IVA inválida. Deve ser um número entre 0 e 100.');
                return;
            }
            
            if (!description) {
                alert('Descrição da taxa de IVA é obrigatória.');
                return;
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('id', id);
            formData.append('rate', rate);
            formData.append('description', description);
            formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
            
            // Send AJAX request
            fetch('admin.php?section=vat_rates&action=update&<?= get_session_id_param() ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update display elements
                    row.querySelector('.rate-display').textContent = parseFloat(rate).toFixed(1).replace('.', ',') + '%';
                    row.querySelector('.description-display').textContent = description;
                    
                    // Show display elements, hide edit elements
                    row.querySelector('.rate-display').style.display = 'inline';
                    row.querySelector('.rate-edit').style.display = 'none';
                    
                    row.querySelector('.description-display').style.display = 'inline';
                    row.querySelector('.description-edit').style.display = 'none';
                    
                    // Show edit button, hide save and cancel buttons
                    row.querySelector('.btn-edit').style.display = 'inline-block';
                    row.querySelector('.btn-save').style.display = 'none';
                    row.querySelector('.btn-cancel').style.display = 'none';
                    
                    // Show success message
                    if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                        AdminUtils.showNotification(data.message || 'Taxa de IVA atualizada com sucesso.', 'success');
                    } else {
                        alert(data.message || 'Taxa de IVA atualizada com sucesso.');
                    }
                } else {
                    // Show error message
                    if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                        AdminUtils.showNotification(data.message || 'Erro ao atualizar taxa de IVA.', 'danger');
                    } else {
                        alert(data.message || 'Erro ao atualizar taxa de IVA.');
                    }
                }
            })
            .catch(error => {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification('Erro ao processar o pedido.', 'danger');
                } else {
                    alert('Erro ao processar o pedido.');
                }
            });
        });
    });
    
    // Set default form submission
    document.querySelectorAll('.set-default-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show updated data
                    window.location.reload();
                } else {
                    // Show error message
                    if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                        AdminUtils.showNotification(data.message || 'Erro ao definir taxa como padrão.', 'danger');
                    } else {
                        alert(data.message || 'Erro ao definir taxa como padrão.');
                    }
                }
            })
            .catch(error => {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification('Erro ao processar o pedido.', 'danger');
                } else {
                    alert('Erro ao processar o pedido.');
                }
            });
        });
    });
    
    // Delete form submission
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (confirm('Tem certeza que deseja eliminar esta taxa de IVA?')) {
                const formData = new FormData(this);
                
                fetch(this.action, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the row from the table
                        const id = formData.get('id');
                        const row = document.getElementById('vat-rate-row-' + id);
                        row.remove();
                        
                        // Show success message
                        if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                            AdminUtils.showNotification(data.message || 'Taxa de IVA eliminada com sucesso.', 'success');
                        } else {
                            alert(data.message || 'Taxa de IVA eliminada com sucesso.');
                        }
                    } else {
                        // Show error message
                        if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                            AdminUtils.showNotification(data.message || 'Erro ao eliminar taxa de IVA.', 'danger');
                        } else {
                            alert(data.message || 'Erro ao eliminar taxa de IVA.');
                        }
                    }
                })
                .catch(error => {
                    if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                        AdminUtils.showNotification('Erro ao processar o pedido.', 'danger');
                    } else {
                        alert('Erro ao processar o pedido.');
                    }
                });
            }
        });
    });
    
    // Add VAT form submission
    document.getElementById('add-vat-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the page to show updated data
                window.location.reload();
            } else {
                // Show error message
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Erro ao adicionar taxa de IVA.', 'danger');
                } else {
                    alert(data.message || 'Erro ao adicionar taxa de IVA.');
                }
            }
        })
        .catch(error => {
            if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                AdminUtils.showNotification('Erro ao processar o pedido.', 'danger');
            } else {
                alert('Erro ao processar o pedido.');
            }
        });
    });
});
</script>

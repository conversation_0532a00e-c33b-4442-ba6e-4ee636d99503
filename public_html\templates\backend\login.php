<?php

?>

<div class="row justify-content-center align-items-center" style="min-height: 80vh;">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-body">
                <h3 class="card-title text-center mb-4">Admin Login</h3>

                <?php if (!empty($login_error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?= sanitize_input($login_error) ?>
                    </div>
                <?php endif; ?>

                <?php
                
                $stored_hash = get_setting('admin_password_hash');
                if (empty($stored_hash) && empty($login_error)) {
                    echo '<div class="alert alert-warning">Nenhuma password de administrador definida. Introduza uma nova password para configurar o acesso.</div>';
                }
                ?>

                <form method="POST" action="admin.php?<?= get_session_id_param() ?>">
                    <?= csrf_input_field() ?> <!-- Add CSRF token -->
                    <input type="hidden" name="login" value="1"> <!-- Identifier for login action -->

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Entrar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

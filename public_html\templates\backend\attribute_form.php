<?php

$is_editing = ($action === 'edit' && isset($item_id));
$form_title = $is_editing ? "Editar Atributo" : "Adicionar Novo Atributo";
$attribute_data = null;

if ($is_editing) {
    
    $attribute_data = db_query("SELECT * FROM attributes WHERE id = :id", [':id' => $item_id], true);
    if (!$attribute_data) {
        echo '<div class="alert alert-danger">Atributo não encontrado.</div>';
        
        echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary">Voltar à Lista</a>';
        return; 
    }
    $form_title .= " (#" . $attribute_data['id'] . ")";
}

$form_data = $_SESSION['form_data'] ?? [];
unset($_SESSION['form_data']); 

?>

<h1><?= $form_title ?></h1>
<hr>

<?php display_flash_messages(); ?>

<form method="POST" action="admin.php?section=attributes&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>">
    <?= csrf_input_field() ?>

    <div class="card mb-4">
        <div class="card-header">Detalhes do Atributo</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="name_pt" class="form-label">Nome do Atributo (PT) *</label>
                <input type="text" class="form-control" id="name_pt" name="name_pt" value="<?= sanitize_input($form_data['name_pt'] ?? $attribute_data['name_pt'] ?? '') ?>" required>
                 <div class="form-text">Exemplo: Cor, Tamanho, Material</div>
            </div>

            <!-- Add fields for other languages if needed later -->

        </div>
    </div>

    <div class="mt-4">
        <button type="submit" class="btn btn-primary">Guardar Atributo</button>
        <a href="admin.php?section=attributes&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
    </div>

</form>

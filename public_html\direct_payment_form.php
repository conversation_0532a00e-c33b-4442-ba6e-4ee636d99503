<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/payment_methods.php';

ensure_payment_methods_table_exists();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    $title = trim($_POST['title'] ?? '');
    $instructions = trim($_POST['instructions'] ?? '');
    $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1;
    $sort_order = isset($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0;
    
    
    $errors = [];
    if (empty($title)) $errors[] = "Title is required.";
    if (empty($instructions)) $errors[] = "Instructions are required.";
    
    if (!empty($errors)) {
        echo "Validation errors:<br>";
        foreach ($errors as $error) {
            echo "- $error<br>";
        }
    } else {
        
        $payment_data = [
            'title' => $title,
            'instructions' => $instructions,
            'is_active' => $is_active,
            'sort_order' => $sort_order
        ];
        
        
        $new_id = add_payment_method($payment_data);
        if ($new_id) {
            echo "Payment method added successfully with ID: $new_id";
        } else {
            echo "Failed to add payment method.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Direct Payment Method Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"], textarea { width: 300px; padding: 5px; }
        textarea { height: 100px; }
        button { padding: 10px 15px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Direct Payment Method Form</h1>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="title">Title:</label>
            <input type="text" id="title" name="title" required>
        </div>
        
        <div class="form-group">
            <label for="instructions">Instructions:</label>
            <textarea id="instructions" name="instructions" required></textarea>
        </div>
        
        <div class="form-group">
            <label for="is_active">Status:</label>
            <select id="is_active" name="is_active">
                <option value="1">Active</option>
                <option value="0">Inactive</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="sort_order">Sort Order:</label>
            <input type="number" id="sort_order" name="sort_order" value="0">
        </div>
        
        <button type="submit">Submit</button>
    </form>
    
    <h2>Existing Payment Methods</h2>
    <?php
    $payment_methods = get_payment_methods();
    if (count($payment_methods) > 0) {
        echo "<ul>";
        foreach ($payment_methods as $method) {
            echo "<li>ID: {$method['id']}, Title: {$method['title']}, Active: {$method['is_active']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No payment methods found.</p>";
    }
    ?>
</body>
</html>

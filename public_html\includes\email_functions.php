<?php

if (!function_exists('send_email')) {
    require_once __DIR__ . '/functions.php';
}

if (!function_exists('send_email_wrapper')) {
    
    function send_email_wrapper(
        string $to_email,
        string $to_name,
        string $subject,
        string $body_html,
        string $body_plain = '',
        ?string $reply_to_email = null,
        ?string $reply_to_name = null,
        array $attachments = [] 
    ): bool {
        
        return send_email($to_email, $to_name, $subject, $body_html, $body_plain, $reply_to_email, $reply_to_name, $attachments); 
    }
}

if (!function_exists('send_digital_product_terms_email')) {
    function send_digital_product_terms_email(int $order_id): bool {
        if (get_setting('digital_terms_email_enabled', '0') !== '1') {
            return false; 
        }

        $order = get_order_by_id($order_id);
        if (!$order) {
            return false;
        }
        if (!$order['has_digital_products']) {
            return false;
        }

        $customer_info = json_decode($order['customer_info_json'] ?? '{}', true);
        $customer_email = $customer_info['customer_email'] ?? null;
        $customer_name = $customer_info['customer_name'] ?? 'Cliente';
        $customer_first_name = explode(' ', $customer_name)[0] ?? $customer_name;

        if (empty($customer_email)) {
            return false;
        }

        $terms_log_json = $order['terms_log_json'] ?? null;
        if (empty($terms_log_json)) {
            return false; 
        }

        $terms_log = json_decode($terms_log_json, true);
        $agreed_page_ids = $terms_log['agreed_pages'] ?? [];

        if (empty($agreed_page_ids)) {
            return false; 
        }

        
        $admin_selected_pages_json = get_setting('digital_terms_email_selected_pages', '[]');
        $admin_selected_page_ids = json_decode($admin_selected_pages_json, true);
        if (!is_array($admin_selected_page_ids)) {
            $admin_selected_page_ids = [];
        }
        
        $admin_selected_page_ids = array_map('intval', $admin_selected_page_ids);

        
        $final_page_ids_to_send = array_intersect($agreed_page_ids, $admin_selected_page_ids);

        if (empty($final_page_ids_to_send)) {
            return false; 
        }

        
        $email_attachments = [];
        $created_temp_files = [];

        foreach ($final_page_ids_to_send as $page_id) {
            $page_data = get_page_by_id((int)$page_id);
            if ($page_data && !empty($page_data['content_pt'])) {
                
                $content_html = $page_data['content_pt'] ?? '';
                $text = $content_html;

                
                $text = preg_replace('/<br\s?\/?>/i', "\n", $text);
                $block_elements_double_nl = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div', 'blockquote', 'address', 'section', 'article', 'aside', 'header', 'footer', 'nav', 'main', 'figure', 'figcaption', 'hr', 'form', 'fieldset', 'pre'];
                foreach ($block_elements_double_nl as $tag) {
                    $text = preg_replace('/<' . $tag . '[^>]*>/i', "\n\n", $text);
                    $text = preg_replace('/<\/' . $tag . '>/i', "\n\n", $text);
                }
                $text = preg_replace('/<ul[^>]*>/i', "\n\n", $text);
                $text = preg_replace('/<\/ul>/i', "\n\n", $text);
                $text = preg_replace('/<ol[^>]*>/i', "\n\n", $text);
                $text = preg_replace('/<\/ol>/i', "\n\n", $text);
                $text = preg_replace('/<li[^>]*>/i', "\n* ", $text);
                $text = preg_replace('/<\/li>/i', "\n", $text);
                $text = preg_replace('/<table[^>]*>/i', "\n\n", $text);
                $text = preg_replace('/<\/table>/i', "\n\n", $text);
                $text = preg_replace('/<tr[^>]*>/i', "\n", $text);
                $text = preg_replace('/<\/tr>/i', "\n", $text);
                $text = preg_replace('/<(td|th)[^>]*>/i', "\t", $text);
                $text = preg_replace('/<\/(td|th)>/i', "", $text);
                $text = strip_tags($text);
                $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                $text = str_replace("\xC2\xA0", " ", $text);
                $text = preg_replace('/[ \t]+/', ' ', $text);
                $text = preg_replace('/^[ \t]*(.*?)[ \t]*$/m', '$1', $text);
                $text = preg_replace('/\n{3,}/s', "\n\n", $text);
                $text = trim($text);
                $trimmed_plain_text_content = $text;

                
                $sanitized_page_title = preg_replace('/[^a-zA-Z0-9_-]/', '_', $page_data['title_pt']);
                $filename = $sanitized_page_title . ".txt";
                
                $temp_file_path = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid('terms_') . '_' . $filename;
                
                if (file_put_contents($temp_file_path, $trimmed_plain_text_content) !== false) {
                    $email_attachments[] = ['path' => $temp_file_path, 'name' => $filename];
                    $created_temp_files[] = $temp_file_path;
                } else {
                }
                
            } else {
            }
        }

        if (empty($email_attachments)) {
            
            foreach ($created_temp_files as $file_path) {
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
            }
            return false;
        }

        $store_name = get_setting('store_name', 'A Nossa Loja');
        $email_subject_template = get_setting('digital_terms_email_subject', 'Termos e Condições da sua Encomenda Digital #{order_number}');
        
        $subject_replacements = [
            '{customer_first_name}' => $customer_first_name,
            '{customer_last_name}' => $customer_info['last_name'] ?? '',
            '{order_number}' => $order['order_ref'],
            '{store_name}' => $store_name
        ];
        $email_subject = str_replace(array_keys($subject_replacements), array_values($subject_replacements), $email_subject_template);

        $user_ip = $order['ip_address'] ?? 'N/A';
        $order_date_formatted = format_date($order['created_at'], 'd/m/Y \à\s H:i:s');

        $footer_note_plain = "\n\n------------------------------------------------------\n";
        $footer_note_plain .= "Os termos de uso, enviados em anexo, foram aceites e reconhecidos pelo utilizador " . sanitize_input($customer_name) . " ";
        $footer_note_plain .= "com email " . sanitize_input($customer_email) . " no dia " . $order_date_formatted . " ";
        $footer_note_plain .= "com IP: " . sanitize_input($user_ip) . ".";
        $footer_note_plain .= "\n------------------------------------------------------\n";

        $email_body_plain = "Termos e Condições Acordados\n";
        $email_body_plain .= "=========\n\n";
        $email_body_plain .= "Olá " . sanitize_input($customer_first_name) . ",\n";
        $email_body_plain .= "  conforme assinalado nos registos online, durante a finalização da sua encomenda (#" . sanitize_input($order['order_ref']) . ") esta incluia a intenção de compra relativa a ficheiros digitais de entrega imediata por meios imateriais. Verifica-se também que concordou com os termos e condições específicos a esse tipo de produtos (i.e., produtos digitais com entrega imaterial), cedendo portanto, o seu consentimento expresso para início imediato da execução com o respetivo reconhecimento da perda do direito de livre resolução. Estes termos e condições, para sua referência, encontram-se em anexo neste email.\n\n";
        $email_body_plain .= "  to este email apenas para lhe ceder uma cópia dos mesmos e com isto efetivar o reconhecimento destes em suporte duradouro, não requerendo qualquer resposta sua. Note que estes termos apenas se aplicam aos produtos digitais de entrega imaterial.\n\n";
        $email_body_plain .= "  Caso não concorde com os mesmos, por favor não finalize a sua encomenda ao proceder com o respectivo pagamento!\n";
        
        $email_body_plain .= $footer_note_plain;
        $email_body_plain .= "\nCom os melhores cumprimentos,\n" . $store_name . "\n";
        
        $simple_html_version = nl2br($email_body_plain);
        $email_sent_status = send_email_wrapper(
            $customer_email,
            $customer_name,
            $email_subject,
            $simple_html_version,
            $email_body_plain,
            null, 
            null, 
            $email_attachments 
        );

        
        foreach ($created_temp_files as $file_path) {
            if (file_exists($file_path)) {
                if (unlink($file_path)) {
                } else {
                }
            }
        }

        if (!$email_sent_status) {
        } else {
        }
        return $email_sent_status;
    }
}

<?php

?>

<h1><PERSON><PERSON><PERSON> Campos Personalizados</h1>
<a href="admin.php?section=custom_fields&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Criar Novo Campo Personalizado
</a>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Lista de Campos Personalizados</h5>
    </div>
    <div class="card-body">
        <?php if (empty($custom_fields)): ?>
            <div class="alert alert-info">
                Nenhum campo personalizado encontrado. Clique no botão acima para criar um novo.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome</th>
                            <th>Tipo</th>
                            <th>Modificador de Preço</th>
                            <th>Obrigatório</th>
                            <th>Estado</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($custom_fields as $field): ?>
                            <tr>
                                <td><?= $field['id'] ?></td>
                                <td><?= sanitize_input($field['name']) ?></td>
                                <td><?= sanitize_input($field['type_name']) ?></td>
                                <td><?= format_price($field['price_modifier']) ?></td>
                                <td>
                                    <?php if ($field['is_required']): ?>
                                        <span class="badge bg-success">Sim</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Não</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($field['is_active']): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="admin.php?section=custom_fields&action=edit&id=<?= $field['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-primary" title="Editar">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="admin.php?section=custom_fields&action=duplicate&id=<?= $field['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-info" title="Duplicar">
                                            <i class="bi bi-files"></i>
                                        </a>
                                        <a href="admin.php?section=custom_fields&action=delete&id=<?= $field['id'] ?>&<?= get_session_id_param() ?>"
                                           class="btn btn-danger delete-item-btn"
                                           onclick="return confirm('Tem a certeza que deseja eliminar o campo personalizado \'<?= sanitize_input($field['name']) ?>\'? Esta ação não pode ser desfeita.');"
                                           title="Eliminar">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>


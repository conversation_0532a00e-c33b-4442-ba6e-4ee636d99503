<?php

ob_start();

require_once __DIR__ . '/../../includes/payment_methods.php';
require_once __DIR__ . '/../../includes/vat_functions.php';
require_once __DIR__ . '/../../includes/custom_field_functions.php';
require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/coupon_functions.php';

$cart_items = $_SESSION['cart'] ?? [];

$has_digital_products = false;
$only_digital_products = true;
foreach ($cart_items as $item) {
    
    if (!isset($item['product_type'])) {
        $product_data = db_query("SELECT product_type FROM products WHERE id = ?", [$item['product_id']], true);
        $item['product_type'] = $product_data ? $product_data['product_type'] : 'regular';
    }

    if (isset($item['product_type']) && $item['product_type'] === 'digital') {
        $has_digital_products = true;
    } else {
        $only_digital_products = false;
    }
}
$only_digital_products = $has_digital_products && $only_digital_products;

$required_pages = get_required_agreement_pages($has_digital_products);

$payment_methods = get_payment_methods(true); 

$cart_totals = calculate_cart_totals();
$cart_subtotal = $cart_totals['subtotal'];
$tax_amount = $cart_totals['tax_amount'];
$shipping_cost = $cart_totals['shipping_cost'];
$discount_amount = $cart_totals['discount_amount'];
$grand_total = $cart_totals['grand_total'];
$total_items_count = $cart_totals['total_items'];
$vat_groups = $cart_totals['vat_groups'];
$min_order_value = $cart_totals['min_order_value'];
$free_shipping_threshold = $cart_totals['free_shipping_threshold'];
$meets_min_order = $cart_totals['meets_min_order'];
$has_free_shipping = $cart_totals['has_free_shipping'];

$currency_symbol = get_setting('currency_symbol', '€');

$cart_items_summary = [];

if ($min_order_value > 0 && !$meets_min_order) {
    
    $_SESSION['flash_message'] = [
        'type' => 'danger',
        'message' => "O valor mínimo para checkout é " . format_price($min_order_value, $currency_symbol) . ". Adicione mais produtos ao carrinho."
    ];

    
    echo '<script>window.location.href = "' . add_session_param_to_url(BASE_URL . '/index.php?view=cart') . '";</script>';
    exit;
}

foreach ($cart_items as $key => $item) {
    
    $image_sql = "SELECT filename FROM product_images WHERE product_id = ? AND (variation_id = ? OR variation_id IS NULL) ORDER BY is_default DESC, id ASC LIMIT 1";
    $image_result = db_query($image_sql, [$item['product_id'], $item['variation_id']], true);

    
    if ($image_result && !empty($image_result['filename'])) {
        $image_url = PRODUCT_IMAGES_URL . '/' . ltrim($image_result['filename'], '/');
    } else {
        
        $fallback_sql = "SELECT filename FROM product_images ORDER BY id ASC LIMIT 1";
        $fallback_result = db_query($fallback_sql, [], true);
        if ($fallback_result && !empty($fallback_result['filename'])) {
            $image_url = PRODUCT_IMAGES_URL . '/' . ltrim($fallback_result['filename'], '/');
        } else {
            
            $image_url = PRODUCT_IMAGES_URL . '/prod_1_1745335482_0_medalha_redonda_aco_com_patina_2020__2.jpg';
        }
    }

    
    $stock_check = db_query("SELECT stock FROM product_variations WHERE id = ?", [$item['variation_id']], true);
    $stock = $stock_check['stock'] ?? 99; 

    $cart_items_summary[$key] = [
        'name' => $item['name'],
        'quantity' => $item['quantity'],
        'price_formatted' => format_price($item['price']),
        'image_url' => $image_url,
        'stock' => $stock,
        'price' => $item['price'],
        'attributes_display' => $item['attributes_display'] ?? '',
        'custom_fields' => $item['custom_fields'] ?? []
    ];
}

$errors = [];
$form_data = $_SESSION['checkout_form_data'] ?? []; 
unset($_SESSION['checkout_form_data']); 

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    

    
    $form_data['email'] = filter_var(trim($_POST['customer_email'] ?? ''), FILTER_SANITIZE_EMAIL);
    $form_data['phone'] = sanitize_input($_POST['customer_phone'] ?? '');

    
    $vat_id = sanitize_input($_POST['customer_vat_id'] ?? '');
    
    $vat_id = preg_replace('/\D/', '', $vat_id);
    
    $form_data['vat_id'] = !empty($vat_id) ? 'PT' . $vat_id : '';

    $form_data['first_name'] = sanitize_input($_POST['shipping_first_name'] ?? '');
    $form_data['last_name'] = sanitize_input($_POST['shipping_last_name'] ?? '');
    $form_data['shipping_address'] = sanitize_input($_POST['shipping_address'] ?? '');
    $form_data['shipping_city'] = sanitize_input($_POST['shipping_city'] ?? '');
    $form_data['shipping_zip'] = sanitize_input($_POST['shipping_zip'] ?? '');
    $form_data['shipping_country'] = sanitize_input($_POST['shipping_country'] ?? 'PT');
    $form_data['payment_method'] = sanitize_input($_POST['payment_method'] ?? '');
    $form_data['notes'] = sanitize_input($_POST['order_notes'] ?? ''); 
    $form_data['agreements'] = $_POST['agreements'] ?? []; 

    
    foreach ($required_pages as $req_page) {
        $checkbox_name = 'agreement_' . $req_page['id'];
        
        if (!isset($form_data['agreements'][$req_page['id']]) || $form_data['agreements'][$req_page['id']] !== '1') {
            $errors[$checkbox_name] = "Tem de aceitar os termos da página \"" . htmlspecialchars($req_page['title']) . "\".";
        }
    }

    
    if (empty($form_data['email']) || !filter_var($form_data['email'], FILTER_VALIDATE_EMAIL)) $errors['customer_email'] = "O email é inválido.";
    
    if (empty($form_data['first_name'])) $errors['shipping_first_name'] = "O primeiro nome é obrigatório.";
    if (empty($form_data['last_name'])) $errors['shipping_last_name'] = "O apelido é obrigatório.";

    
    if (!$only_digital_products) {
        if (empty($form_data['shipping_address'])) $errors['shipping_address'] = "A morada de envio é obrigatória.";
        if (empty($form_data['shipping_city'])) $errors['shipping_city'] = "A localidade é obrigatória.";
        if (empty($form_data['shipping_zip'])) $errors['shipping_zip'] = "O código postal é obrigatório."; 
    }

    if (empty($form_data['payment_method'])) $errors['payment_method'] = "Selecione um método de pagamento.";

    
    $vat_id_required_enabled = get_setting('vat_id_required_enabled', '1') == '1';
    $vat_id_threshold = get_setting('vat_id_threshold', 1000);

    
    if ($vat_id_required_enabled && $grand_total >= $vat_id_threshold) {
        
        $vat_id_value = $form_data['vat_id'];
        if (empty($vat_id_value)) {
            $errors['customer_vat_id'] = "O NIF é obrigatório para encomendas acima de " . format_price($vat_id_threshold, $currency_symbol) . ".";
        } elseif (strlen($vat_id_value) !== 11 || !preg_match('/^PT\d{9}$/', $vat_id_value)) {
            $errors['customer_vat_id'] = "O NIF deve ter 9 dígitos com o prefixo PT.";
        }
    }

    
    if (empty($errors)) {
        
        $order_data = [
            'customer_name' => $form_data['first_name'] . ' ' . $form_data['last_name'],
            'customer_email' => $form_data['email'],
            'customer_phone' => $form_data['phone'],
            'customer_vat_id' => $form_data['vat_id'], 
            'shipping_address' => $form_data['shipping_address'],
            'shipping_city' => $form_data['shipping_city'],
            'shipping_zip' => $form_data['shipping_zip'],
            'shipping_country' => $form_data['shipping_country'],
            
            'payment_method' => $form_data['payment_method'],
            'order_status' => 'pending', 
            'subtotal' => $cart_subtotal,
            'tax_amount' => $tax_amount,
            'shipping_cost' => $shipping_cost,
            'discount_amount' => $discount_amount,
            'total_amount' => $grand_total,
            'order_notes' => $form_data['notes'],
            'session_id' => $current_session_id, 
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
             
            'terms_log' => json_encode([
                'timestamp' => date('Y-m-d H:i:s'), 
                'agreed_pages' => array_keys($form_data['agreements']),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? null
            ])
        ];

        $order_id = create_order($order_data, $cart_items); 

        if ($order_id) {
            
            
            $_SESSION['cart'] = [];
            unset($_SESSION['cart_discount']);
            unset($_SESSION['applied_promo_code']);
            unset($_SESSION['applied_coupon_id']);
            unset($_SESSION['checkout_form_data']); 

            
            echo '<script>
                if (typeof updateCartCounter === "function") {
                    updateCartCounter(0);
                }
                if (typeof updateCartBadge === "function") {
                    updateCartBadge(0);
                }
                // Store cart count in localStorage as a backup
                localStorage.setItem("eshop_cart_count", 0);

                // Force a direct AJAX call to clear the cart in the database
                // This ensures the cart is completely cleared
                fetch("' . add_session_param_to_url(BASE_URL . '/index.php?action=clear_cart') . '", {
                    method: "POST",
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        "Content-Type": "application/json",
                        "X-Session-UUID": window.eshopSessionId || ""
                    },
                    body: JSON.stringify({})
                }).then(response => {
                }).catch(error => {
                });
            </script>';

            
            session_write_close();

            
            require_once __DIR__ . '/../../includes/order_functions.php';
            send_order_confirmation_email($order_id);

            
            $access_token = get_order_access_token($order_id);

            
            $redirect_url = add_session_param_to_url(BASE_URL . '/index.php?view=order_success&token=' . $access_token);

            
            echo '<script>window.location.href = "' . $redirect_url . '";</script>';
            
            
            return;
        } else {
            
            $errors['form'] = "Ocorreu um erro ao processar o seu pedido. Por favor, tente novamente.";
            
            $_SESSION['checkout_form_data'] = $form_data;
            
            if (session_status() === PHP_SESSION_ACTIVE) {
                session_write_close();
                
            }
        }
        

    } else {
         
         $_SESSION['checkout_form_data'] = $form_data;
         
         
    }
}

function display_error($field, $errors) {
    if (isset($errors[$field])) {
        echo '<p class="text-red-400 text-xs mt-1">' . htmlspecialchars($errors[$field]) . '</p>';
    }
}

function old_value($field, $form_data) {
    return htmlspecialchars($form_data[$field] ?? '');
}

function error_class($field, $errors, $base_class) {
    return $base_class . (isset($errors[$field]) ? ' border-red-500 ring-1 ring-red-500' : ' border-gray-700');
}
?>

<h1 class="text-2xl font-semibold mb-6">Finalizar Compra</h1>

<?php if (isset($errors['form'])): ?>
    <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">Erro!</strong>
        <span class="block sm:inline"><?= htmlspecialchars($errors['form']) ?></span>
    </div>
<?php endif; ?>

<form method="POST" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=checkout') ?>" id="checkout-form">
    <!-- Add CSRF token field later -->
    <!-- <input type="hidden" name="csrf_token" value="<?= generate_csrf_token() ?>"> -->

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Checkout Form -->
        <div class="lg:col-span-2">
            <div class="bg-gray-900 rounded-lg p-6 space-y-6">

                <!-- Contact Information -->
                <div>
                    <h3 class="text-lg font-medium mb-4 border-b border-gray-700 pb-2">Informação de Contacto</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="customer_email" class="block text-sm font-medium text-gray-400 mb-1">Email *</label>
                            <input type="email" id="customer_email" name="customer_email" value="<?= old_value('email', $form_data) ?>" required
                                   class="<?= error_class('customer_email', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="<EMAIL>">
                            <?php display_error('customer_email', $errors); ?>
                        </div>
                        <div>
                            <label for="customer_phone" class="block text-sm font-medium text-gray-400 mb-1">Telefone</label>
                            <input type="tel" id="customer_phone" name="customer_phone" value="<?= old_value('phone', $form_data) ?>"
                                   class="<?= error_class('customer_phone', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="+351 ...">
                             <?php display_error('customer_phone', $errors); ?>
                        </div>
                        <?php
                        
                        $vat_id_required_enabled = get_setting('vat_id_required_enabled', '1') == '1';
                        $vat_id_threshold = get_setting('vat_id_threshold', 1000);

                        
                        $vat_id_required = $vat_id_required_enabled && $grand_total >= $vat_id_threshold;

                        
                        $vat_id_required_class = $vat_id_required ? '' : 'hidden';
                        ?>
                        <div class="md:col-span-2">
                            <label for="customer_vat_id" class="block text-sm font-medium text-gray-400 mb-1">
                                NIF / Número de Identificação Fiscal
                                <span class="text-red-500 vat-id-required-indicator <?= $vat_id_required_class ?>">*</span>
                                <?php if ($vat_id_required_enabled && $vat_id_threshold > 0): ?>
                                <span class="text-xs text-gray-500 ml-1">(obrigatório para encomendas acima de <?= format_price($vat_id_threshold, $currency_symbol) ?>)</span>
                                <?php elseif (!$vat_id_required_enabled): ?>
                                <span class="text-xs text-gray-500 ml-1">(facultativo)</span>
                                <?php endif; ?>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center px-3 pointer-events-none bg-gray-700 border-r border-gray-600 rounded-l text-white">
                                    PT
                                </div>
                                <input type="text" id="customer_vat_id" name="customer_vat_id"
                                       value="<?= old_value('vat_id', $form_data) ?>"
                                       <?= $vat_id_required ? 'required' : '' ?>
                                       class="<?= error_class('customer_vat_id', $errors, 'w-full pl-14 px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>"
                                       placeholder="123456789" maxlength="9">
                            </div>
                            <?php display_error('customer_vat_id', $errors); ?>
                        </div>
                    </div>
                </div>

                <!-- Shipping Address -->
                <div>
                    <h3 class="text-lg font-medium mb-4 border-b border-gray-700 pb-2">
                        <?php if ($only_digital_products): ?>
                            Informações Pessoais
                        <?php else: ?>
                            Morada de Envio
                        <?php endif; ?>
                    </h3>

                    <?php if ($has_digital_products): ?>
                    <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-4 mb-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="ri-information-line text-blue-400 text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-400">Informação sobre Produtos Digitais</h3>
                                <div class="mt-2 text-sm text-blue-300">
                                    <p>
                                        <?php if ($only_digital_products): ?>
                                            O seu carrinho contém apenas produtos digitais. Após o pagamento, receberá um email com as instruções de download.
                                        <?php else: ?>
                                            O seu carrinho contém produtos digitais. Após o pagamento, receberá um email com as instruções de download para estes produtos.
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="shipping_first_name" class="block text-sm font-medium text-gray-400 mb-1">Primeiro Nome *</label>
                            <input type="text" id="shipping_first_name" name="shipping_first_name" value="<?= old_value('first_name', $form_data) ?>" required
                                   class="<?= error_class('shipping_first_name', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>">
                            <?php display_error('shipping_first_name', $errors); ?>
                        </div>
                        <div>
                            <label for="shipping_last_name" class="block text-sm font-medium text-gray-400 mb-1">Apelido *</label>
                            <input type="text" id="shipping_last_name" name="shipping_last_name" value="<?= old_value('last_name', $form_data) ?>" required
                                   class="<?= error_class('shipping_last_name', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>">
                            <?php display_error('shipping_last_name', $errors); ?>
                        </div>

                        <?php if (!$only_digital_products): ?>
                        <!-- Physical shipping address fields - only shown when cart has physical products -->
                        <div class="md:col-span-2">
                            <label for="shipping_address" class="block text-sm font-medium text-gray-400 mb-1">Morada *</label>
                            <input type="text" id="shipping_address" name="shipping_address" value="<?= old_value('shipping_address', $form_data) ?>" required
                                   class="<?= error_class('shipping_address', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="Rua, número, andar...">
                            <?php display_error('shipping_address', $errors); ?>
                        </div>
                        <div>
                            <label for="shipping_zip" class="block text-sm font-medium text-gray-400 mb-1">Código Postal *</label>
                            <input type="text" id="shipping_zip" name="shipping_zip" value="<?= old_value('shipping_zip', $form_data) ?>" required
                                   class="<?= error_class('shipping_zip', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="xxxx-xxx">
                            <?php display_error('shipping_zip', $errors); ?>
                        </div>
                        <div>
                            <label for="shipping_city" class="block text-sm font-medium text-gray-400 mb-1">Localidade *</label>
                            <input type="text" id="shipping_city" name="shipping_city" value="<?= old_value('shipping_city', $form_data) ?>" required
                                   class="<?= error_class('shipping_city', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>">
                            <?php display_error('shipping_city', $errors); ?>
                        </div>
                        <div class="md:col-span-2">
                            <label for="shipping_country" class="block text-sm font-medium text-gray-400 mb-1">País *</label>
                            <select id="shipping_country" name="shipping_country" required
                                    class="<?= error_class('shipping_country', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none appearance-none') ?>">
                                <option value="PT" <?= (old_value('shipping_country', $form_data) == 'PT' || empty(old_value('shipping_country', $form_data))) ? 'selected' : '' ?>>Portugal</option>
                                <!-- Add other countries if needed -->
                            </select>
                            <?php display_error('shipping_country', $errors); ?>
                        </div>
                        <?php else: ?>
                        <!-- Hidden fields for digital-only orders -->
                        <input type="hidden" name="shipping_address" value="Digital Product - No Shipping">
                        <input type="hidden" name="shipping_zip" value="0000-000">
                        <input type="hidden" name="shipping_city" value="Digital Delivery">
                        <input type="hidden" name="shipping_country" value="PT">
                        <?php endif; ?>
                    </div>
                </div>

                 <!-- Order Notes -->
                <div>
                    <label for="order_notes" class="block text-sm font-medium text-gray-400 mb-1">Notas do Pedido (Opcional)</label>
                    <textarea id="order_notes" name="order_notes" rows="3"
                              class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                              placeholder="Instruções especiais para a entrega..."><?= old_value('notes', $form_data) ?></textarea>
                </div>

            </div>
        </div>

        <!-- Order Summary & Payment -->
        <div class="lg:col-span-1">
            <div class="bg-gray-900 rounded-lg p-6 sticky top-24 space-y-6">
                <h3 class="text-lg font-medium border-b border-gray-700 pb-2">Resumo do Pedido</h3>

                <!-- Cart Items Mini Summary -->
                <div class="max-h-60 overflow-y-auto custom-scrollbar space-y-3 pr-2">
                     <?php foreach ($cart_items_summary as $key => $item): ?>
                        <div class="flex items-center py-2 border-b border-gray-800 last:border-b-0" data-cart-key="<?= $key ?>">
                            <img src="<?= sanitize_input($item['image_url']) ?>" alt="<?= sanitize_input($item['name']) ?>" class="w-12 h-12 object-cover rounded flex-shrink-0">
                            <div class="ml-3 flex-1 min-w-0">
                                <div class="flex items-center">
                                    <p class="text-sm font-medium truncate"><?= sanitize_input($item['name']) ?></p>
                                    <?php
                                    
                                    $product_type = null;
                                    if (isset($cart_items[$key]['product_type'])) {
                                        $product_type = $cart_items[$key]['product_type'];
                                    }

                                    if ($product_type === 'digital'):
                                    ?>
                                    <span class="inline-flex items-center ml-2 px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                                        <i class="ri-download-line mr-0.5 text-xs"></i> Digital
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <?php if (!empty($item['attributes_display'])): ?>
                                    <p class="text-xs text-gray-400"><?= sanitize_input($item['attributes_display']) ?></p>
                                <?php elseif (isset($cart_items[$key]['variation_id']) && $cart_items[$key]['variation_id'] !== null): ?>
                                    <?php
                                    
                                    require_once __DIR__ . '/../../includes/product_functions.php';
                                    $attributes_display = get_variation_attribute_string($cart_items[$key]['variation_id']);
                                    if (!empty($attributes_display)):
                                    ?>
                                        <p class="text-xs text-gray-400"><?= sanitize_input($attributes_display) ?></p>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if (!empty($item['custom_fields'])): ?>
                                    <?php foreach ($item['custom_fields'] as $custom_field): ?>
                                        <p class="text-xs text-gray-400"><?= sanitize_input(get_cart_custom_field_display($custom_field)) ?></p>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <div class="flex items-center mt-1">
                                    <button class="decrease-qty-btn text-xs bg-gray-800 hover:bg-gray-700 w-5 h-5 flex items-center justify-center rounded" data-cart-key="<?= $key ?>">
                                        <i class="ri-subtract-line"></i>
                                    </button>
                                    <input type="number" value="<?= $item['quantity'] ?>" min="1" max="<?= $item['stock'] ?? 99 ?>" class="quantity-input w-8 bg-transparent border-none text-center text-xs text-white" data-cart-key="<?= $key ?>">
                                    <button class="increase-qty-btn text-xs bg-gray-800 hover:bg-gray-700 w-5 h-5 flex items-center justify-center rounded" data-cart-key="<?= $key ?>">
                                        <i class="ri-add-line"></i>
                                    </button>
                                    <span class="text-xs text-gray-400 ml-2">× <?= $item['price_formatted'] ?></span>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">Total: <span class="total-item"><?= format_price($item['price'] * $item['quantity'], $currency_symbol) ?></span></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Totals -->
                <div class="border-t border-gray-800 pt-4 space-y-1">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-400">Subtotal</span>
                        <span><?= format_price($cart_subtotal, $currency_symbol) ?></span>
                    </div>
                     <div class="flex justify-between text-sm">
                        <span class="text-gray-400">Envio</span>
                        <span>
                            <?php if ($only_digital_products): ?>
                                <span class="text-blue-400">Produto Digital</span>
                            <?php else: ?>
                                <?= $shipping_cost > 0 ? format_price($shipping_cost, $currency_symbol) : 'Grátis' ?>
                            <?php endif; ?>
                        </span>
                    </div>
                    <?php if ($has_digital_products): ?>
                    <div class="flex justify-between text-sm text-blue-400">
                        <span><i class="ri-information-line mr-1"></i> Produtos digitais</span>
                        <span>Entrega por email</span>
                    </div>
                    <?php endif; ?>
                    <?php if ($free_shipping_threshold > 0 && $has_free_shipping): ?>
                    <div class="flex justify-between text-sm text-green-500">
                        <span><i class="ri-checkbox-circle-fill mr-1"></i> Envio gratuito aplicado</span>
                        <span></span>
                    </div>
                    <?php endif; ?>
                     <?php if (count($vat_groups) <= 1): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">IVA <?php
                                if (!empty($vat_groups)) {
                                    $first_group = reset($vat_groups);
                                    echo '(' . sanitize_input($first_group['description']) . ' ' . number_format($first_group['rate'], 1, ',') . '%)';
                                } else {
                                    echo '(0%)';
                                }
                            ?></span>
                            <span><?= format_price($tax_amount, $currency_symbol) ?></span>
                        </div>
                    <?php else: ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">IVA (Detalhado)</span>
                            <span><?= format_price($tax_amount, $currency_symbol) ?></span>
                        </div>
                        <?php foreach ($vat_groups as $group): ?>
                        <div class="flex justify-between pl-4 text-xs">
                            <span class="text-gray-500"><?= sanitize_input($group['description']) ?> <?= number_format($group['rate'], 1, ',') ?>%</span>
                            <span><?= format_price($group['tax'], $currency_symbol) ?></span>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                     <?php if ($discount_amount > 0): ?>
                    <div class="flex justify-between text-sm text-green-400">
                        <span class="text-gray-400">
                            Desconto
                            <?php
                            
                            if (isset($_SESSION['applied_coupon_description']) && !empty($_SESSION['applied_coupon_description'])) {
                                echo ' <i>(' . sanitize_input($_SESSION['applied_coupon_description']) . ')</i>';
                            } elseif (isset($_SESSION['applied_promo_code']) && !empty($_SESSION['applied_promo_code'])) {
                                
                                echo ' <i>(' . sanitize_input($_SESSION['applied_promo_code']) . ')</i>';
                            }
                            ?>
                        </span>
                        <span>-<?= format_price($discount_amount, $currency_symbol) ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="flex justify-between font-semibold text-lg pt-2 border-t border-gray-700 mt-2">
                        <span>Total</span>
                        <span><?= format_price($grand_total, $currency_symbol) ?></span>
                    </div>
                </div>

                <!-- Payment Method -->
                <div>
                    <h3 class="text-lg font-medium mb-4 border-b border-gray-700 pb-2">Método de Pagamento</h3>
                    <div class="space-y-3">
                        <?php $payment_error_class = isset($errors['payment_method']) ? 'border-red-500 ring-1 ring-red-500' : 'border-gray-700'; ?>

                        <?php if (empty($payment_methods)): ?>
                            <!-- Fallback to default payment method if none are configured -->
                            <label class="flex items-center p-3 border <?= $payment_error_class ?> rounded-lg cursor-pointer hover:border-primary transition has-[:checked]:border-primary has-[:checked]:ring-1 has-[:checked]:ring-primary">
                                <input type="radio" name="payment_method" value="bank_transfer" class="h-4 w-4 text-primary focus:ring-primary border-gray-600 bg-gray-800" <?= (old_value('payment_method', $form_data) == 'bank_transfer') ? 'checked' : '' ?> required>
                                <div class="ml-3">
                                    <span class="block text-sm font-medium">Transferência Bancária</span>
                                    <span class="block text-xs text-gray-400">Os detalhes serão enviados por email.</span>
                                </div>
                            </label>
                        <?php else: ?>
                            <?php foreach ($payment_methods as $method): ?>
                                <label class="flex items-center p-3 border <?= $payment_error_class ?> rounded-lg cursor-pointer hover:border-primary transition has-[:checked]:border-primary has-[:checked]:ring-1 has-[:checked]:ring-primary">
                                    <input type="radio" name="payment_method" value="<?= $method['id'] ?>" class="h-4 w-4 text-primary focus:ring-primary border-gray-600 bg-gray-800" <?= (old_value('payment_method', $form_data) == $method['id']) ? 'checked' : '' ?> required>
                                    <div class="ml-3">
                                        <span class="block text-sm font-medium"><?= sanitize_input($method['title']) ?></span>
                                        <span class="block text-xs text-gray-400">Os detalhes serão enviados por email.</span>
                                    </div>
                                </label>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <?php display_error('payment_method', $errors); ?>
                </div>

                 <!-- Required Agreement Checkboxes -->
                 <div>
                     <h3 class="text-lg font-medium mb-4 border-b border-gray-700 pb-2">Termos e Condições</h3>
                     <div class="space-y-2">
                         <?php if (!empty($required_pages)): ?>
                             <?php foreach ($required_pages as $req_page):
                                 $checkbox_name = 'agreement_' . $req_page['id'];
                                 
                                 $is_checked = isset($form_data['agreements'][$req_page['id']]) && $form_data['agreements'][$req_page['id']] === '1';
                                 $page_url = get_page_url($req_page['slug']);
                                 $agreement_error_class = isset($errors[$checkbox_name]) ? 'text-red-400' : '';
                             ?>
                                 <div class="flex items-start">
                                     <input id="<?= $checkbox_name ?>" name="agreements[<?= $req_page['id'] ?>]" value="1" type="checkbox" class="h-4 w-4 rounded border-gray-600 bg-gray-800 text-primary focus:ring-primary mt-0.5 flex-shrink-0 <?= isset($errors[$checkbox_name]) ? 'border-red-500 ring-1 ring-red-500' : '' ?>" required <?= $is_checked ? 'checked' : '' ?>>
                                     <label for="<?= $checkbox_name ?>" class="ml-2 text-sm <?= $agreement_error_class ?>">
                                         Li e aceito a <a href="<?= $page_url ?>" target="_blank" class="text-primary hover:underline font-medium"><?= htmlspecialchars($req_page['title']) ?></a> *
                                     </label>
                                 </div>
                                  <?php display_error($checkbox_name, $errors); ?>
                             <?php endforeach; ?>
                         <?php endif; ?>
                     </div>
                 </div>

                <!-- Submit Button -->
                <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium whitespace-nowrap transition">
                    Confirmar Encomenda
                </button>
            </div>
        </div>
    </div>
</form>

<!-- Include Checkout JS -->
<script src="<?= get_asset_url('js/checkout.js') ?>"></script>

<?php

ob_end_flush();
?>

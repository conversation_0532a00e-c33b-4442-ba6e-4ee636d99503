<?php

$is_editing = ($action === 'edit' && isset($item_id));
$form_title = $is_editing ? "Editar Placeholder" : "Criar Novo Placeholder";
$placeholder_data = null;

if ($is_editing) {
    
    $placeholder_data = get_page_placeholder_by_id($item_id);
    if (!$placeholder_data) {
        echo '<div class="alert alert-danger">Placeholder não encontrado.</div>';
        return; 
    }
    $form_title .= " (#" . $placeholder_data['id'] . ")";
}

?>

<h1><?= $form_title ?></h1>
<hr>

<form method="POST" action="admin.php?section=page_placeholders&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>">
    <?= csrf_input_field() ?>

    <div class="card mb-4">
        <div class="card-header">Detalhes do Placeholder</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="name" class="form-label">Nome do Placeholder *</label>
                <input type="text" class="form-control" id="name" name="name" value="<?= sanitize_input($placeholder_data['name'] ?? '') ?>" required>
                <div class="form-text">Este nome será exibido como título da seção no rodapé.</div>
            </div>
            <div class="mb-3">
                <label for="slug" class="form-label">Slug *</label>
                <input type="text" class="form-control" id="slug" name="slug" value="<?= sanitize_input($placeholder_data['slug'] ?? '') ?>">
                <div class="form-text">Identificador único para o placeholder. Se deixado em branco, será gerado automaticamente a partir do nome.</div>
            </div>

            <?php if ($is_editing): ?>
            <div class="mb-3">
                <label class="form-label">Páginas Associadas</label>
                <?php
                $placeholder_pages = get_pages_by_placeholder_id($item_id);
                if (!empty($placeholder_pages)): ?>
                    <ul class="list-group">
                        <?php foreach ($placeholder_pages as $page): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?= htmlspecialchars($page['title']) ?>
                                <a href="admin.php?section=pages&action=edit&id=<?= $page['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil-square"></i> Editar
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p class="text-muted">Nenhuma página associada a este placeholder.</p>
                <?php endif; ?>
                <div class="form-text mt-2">Para associar páginas a este placeholder, edite a página desejada e selecione este placeholder.</div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="mt-4">
        <button type="submit" class="btn btn-primary">Guardar Placeholder</button>
        <a href="admin.php?section=page_placeholders&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
    </div>

</form>

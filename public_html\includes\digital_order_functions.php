<?php

require_once __DIR__ . '/digital_product_functions.php';

function order_contains_digital_products(array $order_items): bool
{
    if (empty($order_items)) return false;

    foreach ($order_items as $item) {
        $product_id = $item['product_id'] ?? 0;
        if ($product_id <= 0) continue;

        $product = db_query(
            "SELECT product_type FROM products WHERE id = :id",
            [':id' => $product_id],
            true
        );

        if ($product && $product['product_type'] === 'digital') {
            return true;
        }
    }

    return false;
}

function get_digital_products_from_order(array $order_items): array
{
    if (empty($order_items)) return [];

    $digital_products = [];

    foreach ($order_items as $item) {
        $product_id = $item['product_id'] ?? 0;
        if ($product_id <= 0) continue;

        $product = db_query(
            "SELECT id, product_type FROM products WHERE id = :id",
            [':id' => $product_id],
            true
        );

        if ($product && $product['product_type'] === 'digital') {

            $digital_product = get_digital_product_by_product_id($product_id);
            if ($digital_product) {
                $digital_products[] = $digital_product;
            }
        }
    }

    return $digital_products;
}

function create_licenses_for_order(int $order_id, array $customer_info): array
{
    if ($order_id <= 0 || empty($customer_info)) {
        
        return [];
    }

    
    $order_items = db_query(
        "SELECT oi.id as order_item_id, oi.product_id
         FROM order_items oi
         WHERE oi.order_id = :order_id",
        [':order_id' => $order_id],
        false, true
    );

    if (empty($order_items)) {
        
        return [];
    }

    $license_ids = [];
    $customer_name = $customer_info['customer_name'] ?? '';
    $customer_email = $customer_info['customer_email'] ?? '';

    if (empty($customer_name) || empty($customer_email)) {
        
        return [];
    }

    
    require_once __DIR__ . '/digital_product_functions.php';

    $digital_product_table_ids_for_order = [];
    $first_digital_order_item_id = null;
    $first_digital_product_entry_for_settings = null;

    foreach ($order_items as $item) {
        if (empty($item['product_id']) || empty($item['order_item_id'])) {
            
            continue;
        }

        $product_details = db_query("SELECT product_type FROM products WHERE id = :pid", [':pid' => $item['product_id']], true);

        if ($product_details && $product_details['product_type'] === 'digital') {
            

            $digital_product_entry = get_digital_product_by_product_id($item['product_id']);

            if ($digital_product_entry && isset($digital_product_entry['id'])) {
                $digital_product_table_ids_for_order[] = $digital_product_entry['id'];
                if ($first_digital_order_item_id === null) {
                    $first_digital_order_item_id = $item['order_item_id'];
                    $first_digital_product_entry_for_settings = $digital_product_entry;
                }
            } else {
            }
        }
    }

    if (!empty($digital_product_table_ids_for_order)) {
        
        $license_data = [
            'order_id' => $order_id,
            'order_item_id' => $first_digital_order_item_id,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'status' => 'waiting_payment',
            'digital_product_ids' => $digital_product_table_ids_for_order
        ];

        if ($first_digital_product_entry_for_settings) {
            $license_data['expiry_days'] = $first_digital_product_entry_for_settings['expiry_days'] ?? get_setting('digital_download_expiry_days', 5);
            $license_data['download_limit'] = $first_digital_product_entry_for_settings['download_limit'] ?? get_setting('digital_download_limit', 3);
        } else {
            
            $license_data['expiry_days'] = get_setting('digital_download_expiry_days', 5);
            $license_data['download_limit'] = get_setting('digital_download_limit', 3);
        }

        

        $license_id = create_license($license_data);
        if ($license_id) {
            $license_ids[] = $license_id;
            
        } else {
        }
    } else {
        
    }
    
    return $license_ids;
}

function activate_licenses_for_order(int $order_id): bool
{
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {

        $licenses = db_query(
            "SELECT * FROM licenses WHERE order_id = :order_id",
            [':order_id' => $order_id],
            false, true
        );

        if (empty($licenses)) {

            $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
            if (!$order) {
                return false;
            }

            $customer_info = json_decode($order['customer_info_json'], true);

            $license_ids = create_licenses_for_order($order_id, $customer_info);
            if (empty($license_ids)) {
                return false;
            }

            $licenses = db_query(
                "SELECT * FROM licenses WHERE order_id = :order_id",
                [':order_id' => $order_id],
                false, true
            );
        }

        if (empty($licenses)) {
            return false;
        }

        $pdo->beginTransaction();
        $success = true;
        $all_product_details_for_email = [];
        $master_token_data = null;
        $overall_min_download_limit = PHP_INT_MAX;
        $overall_earliest_expiry_date_ts = PHP_INT_MAX; 

        foreach ($licenses as $license_record) { 
            
            
            
            

            $current_license_expiry_ts = strtotime($license_record['expiry_date']);
            if ($current_license_expiry_ts !== false && $current_license_expiry_ts < $overall_earliest_expiry_date_ts) {
                $overall_earliest_expiry_date_ts = $current_license_expiry_ts;
            }

            $current_download_limit = (int) $license_record['download_limit'];
            if ($current_download_limit < $overall_min_download_limit) {
                $overall_min_download_limit = $current_download_limit;
            }

            
            
            
            
            
            $update_params = [':id' => $license_record['id']];
            $update_sql_parts = ["status = 'active'", "updated_at = datetime('now', 'localtime')"];

            
            
            
            
            

            $result = db_query(
                "UPDATE licenses SET " . implode(', ', $update_sql_parts) . " WHERE id = :id",
                $update_params
            );

            if ($result === false) {
                $success = false;
                break;
            } else {
                $updated_license_details = get_license_by_id($license_record['id']);
                if ($updated_license_details) {
                    require_once __DIR__ . '/digital_product_functions.php';
                    $licensed_digital_products = get_license_digital_products($updated_license_details['id']);

                    if (!empty($licensed_digital_products)) {
                        
                        
                        if ($master_token_data === null) {
                            $master_token_data = create_download_token(
                                $updated_license_details['id'],
                                60 * 24 * get_setting('digital_direct_download_link_expiry_days', 7) 
                            );
                        }

                        
                        $license_expiry_ts = strtotime($updated_license_details['expiry_date']);
                        if ($license_expiry_ts !== false && $license_expiry_ts < $overall_earliest_expiry_date_ts) {
                            $overall_earliest_expiry_date_ts = $license_expiry_ts;
                        }
                        if ((int)$updated_license_details['download_limit'] < $overall_min_download_limit) {
                            $overall_min_download_limit = (int)$updated_license_details['download_limit'];
                        }

                        foreach ($licensed_digital_products as $licensed_dp_entry) {
                            $product_id_for_name = $licensed_dp_entry['product_id'] ?? 0;
                            $product_for_name = $product_id_for_name ? db_query("SELECT name_pt FROM products WHERE id = :id", [':id' => $product_id_for_name], true) : false;

                            if ($product_for_name) {
                                $all_product_details_for_email[] = [
                                    'license_code' => $updated_license_details['license_code'], 
                                    'product_name' => $product_for_name['name_pt']
                                    
                                ];
                            } else {
                            }
                        }
                    } else {
                    }
                } else {
                }
            }
        }

        if ($success) {
            $pdo->commit();
            if (!empty($all_product_details_for_email) && $master_token_data) {
                $final_overall_expiry_date_str = ($overall_earliest_expiry_date_ts === PHP_INT_MAX) ? 'N/A' : date('d/m/Y', $overall_earliest_expiry_date_ts);
                $final_overall_min_download_limit = ($overall_min_download_limit === PHP_INT_MAX) ? 'N/A' : $overall_min_download_limit;

                send_consolidated_license_activation_email(
                    $order_id,
                    $all_product_details_for_email,
                    $master_token_data,
                    $final_overall_expiry_date_str, 
                    $final_overall_min_download_limit 
                );
            }
        } else {
            $pdo->rollBack();
        }

        return $success;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function send_consolidated_license_activation_email(
    int $order_id,
    array $product_details_for_email,
    ?array $master_token_data,
    string $overall_license_expiry_date_str,
    string $overall_min_download_limit_str
): bool
{
    if ($order_id <= 0 || empty($product_details_for_email)) {
        return false;
    }

    $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
    if (!$order) {
        return false;
    }

    $customer_info = json_decode($order['customer_info_json'], true);
    $customer_name = $customer_info['customer_name'] ?? '';
    $customer_email = $customer_info['customer_email'] ?? '';

    if (empty($customer_email)) {
        return false;
    }

    $store_name = get_setting('store_name', 'A Nossa Loja');
    $base_url = BASE_URL;

    $subject = "Seus produtos digitais da {$store_name} - Pedido #{$order['order_ref']} estão prontos!";

    $message = "<html><body style='font-family: Arial, sans-serif; line-height: 1.6;'>";
    $message .= "<div style='max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>";
    $message .= "<div style='text-align: center; margin-bottom: 20px;'><img src='" . $base_url . "/public/assets/images/logo/store_logo.png' alt='" . htmlspecialchars($store_name) . " Logo' style='max-width: 150px; height: auto;'></div>";$message .= "<div style='text-align: center; margin-bottom: 20px;'><img src='" . $base_url . "/public/assets/images/logo/store_logo.png' alt='" . htmlspecialchars($store_name) . " Logo' style='max-width: 150px; height: auto; filter: invert(1) brightness(0) contrast(100);'></div>";
    $message .= "<h2 style='color: #333;'>Seus produtos digitais estão prontos para download!</h2>";
    $message .= "<p>Olá " . htmlspecialchars($customer_name) . ",</p>";
    $message .= "<p>Obrigado pela sua compra! Seus produtos digitais para o pedido #" . htmlspecialchars($order['order_ref']) . " foram ativados e estão prontos para download.</p>";

    $message .= "<h3 style='color: #333; margin-top: 25px; border-bottom: 1px solid #eee; padding-bottom: 5px;'>Seus Produtos Digitais Incluídos:</h3>";
    $message .= "<table style='width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 20px;'>";
    $message .= "<thead><tr style='background-color: #f0f0f0;'>
                    <th style='padding: 10px; text-align: left; border: 1px solid #ddd;'>Produto</th>
                    <th style='padding: 10px; text-align: left; border: 1px solid #ddd;'>Código de Licença Associado</th>
                 </tr></thead>";
    $message .= "<tbody>";

    foreach ($product_details_for_email as $details) {
        $product_name_display = htmlspecialchars($details['product_name']);
        $license_code_display = htmlspecialchars($details['license_code']);

        $message .= "<tr>";
        $message .= "<td style='padding: 10px; border: 1px solid #ddd;'>{$product_name_display}</td>";
        $message .= "<td style='padding: 10px; border: 1px solid #ddd;'>{$license_code_display}</td>";
        $message .= "</tr>";
    }
    $message .= "</tbody></table>";

    
    $download_link_html = "";
    $token_direct_link_expiry_info = "";

    if ($master_token_data && !empty($master_token_data['email_token'])) {
        $download_url = $base_url . '/index.php?view=download&email_token=' . urlencode($master_token_data['email_token']);
        if(function_exists('add_session_param_to_url')){
            $download_url = add_session_param_to_url($download_url);
        }
        $download_link_html = "<p style='text-align: center; margin-top: 20px; margin-bottom: 20px;'><a href=\"" . htmlspecialchars($download_url) . "\" style='display: inline-block; background-color: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-size: 1.1em; font-weight: bold;'>Download Direto de Seus Produtos</a></p>";
        $token_direct_link_expiry_info = "<li style='font-size: 0.9em;'>O token de download é portanto válido até: <strong>" . date('d/m/Y H:i', strtotime($master_token_data['expires_at'])) . "</strong>.</li>";
    } else {
        $download_link_html = "<p style='color: #dc3545; font-size: 0.9em; text-align: center; margin-top: 20px; margin-bottom: 20px;'>Ocorreu um erro ao gerar o link de download. Por favor, contacte o suporte.</p>";
    }

    $message .= $download_link_html;

    $message .= "<div style='background-color: #f9f9f9; border: 1px solid #eee; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
    $message .= "<h4 style='margin-top: 0; color: #333;'>Informações Importantes sobre seu Acesso:</h4>";
    $message .= "<ul style='padding-left: 20px; margin-bottom: 0;'>";
    $message .= "<li style='font-size: 0.9em;'>Sua licença (para todos os produtos acima) permite até: <strong>" . htmlspecialchars($overall_min_download_limit_str) . " downloads no total</strong>.</li>";
    $message .= "<li style='font-size: 0.9em;'>O link de download (para todos os produtos acima) é válido até: <strong>" . htmlspecialchars($overall_license_expiry_date_str) . "</strong>.</li>";
    $message .= "<li style='font-size: 0.9em;'>Guarde os códigos de licença. Eles podem ser necessários se o link direto expirar e para verificação de autenticidade.</li>";
    $message .= "</ul>";
    $message .= "</div>";

    $message .= "<br><p>Se tiver dificuldades com o link direto, pode aceder à página de downloads no site e inserir o código de licença correspondente ao produto que deseja baixar. Se ainda for possível fazer o seu download essa opção surgirá como disponível, caso contrário.. entre em contacto.</p>";
    $message .= "<p style='margin-top: 20px;'>Atenciosamente,<br><strong>" . htmlspecialchars($store_name) . "</strong></p>";
    $message .= "</div>";
    $message .= "</body></html>";

    require_once __DIR__ . '/email_functions.php';
    if (!function_exists('send_email')) {
        return false;
    }
    return send_email($customer_email, $customer_name, $subject, $message);
}

function cancel_licenses_for_order(int $order_id): bool
{
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $result = db_query(
            "UPDATE licenses SET
                status = 'canceled',
                updated_at = datetime('now', 'localtime')
            WHERE order_id = :order_id",
            [':order_id' => $order_id]
        );

        if ($result === false) {
            throw new Exception("Failed to update license status");
        }

        require_once __DIR__ . '/digital_product_functions.php';
        $tokens_removed = remove_order_download_tokens($order_id);

        if (!$tokens_removed) {

        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function deactivate_licenses_for_order(int $order_id): bool
{
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $result = db_query(
            "UPDATE licenses SET
                status = 'disabled',
                updated_at = datetime('now', 'localtime')
            WHERE order_id = :order_id",
            [':order_id' => $order_id]
        );

        if ($result === false) {
            throw new Exception("Failed to update license status");
        }

        require_once __DIR__ . '/digital_product_functions.php';
        $tokens_removed = remove_order_download_tokens($order_id);

        if (!$tokens_removed) {

        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_digital_checkout_agreement_page(): array|false
{
    $page_id = get_setting('digital_checkout_agreement_page_id', 0);
    if ($page_id <= 0) return false;

    return db_query(
        "SELECT * FROM pages WHERE id = :id AND is_active = 1",
        [':id' => $page_id],
        true
    );
}

function cart_contains_digital_products(array $cart_items): bool
{
    if (empty($cart_items)) return false;

    foreach ($cart_items as $item) {
        $product_id = $item['product_id'] ?? 0;
        if ($product_id <= 0) continue;

        $product = db_query(
            "SELECT product_type FROM products WHERE id = :id",
            [':id' => $product_id],
            true
        );

        if ($product && $product['product_type'] === 'digital') {
            return true;
        }
    }

    return false;
}

function count_digital_products_in_cart(array $cart_items): int
{
    if (empty($cart_items)) return 0;

    $count = 0;

    foreach ($cart_items as $item) {
        $product_id = $item['product_id'] ?? 0;
        if ($product_id <= 0) continue;

        
        if (isset($item['product_type']) && $item['product_type'] === 'digital') {
            $count++;
            continue;
        }

        
        $product = db_query(
            "SELECT product_type FROM products WHERE id = :id",
            [':id' => $product_id],
            true
        );

        if ($product && $product['product_type'] === 'digital') {
            $count++;
        }
    }

    return $count;
}

function get_order_license_by_id(int $license_id): array|false
{
    if ($license_id <= 0) return false;

    return db_query(
        "SELECT * FROM licenses WHERE id = :id",
        [':id' => $license_id],
        true
    );
}

function send_license_order_email(array $license): bool
{
    if (empty($license)) return false;

    $order_id = $license['order_id'] ?? 0;
    if ($order_id <= 0) return false;

    $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
    if (!$order) return false;

    
    $customer_info_json = $order['customer_info_json'] ?? '{}';
    $customer_info = json_decode($customer_info_json, true);
    
    $customer_name_from_order = $customer_info['customer_name'] ?? 'Cliente'; 
    $customer_email_from_order = $customer_info['customer_email'] ?? null;

    
    if (empty($customer_email_from_order) || !filter_var($customer_email_from_order, FILTER_VALIDATE_EMAIL) || strpos($customer_email_from_order, 'anonymized') !== false || strpos($customer_email_from_order, '@example.com') !== false) {
        return false; 
    }

    
    $final_customer_name = ($customer_name_from_order !== 'Cliente' && !empty($customer_name_from_order)) ? $customer_name_from_order : get_decrypted_license_name($license);
    $final_customer_email = $customer_email_from_order;

    $order_item_id = $license['order_item_id'] ?? 0;
    if ($order_item_id <= 0) return false;

    $order_item = db_query(
        "SELECT * FROM order_items WHERE id = :id",
        [':id' => $order_item_id],
        true
    );

    if (!$order_item) return false;

    $product_id = $order_item['product_id'] ?? 0;
    if ($product_id <= 0) return false;

    $product = db_query(
        "SELECT * FROM products WHERE id = :id",
        [':id' => $product_id],
        true
    );

    if (!$product) return false;

    $store_name = get_setting('store_name', 'A Nossa Loja');
    $base_url = BASE_URL;

    require_once __DIR__ . '/digital_product_functions.php';
    $token_data = create_download_token($license['id'], 60 * 24 * 7);

    $subject = "Seus produtos digitais da {$store_name} - Pedido #{$order_id}";

    $message = "<html><body>";
    $message .= "<h2>Seus produtos digitais estão prontos para download</h2>";
    $message .= "<p>Olá " . htmlspecialchars($final_customer_name) . ",</p>";
    $message .= "<p>Obrigado pela sua compra! Seus produtos digitais estão prontos para download.</p>";
    $message .= "<p>Detalhes do pedido: #{$order['order_ref']}</p>"; 
    $message .= "<h3>Seu produto digital:</h3>";
    $message .= "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>";
    $message .= "<tr><th>Produto</th><th>Código de licença</th><th>Expira em</th><th>Downloads restantes</th></tr>";

    $expiry_date = date('d/m/Y', strtotime($license['expiry_date']));
    $downloads_remaining = $license['download_limit'] - $license['downloads_used'];

    $message .= "<tr>";
    $message .= "<td>{$product['name_pt']}</td>";
    $message .= "<td>{$license['license_code']}</td>";
    $message .= "<td>{$expiry_date}</td>";
    $message .= "<td>{$downloads_remaining}</td>";
    $message .= "</tr>";

    $message .= "</table>";

    if ($token_data) {
        $download_url = $base_url . '/index.php?view=download&email_token=' . urlencode($token_data['email_token']);

        $download_url = add_session_param_to_url($download_url);

        $message .= "<div style='margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px;'>";
        $message .= "<h3>Download Direto</h3>";
        $message .= "<p>Clique no botão abaixo para fazer o download direto do seu produto:</p>";
        $message .= "<p><a href=\"{$download_url}\" style='display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Download Direto</a></p>";
        $message .= "<p>Este link é válido até " . date('d/m/Y H:i', strtotime($token_data['expires_at'])) . ".</p>";
        $message .= "</div>";

        $message .= "<p>Você também pode fazer o download manualmente acessando a página de downloads em nosso site e inserindo o código de licença acima.</p>";
    } else {
        $message .= "<p>Para fazer o download do seu produto, acesse a página de downloads em nosso site e insira o código de licença acima.</p>";
    }

    $message .= "<p>Atenciosamente,<br>{$store_name}</p>";
    $message .= "</body></html>";

    return send_email($final_customer_email, $final_customer_name, $subject, $message);
}

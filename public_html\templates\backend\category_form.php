<?php

defined('ADMIN_CONTEXT') or die('Direct access is not allowed.');

$category_id = $item_id ?? null;
$category_data = $category_data ?? null;
$form_data = $form_data ?? [];

$is_editing = isset($category_data) && !empty($category_data);
$form_title = $is_editing ? 'Editar Categoria' : 'Adicionar Nova Categoria';
$category_name = $form_data['category_name'] ?? ($category_data['name'] ?? '');
$category_slug = $form_data['slug'] ?? ($category_data['slug'] ?? '');
?>

<h1><?= $form_title ?></h1>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <form method="POST" action="admin.php?section=categories&action=<?= $is_editing ? 'edit&id=' . $category_id : 'new' ?>&<?= get_session_id_param() ?>">
            <?= csrf_input_field() ?>

            <div class="mb-3">
                <label for="name" class="form-label">Nome da Categoria *</label>
                <input type="text" class="form-control" id="name" name="category_name" value="<?= htmlspecialchars($category_name) ?>" required>
            </div>

            <div class="mb-3">
                <label for="slug" class="form-label">Slug *</label>
                <input type="text" class="form-control" id="slug" name="slug" value="<?= htmlspecialchars($category_slug) ?>" required>
                <div class="form-text">Identificador único para URLs (ex: "eletronicos"). Será gerado automaticamente se deixado em branco.</div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> <?= $is_editing ? 'Atualizar Categoria' : 'Adicionar Categoria' ?>
                </button>
                <a href="admin.php?section=categories&<?= get_session_id_param() ?>" class="btn btn-secondary">
                    <i class="bi bi-x"></i> Cancelar
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-generate slug from name
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');

    if (nameInput && slugInput) {
        nameInput.addEventListener('input', function() {
            // Only auto-generate if slug is empty or hasn't been manually edited
            if (!slugInput.value || slugInput._autoGenerated) {
                slugInput.value = generateSlug(nameInput.value);
                slugInput._autoGenerated = true;
            }
        });

        // Once user manually edits slug, stop auto-generating
        slugInput.addEventListener('input', function() {
            slugInput._autoGenerated = false;
        });
    }

    // Slug generation function
    function generateSlug(text) {
        return text.toString().toLowerCase()
            .normalize('NFD') // split an accented letter in the base letter and the acent
            .replace(/[\u0300-\u036f]/g, '') // remove all previously split accents
            .replace(/\s+/g, '-')           // Replace spaces with -
            .replace(/[^\w\-]+/g, '')       // Remove all non-word chars except -
            .replace(/\-\-+/g, '-')         // Replace multiple - with single -
            .replace(/^-+/, '')             // Trim - from start of text
            .replace(/-+$/, '');            // Trim - from end of text
    }
});
</script>
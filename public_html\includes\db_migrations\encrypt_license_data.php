<?php

require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../db.php';
require_once __DIR__ . '/../security.php';

$pdo = get_db_connection();

$stmt = $pdo->query("PRAGMA table_info(licenses);");
$columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1); 

if (!in_array('is_encrypted', $columns)) {
    try {
        $pdo->exec("ALTER TABLE licenses ADD COLUMN is_encrypted INTEGER NOT NULL DEFAULT 0;");
        echo "Successfully added 'is_encrypted' column to licenses table.\n";
    } catch (PDOException $e) {
        echo "Error adding column: " . $e->getMessage() . "\n";
        exit(1);
    }
}

$sql = "SELECT * FROM licenses WHERE is_encrypted = 0 OR is_encrypted IS NULL";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$licenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

$total = count($licenses);
$encrypted = 0;
$errors = 0;

echo "Found {$total} licenses to encrypt.\n";

foreach ($licenses as $license) {
    $customer_name = $license['customer_name'];
    $customer_email = $license['customer_email'];
    
    
    $encrypted_name = encrypt_sensitive_data($customer_name);
    $encrypted_email = encrypt_sensitive_data($customer_email);
    
    if ($encrypted_name === false || $encrypted_email === false) {
        echo "Error encrypting data for license ID {$license['id']}.\n";
        $errors++;
        continue;
    }
    
    
    $update_sql = "UPDATE licenses SET 
                    customer_name = :customer_name,
                    customer_email = :customer_email,
                    is_encrypted = 1,
                    updated_at = datetime('now', 'localtime')
                   WHERE id = :id";
    
    $update_stmt = $pdo->prepare($update_sql);
    $result = $update_stmt->execute([
        ':customer_name' => $encrypted_name,
        ':customer_email' => $encrypted_email,
        ':id' => $license['id']
    ]);
    
    if ($result) {
        $encrypted++;
    } else {
        $errors++;
        echo "Error updating license ID {$license['id']}.\n";
    }
}

echo "Encryption complete. Encrypted: {$encrypted}, Errors: {$errors}, Total: {$total}\n";

/* Icon Selector Styles */
.icon-selector-container {
    position: relative;
}

.icon-selector-dropdown {
    width: 320px;
    max-width: 100%;
}

.icon-btn {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.icon-btn:hover {
    background-color: var(--bs-primary);
    color: white;
}

/* Make the tabs smaller and more compact */
.icon-selector-dropdown .nav-tabs .nav-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Ensure the dropdown doesn't get cut off */
.dropdown-menu.icon-selector-dropdown {
    max-height: 400px;
    overflow-y: auto;
}

/* Styling for dropdown when it should be shown */
.icon-selector-dropdown.show {
    display: block;
}

/* Only keep the dropdown open when it has the keep-open class AND is not being closed */
.icon-selector-dropdown.keep-open:not(.closing) {
    display: block;
}

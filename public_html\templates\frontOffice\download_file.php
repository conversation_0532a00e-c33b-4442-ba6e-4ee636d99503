<?php

if (!isset($_SESSION['download_file']) || 
    !isset($_SESSION['download_file']['path']) || 
    !isset($_SESSION['download_file']['name']) ||
    !isset($_SESSION['download_file']['expires'])) {
    
    
    header('Location: index.php?view=download&error=invalid_session');
    exit;
}

if ($_SESSION['download_file']['expires'] < time()) {
    
    unset($_SESSION['download_file']);
    
    
    header('Location: index.php?view=download&error=expired');
    exit;
}

$file_path = $_SESSION['download_file']['path'];
$file_name = $_SESSION['download_file']['name'];

if (!file_exists($file_path)) {
    
    unset($_SESSION['download_file']);
    
    
    header('Location: index.php?view=download&error=file_not_found');
    exit;
}

$file_size = filesize($file_path);

header('Content-Description: File Transfer');
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $file_name . '"');
header('Content-Transfer-Encoding: binary');
header('Expires: 0');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Content-Length: ' . $file_size);

ob_clean();
flush();

readfile($file_path);

@unlink($file_path);

unset($_SESSION['download_file']);

exit;

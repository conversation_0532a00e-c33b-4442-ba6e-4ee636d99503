<?php
require_once __DIR__ . '/banner_functions.php';

function display_banners($banner_type, $css_class = '') {
    
    $all_banners = get_active_banners($banner_type, null); 
    
    if (empty($all_banners)) {
        return ''; 
    }
    
    
    $banner_to_display = $all_banners[0];
    
    
    increment_banner_visitor_count($banner_to_display['id']);
        
    $banner_url = get_banner_url($banner_to_display);
    $image_path = BASE_URL . '/public/uploads/banners/' . htmlspecialchars($banner_to_display['image_filename']);
    $title = htmlspecialchars($banner_to_display['title']);
    $target = $banner_to_display['link_target'] === '_blank' ? ' target="_blank" rel="noopener"' : '';
    
    $banner_class = 'banner-item banner-wide'; 
    if (!empty($css_class)) {
        $banner_class .= ' ' . $css_class;
    }
    
    $output = '';
    if ($banner_to_display['link_type'] !== 'none' && !empty($banner_url) && $banner_url !== '#') {
        $output .= '<div class="' . $banner_class . '">';
        $output .= '<div class="banner-container">';
        $output .= '<a href="' . htmlspecialchars($banner_url) . '"' . $target . ' class="banner-link">';
        if (strtolower(pathinfo($banner_to_display['image_filename'], PATHINFO_EXTENSION)) === 'webm') {
            $output .= '<video src="' . $image_path . '" alt="' . $title . '" class="banner-image banner-video" autoplay loop muted playsinline loading="lazy"></video>';
        } else {
            $output .= '<img src="' . $image_path . '" alt="' . $title . '" class="banner-image" loading="lazy">';
        }
        $output .= '</a>';
        $output .= '<div class="banner-label">Destaque</div>';
        $output .= '</div>';
        $output .= '</div>';
    } else {
        $output .= '<div class="' . $banner_class . '">';
        $output .= '<div class="banner-container">';
        if (strtolower(pathinfo($banner_to_display['image_filename'], PATHINFO_EXTENSION)) === 'webm') {
            $output .= '<video src="' . $image_path . '" alt="' . $title . '" class="banner-image banner-video" autoplay loop muted playsinline loading="lazy"></video>';
        } else {
            $output .= '<img src="' . $image_path . '" alt="' . $title . '" class="banner-image" loading="lazy">';
        }
        $output .= '<div class="banner-label">Destaque</div>';
        $output .= '</div>';
        $output .= '</div>';
    }
    
    return $output;
}

function display_wide_banner($css_class = 'mb-4') {
    return display_banners('homepage_wide', $css_class);
}

function display_small_banner($css_class = 'mb-3') {
    return display_banners('product_small', $css_class);
}

function get_banner_styles() {
    return '
<style>
.banner-item {
    margin-bottom: 1rem;
    text-align: center;
    width: 100%;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
}

.banner-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.banner-wide {
    width: 100%;
    max-width: 100%;
}

.banner-small {
    width: 100%;
    max-width: 100%;
}

.banner-link {
    display: block;
    text-decoration: none;
    transition: opacity 0.3s ease;
    width: 100%;
}

.banner-link:hover {
    opacity: 0.9;
}

.banner-image {
    width: 100% !important;
    max-width: 100% !important;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    object-fit: cover;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.banner-video {
    width: 100% !important;
    max-width: 100% !important;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: cover;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.banner-link:hover .banner-image {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.banner-label {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(59, 130, 246, 0.9);
    color: white;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 9999px;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    z-index: 10;
}

.banner-label:hover {
    transform: scale(1.05);
}

@media (min-width: 640px) {
    .banner-label {
        top: 12px;
        left: 12px;
        font-size: 13px;
        padding: 6px 12px;
    }
}

@media (max-width: 768px) {
    .banner-item {
        padding: 0 0.5rem;
    }
    
    .banner-image {
        border-radius: 6px;
    }
}

@media (min-width: 1200px) {
    .banner-item {
        padding: 0 2rem;
    }
}
</style>
';
}

function init_banner_display() {
    echo get_banner_styles();
}
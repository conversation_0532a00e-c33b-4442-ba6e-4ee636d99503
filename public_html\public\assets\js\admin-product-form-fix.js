

document.addEventListener('DOMContentLoaded', function() {
    
    const productForm = document.querySelector('form[action*="section=products"]');
    
    if (!productForm) {
        console.warn('Product form not found on this page');
        return;
    }
    
    
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('sid');
    
    
    const csrfTokenInput = productForm.querySelector('input[name="csrf_token"]');
    
    if (!csrfTokenInput) {
        return;
    }
    
    
    const originalSubmit = productForm.submit;
    
    
    productForm.submit = function() {
        
        if (!csrfTokenInput.value) {
            alert('Erro de segurança: Token CSRF em falta. Por favor, recarregue a página e tente novamente.');
            return false;
        }
        
        
        originalSubmit.apply(this, arguments);
    };
    
    
    productForm.addEventListener('submit', function(event) {
        
        if (!csrfTokenInput.value) {
            event.preventDefault();
            alert('Erro de segurança: Token CSRF em falta. Por favor, recarregue a página e tente novamente.');
            return false;
        }
        
        
        if (sessionId && !productForm.querySelector('input[name="sid"]')) {
            
            const sidInput = document.createElement('input');
            sidInput.type = 'hidden';
            sidInput.name = 'sid';
            sidInput.value = sessionId;
            productForm.appendChild(sidInput);
        }
        
        
        return true;
    });
});

<?php

display_flash_messages();
?>

<h1>Configuraç<PERSON>es da Loja</h1>
<hr>

<form method="POST" action="admin.php?section=settings&<?= get_session_id_param() ?>" enctype="multipart/form-data" class="settings-form">
    <?= csrf_input_field() ?>

    <!-- General Settings -->
    <div class="card mb-4 collapsible-card" id="general-settings-card">
        <div class="card-header">Geral</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="setting_store_name" class="form-label">Nome da Loja *</label>
                <input type="text" class="form-control" id="setting_store_name" name="settings[store_name]" value="<?= sanitize_input($settings['store_name'] ?? '') ?>" required>
            </div>
             <div class="mb-3">
                <label for="setting_admin_email" class="form-label">Email do Administrador *</label>
                <input type="email" class="form-control" id="setting_admin_email" name="settings[admin_email]" value="<?= sanitize_input($settings['admin_email'] ?? '') ?>" required>
                 <div class="form-text">Usado para notificações.</div>
            </div>
            <div class="mb-3">
                <label for="setting_store_description" class="form-label">Descrição da Loja (Rodapé)</label>
                <textarea class="form-control" id="setting_store_description" name="settings[store_description]" rows="3"><?= sanitize_input($settings['store_description'] ?? '') ?></textarea>
                <div class="form-text">Uma breve descrição exibida no rodapé.</div>
            </div>
            <div class="mb-3">
                <label for="setting_digital_license_text" class="form-label">Texto de Licença para Produtos Digitais</label>
                <textarea class="form-control" id="setting_digital_license_text" name="settings[digital_license_text]" rows="4"><?= sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')) ?></textarea>
                <div class="form-text">Texto de licença que será exibido para produtos digitais e incluído nos emails de licença.</div>
            </div>
            <div class="mb-3">
                <label for="setting_footer_info_line1" class="form-label">Informação Importante 1 (Rodapé)</label>
                <textarea class="form-control" id="setting_footer_info_line1" name="settings[footer_info_line1]" rows="2"><?= sanitize_input($settings['footer_info_line1'] ?? '') ?></textarea>
                <div class="form-text">Primeira linha de informação importante exibida em itálico no rodapé (ex: "Todos os produtos estão isentos de IVA ao abrigo do Regime de Isenção - Art. 53").</div>
            </div>
            <div class="mb-3">
                <label for="setting_footer_info_line2" class="form-label">Informação Importante 2 (Rodapé)</label>
                <textarea class="form-control" id="setting_footer_info_line2" name="settings[footer_info_line2]" rows="2"><?= sanitize_input($settings['footer_info_line2'] ?? '') ?></textarea>
                <div class="form-text">Segunda linha de informação importante exibida em itálico no rodapé (ex: "Para sua segurança e privacidade, este website não usa cookies!").</div>
            </div>
            <div class="mb-3">
                <label for="setting_items_per_page" class="form-label">Items por Página (Frontend)</label>
                <input type="number" class="form-control" id="setting_items_per_page" name="settings[items_per_page]" value="<?= sanitize_input(get_setting('items_per_page', 12)) ?>" min="1" required>
                <div class="form-text">Número de produtos a exibir por página na listagem principal.</div>
            </div>
            <hr>
            <h5>Informações de Contacto (Rodapé e Página de Contacto)</h5>
             <div class="mb-3">
                <label for="setting_contact_address" class="form-label">Morada</label>
                <input type="text" class="form-control" id="setting_contact_address" name="settings[contact_address]" value="<?= sanitize_input($settings['contact_address'] ?? '') ?>">
            </div>
             <div class="mb-3">
                <label for="setting_contact_phone" class="form-label">Telefone</label>
                <input type="text" class="form-control" id="setting_contact_phone" name="settings[contact_phone]" value="<?= sanitize_input($settings['contact_phone'] ?? '') ?>">
            </div>
             <div class="mb-3">
                 <label for="setting_contact_email" class="form-label">Email de Contacto</label>
                 <input type="email" class="form-control" id="setting_contact_email" name="settings[contact_email]" value="<?= sanitize_input($settings['contact_email'] ?? '') ?>">
             </div>
             <div class="mb-3">
                 <label for="setting_contact_whatsapp" class="form-label"><i class="ri-whatsapp-line me-2"></i>WhatsApp</label>
                 <input type="text" class="form-control" id="setting_contact_whatsapp" name="settings[contact_whatsapp]" value="<?= sanitize_input($settings['contact_whatsapp'] ?? '') ?>" placeholder="+351...">
                 <div class="form-text">Número completo com código do país (ex: +351912345678). Deixe em branco para não exibir.</div>
             </div>
             <div class="mb-3">
                 <label for="setting_contact_signal" class="form-label"><i class="ri-chat-1-line me-2"></i>Signal</label>
                 <input type="text" class="form-control" id="setting_contact_signal" name="settings[contact_signal]" value="<?= sanitize_input($settings['contact_signal'] ?? '') ?>" placeholder="+351...">
                 <div class="form-text">Número completo com código do país (ex: +351912345678). Deixe em branco para não exibir.</div>
             </div>

             <h5 class="mt-4">Mapa na Página de Contacto</h5>
             <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="setting_contact_map_latitude" class="form-label">Latitude</label>
                    <input type="text" class="form-control" id="setting_contact_map_latitude" name="settings[contact_map_latitude]" value="<?= sanitize_input($settings['contact_map_latitude'] ?? '38.7223') ?>">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="setting_contact_map_longitude" class="form-label">Longitude</label>
                    <input type="text" class="form-control" id="setting_contact_map_longitude" name="settings[contact_map_longitude]" value="<?= sanitize_input($settings['contact_map_longitude'] ?? '-9.1393') ?>">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="setting_contact_map_zoom" class="form-label">Nível de Zoom</label>
                    <input type="number" class="form-control" id="setting_contact_map_zoom" name="settings[contact_map_zoom]" value="<?= sanitize_input($settings['contact_map_zoom'] ?? '15') ?>" min="1" max="20">
                </div>
             </div>
             <div class="form-text mb-3">
                 Pode obter as coordenadas do seu local no <a href="https://www.latlong.net/" target="_blank">LatLong.net</a> ou no Google Maps (clique com o botão direito no mapa e selecione "O que há aqui?").
             </div>
             <hr>
             <h5>Informações Adicionais (Página de Produto)</h5>
             <div class="mb-3">
                 <label for="setting_product_shipping_info" class="form-label">Texto Info Envio</label>
                 <input type="text" class="form-control" id="setting_product_shipping_info" name="settings[product_shipping_info]" value="<?= sanitize_input($settings['product_shipping_info'] ?? 'Envio gratuito acima de 100€') ?>">
                 <div class="form-text">Texto exibido na página de detalhe do produto sobre envio.</div>
             </div>
             <div class="mb-3">
                 <label for="setting_product_return_policy" class="form-label">Texto Política Devolução</label>
                 <input type="text" class="form-control" id="setting_product_return_policy" name="settings[product_return_policy]" value="<?= sanitize_input($settings['product_return_policy'] ?? '30 dias para devolução') ?>">
                 <div class="form-text">Texto exibido na página de detalhe do produto sobre devoluções.</div>
             </div>
             <hr>
             <h5>Logo da Loja</h5>
             <div class="mb-3">
                 <label for="store_logo" class="form-label">Carregar Logo (PNG, JPG, GIF, SVG) - Logo branco em fundo transparente</label>
                 <input class="form-control" type="file" id="store_logo" name="store_logo" accept="image/png, image/jpeg, image/gif, image/svg+xml">
                 <?php
                 $current_logo_path = get_setting('store_logo_path');
                 if (!empty($current_logo_path) && file_exists(PROJECT_ROOT . '/' . $current_logo_path)):
                     $logo_url = BASE_URL . '/' . $current_logo_path . '?' . filemtime(PROJECT_ROOT . '/' . $current_logo_path);
                 ?>
                 <div class="mt-2">
                     <label class="form-label">Logo Atual:</label><br>
                     <img src="<?= sanitize_input($logo_url) ?>" alt="Logo Atual" style="max-height: 60px; background: #eee; padding: 5px; border: 1px solid #ddd;">
                     <div class="form-check mt-2">
                       <input class="form-check-input" type="checkbox" value="1" id="remove_store_logo" name="remove_store_logo">
                       <label class="form-check-label" for="remove_store_logo">
                         Remover logo atual
                       </label>
                     </div>
                 </div>
                 <?php else: ?>
                    <div class="form-text">Nenhum logo definido. Carregue um ficheiro para definir o logo da loja.</div>
                 <?php endif; ?>
             </div>
            <hr>
            <h5>Alterar Password de Admin</h5>
             <div class="mb-3">
                <label for="current_password" class="form-label">Password Atual</label>
                <input type="password" class="form-control" id="current_password" name="current_password">
                 <div class="form-text">Deixe em branco se não quiser alterar.</div>
            </div>
             <div class="mb-3">
                <label for="new_password" class="form-label">Nova Password</label>
                <input type="password" class="form-control" id="new_password" name="new_password">
            </div>
             <div class="mb-3">
                <label for="confirm_password" class="form-label">Confirmar Nova Password</label>
                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
            </div>
        </div>
    </div>

    <!-- Social Media Links -->
    <div class="card mb-4 collapsible-card" id="social-media-card">
        <div class="card-header">Redes Sociais (Rodapé)</div>
        <div class="card-body">
            <p class="form-text mb-3">Insira os links completos (URLs) para os seus perfis. Deixe em branco para não exibir o ícone.</p>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_social_facebook" class="form-label"><i class="ri-facebook-fill me-2"></i>Facebook</label>
                    <input type="url" class="form-control" id="setting_social_facebook" name="settings[social_facebook]" value="<?= sanitize_input($settings['social_facebook'] ?? '') ?>" placeholder="https://facebook.com/seu_perfil">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="setting_social_twitter" class="form-label"><i class="ri-twitter-fill me-2"></i>Twitter / X</label>
                    <input type="url" class="form-control" id="setting_social_twitter" name="settings[social_twitter]" value="<?= sanitize_input($settings['social_twitter'] ?? '') ?>" placeholder="https://twitter.com/seu_perfil">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_social_instagram" class="form-label"><i class="ri-instagram-fill me-2"></i>Instagram</label>
                    <input type="url" class="form-control" id="setting_social_instagram" name="settings[social_instagram]" value="<?= sanitize_input($settings['social_instagram'] ?? '') ?>" placeholder="https://instagram.com/seu_perfil">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="setting_social_linkedin" class="form-label"><i class="ri-linkedin-fill me-2"></i>LinkedIn</label>
                    <input type="url" class="form-control" id="setting_social_linkedin" name="settings[social_linkedin]" value="<?= sanitize_input($settings['social_linkedin'] ?? '') ?>" placeholder="https://linkedin.com/in/seu_perfil">
                </div>
            </div>
             <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_social_youtube" class="form-label"><i class="ri-youtube-fill me-2"></i>YouTube</label>
                    <input type="url" class="form-control" id="setting_social_youtube" name="settings[social_youtube]" value="<?= sanitize_input($settings['social_youtube'] ?? '') ?>" placeholder="https://youtube.com/seu_canal">
                </div>
                <!-- Add more social media inputs here if needed -->
            </div>
        </div>
    </div>

     <!-- Localization & Currency -->
    <div class="card mb-4 collapsible-card" id="localization-card">
        <div class="card-header">Localização e Moeda</div>
        <div class="card-body">
             <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_default_currency" class="form-label">Moeda Padrão</label>
                    <input type="text" class="form-control" id="setting_default_currency" name="settings[default_currency]" value="<?= sanitize_input($settings['default_currency'] ?? 'EUR') ?>" placeholder="EUR">
                </div>
                 <div class="col-md-6 mb-3">
                    <label for="setting_currency_symbol" class="form-label">Símbolo da Moeda</label>
                    <input type="text" class="form-control" id="setting_currency_symbol" name="settings[currency_symbol]" value="<?= sanitize_input($settings['currency_symbol'] ?? '€') ?>" placeholder="€">
                </div>
            </div>
        </div>
    </div>

    <!-- Shipping & Tax -->
    <div class="card mb-4 collapsible-card" id="shipping-tax-card">
        <div class="card-header">Envio e Impostos</div>
        <div class="card-body">
            <h5>Configurações de Envio</h5>
            <div class="mb-3">
                <label for="setting_min_order_value" class="form-label">Valor Mínimo para Checkout (EUR)</label>
                <input type="number" step="0.01" min="0" class="form-control" id="setting_min_order_value" name="settings[min_order_value]" value="<?= sanitize_input(get_setting('min_order_value', 0)) ?>">
                <div class="form-text">Valor mínimo para permitir finalizar a compra. Defina como 0 para permitir qualquer valor.</div>
            </div>
            <div class="mb-3">
                <label for="setting_free_shipping_threshold" class="form-label">Valor Mínimo para Envio Gratuito (EUR)</label>
                <input type="number" step="0.01" min="0" class="form-control" id="setting_free_shipping_threshold" name="settings[free_shipping_threshold]" value="<?= sanitize_input(get_setting('free_shipping_threshold', 0)) ?>">
                <div class="form-text">Valor mínimo para oferecer envio gratuito. Defina como 0 para sempre oferecer envio gratuito.</div>
            </div>

            <div class="mb-3">
                <label for="setting_fixed_shipping_cost" class="form-label">Custo de Envio Fixo (Não-Digitais) (EUR)</label>
                <input type="number" step="0.01" min="0" class="form-control" id="setting_fixed_shipping_cost" name="settings[fixed_shipping_cost]" value="<?= sanitize_input(get_setting('fixed_shipping_cost', '5.00')) ?>">
                <div class="form-text">Custo de envio padrão para todos os itens físicos/não-digitais. Defina como 0 se não houver custo fixo ou se for calculado de outra forma.</div>
            </div>

            <hr>

            <h5>Configurações de NIF/VAT ID</h5>
            <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" role="switch" id="setting_vat_id_required_enabled"
                       name="settings[vat_id_required_enabled]" value="1"
                       <?= (get_setting('vat_id_required_enabled', '1') == '1') ? 'checked' : '' ?>>
                <label class="form-check-label" for="setting_vat_id_required_enabled">
                    Ativar obrigatoriedade do NIF baseada no valor
                </label>
                <div class="form-text">Se desativado, o NIF será sempre facultativo independentemente do valor da encomenda.</div>
            </div>
            <div class="mb-3">
                <label for="setting_vat_id_threshold" class="form-label">Valor Mínimo para NIF Obrigatório (EUR)</label>
                <input type="number" step="0.01" min="0" class="form-control" id="setting_vat_id_threshold" name="settings[vat_id_threshold]" value="<?= sanitize_input(get_setting('vat_id_threshold', 1000)) ?>">
                <div class="form-text">O NIF será obrigatório para encomendas acima deste valor quando a opção acima estiver ativada. Defina como 0 para tornar sempre obrigatório.</div>
            </div>

            <hr>

            <h5>Gestão de Taxas de IVA</h5>
            <p class="form-text mb-3">Configure as diferentes taxas de IVA disponíveis para seleção nos produtos.</p>

            <?php

            require_once __DIR__ . '/../../includes/vat_functions.php';

            $vat_rates = get_vat_rates();
            ?>

            <div class="table-responsive">
                <table class="table table-striped table-hover" id="vat-rates-table">
                    <thead>
                        <tr>
                            <th>Taxa (%)</th>
                            <th>Descrição</th>
                            <th>Padrão</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="vat-rates-table-body">
                        <?php if ($vat_rates && count($vat_rates) > 0): ?>
                            <?php foreach ($vat_rates as $rate): ?>
                                <tr data-rate-id="<?= $rate['id'] ?>">
                                    <td><?= number_format($rate['rate'], 1, ',', '.') ?>%</td>
                                    <td><?= sanitize_input($rate['description']) ?></td>
                                    <td><?= $rate['is_default'] ? '<span class="badge bg-success">Sim</span>' : '<span class="badge bg-secondary">Não</span>' ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary edit-vat-rate"
                                                    title="Editar Taxa"
                                                    data-id="<?= $rate['id'] ?>"
                                                    data-rate="<?= sanitize_input($rate['rate']) ?>"
                                                    data-description="<?= sanitize_input($rate['description']) ?>"
                                                    data-default="<?= $rate['is_default'] ? '1' : '0' ?>">
                                                <i class="fas fa-edit"></i> Editar
                                            </button>
                                            <?php if (!$rate['is_default']): ?>
                                                <button type="button" class="btn btn-sm btn-success set-default-vat"
                                                        title="Definir como Padrão"
                                                        data-id="<?= $rate['id'] ?>">
                                                    <i class="fas fa-check"></i> Definir Padrão
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-vat-rate"
                                                        title="Eliminar Taxa"
                                                        data-id="<?= $rate['id'] ?>">
                                                    <i class="fas fa-trash"></i> Eliminar
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center">Nenhuma taxa de IVA definida.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <button type="button" class="btn btn-success mt-3" id="add-vat-rate-btn">
                <i class="fas fa-plus"></i> Adicionar Nova Taxa de IVA
            </button>

            <!-- Delete Confirmation Modal -->
            <div class="modal fade" id="deleteVatRateModal" tabindex="-1" aria-labelledby="deleteVatRateModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteVatRateModalLabel">Confirmar Eliminação</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Tem certeza que deseja eliminar esta taxa de IVA?</p>
                            <p class="text-danger">Esta ação não pode ser desfeita. Os produtos que usam esta taxa serão atualizados para usar a taxa padrão.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" >Cancelar</button>
                            <button type="button" class="btn btn-danger" id="confirm-delete-vat-rate-btn">Eliminar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

     <!-- Email Settings -->
    <div class="card mb-4 collapsible-card" id="email-settings-card">
        <div class="card-header">Configurações de Email (SMTP)</div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_smtp_host" class="form-label">Host SMTP</label>
                    <input type="text" class="form-control" id="setting_smtp_host" name="settings[smtp_host]" value="<?= sanitize_input($settings['smtp_host'] ?? '') ?>">
                </div>
                 <div class="col-md-6 mb-3">
                    <label for="setting_smtp_port" class="form-label">Porta SMTP</label>
                    <input type="number" class="form-control" id="setting_smtp_port" name="settings[smtp_port]" value="<?= sanitize_input($settings['smtp_port'] ?? '587') ?>">
                </div>
            </div>
             <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_smtp_username" class="form-label">Utilizador SMTP</label>
                    <input type="text" class="form-control" id="setting_smtp_username" name="settings[smtp_username]" value="<?= sanitize_input($settings['smtp_username'] ?? '') ?>">
                </div>
                 <div class="col-md-6 mb-3">
                    <label for="setting_smtp_password" class="form-label">Password SMTP</label>
                    <input type="password" class="form-control" id="setting_smtp_password" name="settings[smtp_password]" placeholder="Deixe em branco para não alterar">
                </div>
            </div>
             <div class="mb-3">
                <label for="setting_smtp_secure" class="form-label">Segurança SMTP</label>
                <select class="form-select" id="setting_smtp_secure" name="settings[smtp_secure]">
                    <option value="" <?= (($settings['smtp_secure'] ?? '') == '') ? 'selected' : '' ?>>Nenhuma</option>
                    <option value="tls" <?= (($settings['smtp_secure'] ?? 'tls') == 'tls') ? 'selected' : '' ?>>TLS</option>
                    <option value="ssl" <?= (($settings['smtp_secure'] ?? '') == 'ssl') ? 'selected' : '' ?>>SSL</option>
                </select>
            </div>
             <hr>
             <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_from_email" class="form-label">Email Remetente</label>
                    <input type="email" class="form-control" id="setting_from_email" name="settings[from_email]" value="<?= sanitize_input($settings['from_email'] ?? '') ?>">
                </div>
                 <div class="col-md-6 mb-3">
                    <label for="setting_from_name" class="form-label">Nome Remetente</label>
                    <input type="text" class="form-control" id="setting_from_name" name="settings[from_name]" value="<?= sanitize_input($settings['from_name'] ?? '') ?>">
                </div>
            </div>
             <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_reply_to_email" class="form-label">Email de Resposta</label>
                    <input type="email" class="form-control" id="setting_reply_to_email" name="settings[reply_to_email]" value="<?= sanitize_input($settings['reply_to_email'] ?? '') ?>">
                </div>
                 <div class="col-md-6 mb-3">
                    <label for="setting_reply_to_name" class="form-label">Nome de Resposta</label>
                    <input type="text" class="form-control" id="setting_reply_to_name" name="settings[reply_to_name]" value="<?= sanitize_input($settings['reply_to_name'] ?? '') ?>">
                </div>
            </div>
            <p><strong>Nota:</strong> A funcionalidade de edição de templates de email será implementada separadamente.</p>
        </div>
    </div>

    <!-- Order Email Notifications -->
    <div class="card mb-4 collapsible-card" id="order-email-card">
        <div class="card-header">Notificações de Email para Encomendas</div>
        <div class="card-body">
            <p class="mb-3">Configure as notificações de email enviadas aos clientes quando o estado das encomendas muda.</p>

            <!-- Status Change Notification -->
            <div class="mb-4">
                <h5>Alteração de Estado</h5>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="order_email_status_change_enabled"
                           name="settings[order_email_status_change_enabled]" value="1"
                           <?= (get_setting('order_email_status_change_enabled', '1') == '1') ? 'checked' : '' ?>>
                    <label class="form-check-label" for="order_email_status_change_enabled">
                        Ativar notificações de alteração de estado
                    </label>
                </div>
                <div class="mb-3">
                    <label for="order_email_template_status_change" class="form-label">Template de Email</label>
                    <textarea class="form-control summernote-editor" id="order_email_template_status_change"
                              name="settings[order_email_template_status_change]" rows="5"><?= sanitize_input(get_setting('order_email_template_status_change',
'<p>Olá {customer_first_name},</p>
<p>O estado da sua encomenda #{order_number} foi alterado para <strong>{order_status}</strong>.</p>
<p>Pode consultar os detalhes da sua encomenda a qualquer momento através do nosso site.</p>
<p>Cumprimentos,<br>{store_name}</p>')) ?></textarea>
                    <div class="form-text">
                        Placeholders disponíveis: {customer_first_name}, {customer_last_name}, {order_number},
                        {order_date}, {order_status}, {store_name}
                    </div>
                </div>
            </div>

            <!-- Tracking Number Update Notification -->
            <div class="mb-4">
                <h5>Atualização de Número de Tracking</h5>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="order_email_tracking_update_enabled"
                           name="settings[order_email_tracking_update_enabled]" value="1"
                           <?= (get_setting('order_email_tracking_update_enabled', '1') == '1') ? 'checked' : '' ?>>
                    <label class="form-check-label" for="order_email_tracking_update_enabled">
                        Ativar notificações de atualização de tracking
                    </label>
                </div>
                <div class="mb-3">
                    <label for="order_email_template_tracking_update" class="form-label">Template de Email</label>
                    <textarea class="form-control summernote-editor" id="order_email_template_tracking_update"
                              name="settings[order_email_template_tracking_update]" rows="5"><?= sanitize_input(get_setting('order_email_template_tracking_update',
'<p>Olá {customer_first_name},</p>
<p>A sua encomenda #{order_number} foi enviada!</p>
<p>Pode acompanhar a sua encomenda com o número de tracking: <strong>{tracking_number}</strong></p>
{tracking_link}
<p>Cumprimentos,<br>{store_name}</p>')) ?></textarea>
                    <div class="form-text">
                        Placeholders disponíveis: {customer_first_name}, {customer_last_name}, {order_number},
                        {order_date}, {order_status}, {tracking_number}, {tracking_url}, {tracking_link}, {store_name}
                    </div>
                </div>
            </div>

            <!-- Order Created Notification -->
            <div class="mb-4">
                <h5>Criação de Encomenda</h5>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="order_email_created_enabled"
                           name="settings[order_email_created_enabled]" value="1"
                           <?= (get_setting('order_email_created_enabled', '1') == '1') ? 'checked' : '' ?>>
                    <label class="form-check-label" for="order_email_created_enabled">
                        Ativar notificações de criação de encomenda
                    </label>
                </div>
                <div class="form-text mb-3">
                    O template para emails de confirmação de encomenda é mais complexo e inclui detalhes dos produtos.
                    Utilize a função <code>send_order_confirmation_email()</code> para personalizar este email.
                </div>
            </div>

            <!-- Order Anonymized Notification -->
            <div class="mb-4">
                <h5>Anonimização de Encomenda (RGPD)</h5>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="order_email_anonymized_enabled"
                           name="settings[order_email_anonymized_enabled]" value="1"
                           <?= (get_setting('order_email_anonymized_enabled', '1') == '1') ? 'checked' : '' ?>>
                    <label class="form-check-label" for="order_email_anonymized_enabled">
                        Ativar notificações de anonimização de dados
                    </label>
                </div>
                <div class="mb-3">
                    <label for="order_email_template_anonymized" class="form-label">Template de Email</label>
                    <textarea class="form-control summernote-editor" id="order_email_template_anonymized"
                              name="settings[order_email_template_anonymized]" rows="5"><?= sanitize_input(get_setting('order_email_template_anonymized',
'<p>Olá {customer_first_name},</p>
<p>Conforme solicitado, os seus dados pessoais relacionados à encomenda #{order_number} foram anonimizados em nosso sistema.</p>
<p>Esta ação foi realizada em conformidade com o Regulamento Geral de Proteção de Dados (RGPD).</p>
<p>Cumprimentos,<br>{store_name}</p>')) ?></textarea>
                    <div class="form-text">
                        Placeholders disponíveis: {customer_first_name}, {customer_last_name}, {order_number},
                        {order_date}, {store_name}
                    </div>
                </div>
            </div>

            <!-- License Verification Email Template -->
            <div class="mb-4">
                <h5>Email de Verificação de Licença</h5>
                <div class="mb-3">
                    <label for="license_verification_email_template" class="form-label">Template de Email</label>
                    <textarea class="form-control summernote-editor" id="license_verification_email_template"
                              name="settings[license_verification_email_template]" rows="8"><?= sanitize_input(get_setting('license_verification_email_template',
'Olá {customer_name},

Seu código de verificação para acessar os detalhes da licença {license_code} é:

**{verification_code}**

Este código expira em 15 minutos.

Se você não solicitou este código, ignore este email.')) ?></textarea>
                    <div class="form-text">
                        Placeholders disponíveis: {customer_name}, {license_code}, {verification_code}
                    </div>
                </div>
            </div>

            <!-- Digital Product Terms Email Notification -->
            <div class="mb-4">
                <h5>Email de Termos de Produtos Digitais</h5>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="setting_digital_terms_email_enabled"
                           name="settings[digital_terms_email_enabled]" value="1"
                           <?= (get_setting('digital_terms_email_enabled', '0') == '1') ? 'checked' : '' ?>>
                    <label class="form-check-label" for="setting_digital_terms_email_enabled">
                        Ativar envio de email com termos para produtos digitais
                    </label>
                    <div class="form-text">Se ativo, um email separado será enviado ao cliente com os termos e condições aceites durante o checkout para cada produto digital encomendado.</div>
                </div>
                <div class="mb-3">
                    <label for="digital_terms_email_subject" class="form-label">Assunto do Email de Termos</label>
                    <input type="text" class="form-control" id="digital_terms_email_subject"
                           name="settings[digital_terms_email_subject]"
                           value="<?= sanitize_input(get_setting('digital_terms_email_subject', 'Termos e Condições da sua Encomenda Digital #{order_number}')) ?>">
                    <div class="form-text">
                        Placeholders disponíveis: {customer_first_name}, {customer_last_name}, {order_number}, {store_name}
                    </div>
                </div>

                <?php
                
                $all_checkout_terms_pages = [];
                try {
                    
                    if (!isset($pdo) || !$pdo instanceof PDO) {
                        
                        $pdo = get_db_connection();
                    }

                    if ($pdo) {
                        $sql = "SELECT id, title_pt
                                FROM pages
                                WHERE (require_agreement_checkout = 1 OR require_agreement_digital_checkout = 1)
                                  AND is_active = 1
                                ORDER BY title_pt ASC";
                        $stmt_terms = $pdo->query($sql);
                        if ($stmt_terms) {
                            $all_checkout_terms_pages = $stmt_terms->fetchAll(PDO::FETCH_ASSOC);
                        } else {
                        }
                    } else {
                    }
                } catch (PDOException $e) {
                }

                $selected_terms_for_email_setting = get_setting('digital_terms_email_selected_pages', []);
                if (is_string($selected_terms_for_email_setting)) {
                    $decoded_selection = json_decode($selected_terms_for_email_setting, true);
                    $selected_terms_for_email_setting = is_array($decoded_selection) ? $decoded_selection : [];
                } elseif (!is_array($selected_terms_for_email_setting)) {
                    $selected_terms_for_email_setting = [];
                }
                
                $selected_terms_for_email_setting = array_map('strval', $selected_terms_for_email_setting);

                ?>
                <div class="mb-3">
                    <label for="setting_digital_terms_email_selected_pages" class="form-label">Termos Obrigatórios a Incluir no Email de Encomenda</label>
                    <select multiple class="form-select" id="setting_digital_terms_email_selected_pages" name="settings[digital_terms_email_selected_pages][]" size="8" aria-describedby="setting_digital_terms_email_selected_pages_help">
                        <?php if (!empty($all_checkout_terms_pages)): ?>
                            <?php foreach ($all_checkout_terms_pages as $term_page): ?>
                                <option value="<?= $term_page['id'] ?>" <?= in_array((string)$term_page['id'], $selected_terms_for_email_setting, true) ? 'selected' : '' ?>>
                                    <?= sanitize_input($term_page['title_pt']) ?> (ID: <?= $term_page['id'] ?>)
                                </option>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <option value="" disabled>Nenhuma página de termos (que requeira acordo no checkout) configurada ou ativa.</option>
                        <?php endif; ?>
                    </select>
                    <div id="setting_digital_terms_email_selected_pages_help" class="form-text">
                        Selecione quais Termos e Condições (previamente marcados como 'requer acordo no checkout' e ativos) devem ser enviados no email de confirmação da encomenda (especialmente útil para produtos digitais, mas aplicável a todos os termos acordados). O conteúdo será enviado em texto simples. Mantenha CTRL (ou Command no Mac) pressionado para selecionar múltiplos.
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Blog Settings -->
    <div class="card mb-4 collapsible-card" id="blog-settings-card">
        <div class="card-header">Configurações do Blog</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="setting_blog_posts_per_page" class="form-label">Posts por Página</label>
                <input type="number" class="form-control" id="setting_blog_posts_per_page" name="settings[blog_posts_per_page]" value="<?= sanitize_input(get_setting('blog_posts_per_page', 5)) ?>" min="1" required>
                <div class="form-text">Número de posts a exibir por página na listagem do blog.</div>
            </div>
            <hr>
            <h5>Slider de Posts na Página Inicial</h5>
            <div class="mb-3">
                <label for="setting_blog_slider_count" class="form-label">Número de Posts no Slider</label>
                <input type="number" class="form-control" id="setting_blog_slider_count" name="settings[blog_slider_count]" value="<?= sanitize_input(get_setting('blog_slider_count', 3)) ?>" min="1" required>
                <div class="form-text">Número de posts a exibir no slider da página inicial. Apenas os posts mais recentes serão exibidos.</div>
            </div>
            <div class="mb-3">
                <label for="setting_blog_slider_delay" class="form-label">Tempo de Pausa do Slider (segundos)</label>
                <input type="number" class="form-control" id="setting_blog_slider_delay" name="settings[blog_slider_delay]" value="<?= sanitize_input(get_setting('blog_slider_delay', 5)) ?>" min="1" required>
                <div class="form-text">Tempo de pausa em segundos entre cada slide no slider de posts da página inicial.</div>
            </div>
        </div>
    </div>

    <!-- SEO & Sitemap Settings -->
    <div class="card mb-4 collapsible-card" id="seo-sitemap-settings-card">
        <div class="card-header">SEO & Sitemaps</div>
        <div class="card-body">
            <h5>Configurações de Sitemaps</h5>
            <div class="mb-3">
                <label for="setting_sitemap_default_directory" class="form-label">Diretório Padrão para Sitemaps</label>
                <input type="text" class="form-control" id="setting_sitemap_default_directory" name="settings[sitemap_default_directory]" value="<?= sanitize_input(get_setting('sitemap_default_directory', '')) ?>">
                <div class="form-text">Diretório padrão para salvar sitemaps e XMLs. Pode ser absoluto ou relativo ao diretório raiz do projeto. Deixe em branco para usar o diretório raiz.</div>
            </div>
            <div class="mb-3">
                <a href="admin.php?section=sitemaps&<?= get_session_id_param() ?>" class="btn btn-outline-primary">
                    <i class="bi bi-diagram-3"></i> Gerir Sitemaps e XMLs
                </a>
                <div class="form-text mt-2">Configure e gere sitemaps e feeds XML para motores de busca e Google Merchant.</div>
            </div>
            <hr>
            <h5>Nuvem de Palavras-chave de Produtos</h5>
            <div class="mb-3">
                <label for="setting_keyword_cloud_max_keywords" class="form-label">Número Máximo de Palavras-chave (Produtos)</label>
                <input type="number" class="form-control" id="setting_keyword_cloud_max_keywords" name="settings[keyword_cloud_max_keywords]" value="<?= sanitize_input(get_setting('keyword_cloud_max_keywords', 20)) ?>" min="1" required>
                <div class="form-text">Define o número máximo de palavras-chave a serem exibidas na nuvem de palavras-chave na página de produto.</div>
            </div>
            <hr>
            <h5>Nuvem de Palavras-chave de Páginas</h5>
            <div class="mb-3">
                <label for="setting_page_keyword_cloud_max_keywords" class="form-label">Número Máximo de Palavras-chave (Páginas)</label>
                <input type="number" class="form-control" id="setting_page_keyword_cloud_max_keywords" name="settings[page_keyword_cloud_max_keywords]" value="<?= sanitize_input(get_setting('page_keyword_cloud_max_keywords', 15)) ?>" min="1">
                <div class="form-text">Define o número máximo de palavras-chave a serem exibidas na nuvem de palavras-chave no final das páginas. Deixe em branco ou 0 para usar o valor padrão (15).</div>
            </div>
        </div>
    </div>

    <!-- Watermark Settings -->
    <div class="card mb-4 collapsible-card" id="watermark-settings-card">
        <div class="card-header">Marca d'Água (Watermark)</div>
        <div class="card-body">
            <div class="form-check form-switch mb-3">
              <input type="hidden" name="settings[watermark_enabled]" value="0"> <!-- Hidden input for unchecked value -->
              <input class="form-check-input" type="checkbox" role="switch" id="setting_watermark_enabled" name="settings[watermark_enabled]" value="1" <?= (get_setting('watermark_enabled', 0) == 1) ? 'checked' : '' ?>> <!-- Also corrected the comparison here -->
              <label class="form-check-label" for="setting_watermark_enabled">Ativar marca d'água nas imagens dos produtos</label>
            </div>
            <div class="mb-3">
                <label for="watermark_image" class="form-label">Carregar Imagem da Marca d'Água (PNG)</label>
                <input class="form-control" type="file" id="watermark_image" name="watermark_image" accept="image/png">
                <div class="form-text">Selecione um ficheiro PNG transparente. Se existir, a imagem atual será substituída: <code><?= WATERMARK_IMAGE_PATH ?></code></div>
                <?php if (file_exists(WATERMARK_IMAGE_PATH)): ?>
                    <img src="<?= BASE_URL . '/public/assets/images/watermark/watermark.png?' . time() ?>" alt="Watermark Atual" style="max-height: 50px; background: #ccc; margin-top: 10px;">
                <?php endif; ?>
            </div>
             <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="setting_watermark_opacity" class="form-label">Opacidade (0.0 - 1.0)</label>
                    <input type="number" step="0.1" min="0" max="1" class="form-control" id="setting_watermark_opacity" name="settings[watermark_opacity]" value="<?= sanitize_input($settings['watermark_opacity'] ?? '0.5') ?>">
                </div>
                 <div class="col-md-6 mb-3">
                    <label for="setting_watermark_position" class="form-label">Posição</label>
                    <select class="form-select" id="setting_watermark_position" name="settings[watermark_position]">
                        <option value="center" <?= (($settings['watermark_position'] ?? 'center') == 'center') ? 'selected' : '' ?>>Centro</option>
                        <option value="top-left" <?= (($settings['watermark_position'] ?? '') == 'top-left') ? 'selected' : '' ?>>Topo Esquerdo</option>
                        <option value="top-right" <?= (($settings['watermark_position'] ?? '') == 'top-right') ? 'selected' : '' ?>>Topo Direito</option>
                        <option value="bottom-left" <?= (($settings['watermark_position'] ?? '') == 'bottom-left') ? 'selected' : '' ?>>Fundo Esquerdo</option>
                        <option value="bottom-right" <?= (($settings['watermark_position'] ?? '') == 'bottom-right') ? 'selected' : '' ?>>Fundo Direito</option>
                        <!-- Add more positions if needed -->
                    </select>
                </div>
                 <div class="col-md-6 mb-3">
                     <label for="setting_watermark_size_percent" class="form-label">Tamanho (% da Largura da Imagem)</label>
                     <input type="number" step="1" min="1" max="100" class="form-control" id="setting_watermark_size_percent" name="settings[watermark_size_percent]" value="<?= sanitize_input($settings['watermark_size_percent'] ?? '15') ?>">
                     <div class="form-text">A largura da marca d'água como percentagem da largura da imagem alvo.</div>
                 </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <button type="submit" class="btn btn-primary" id="save-settings-btn" onclick="document.querySelector('.settings-form').submit(); return false;">Guardar Configurações</button>
    </div>

</form>

<!-- VAT Rate Modal - Placed OUTSIDE the main settings form to prevent event interference -->
<div class="modal fade" id="vatRateModal" tabindex="-1" aria-labelledby="vatRateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="vatRateModalLabel">Adicionar Taxa de IVA</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Separate form that's not nested within the main settings form -->
                <form id="vat-rate-form" onsubmit="return false;" data-form-type="vat-rate">
                    <?= csrf_input_field() ?>
                    <input type="hidden" id="vat_rate_id" name="vat_rate_id" value="">
                    <div class="mb-3">
                        <label for="vat_rate" class="form-label">Taxa (%)</label>
                        <input type="number" step="0.1" min="0" class="form-control" id="vat_rate" name="vat_rate" required>
                    </div>
                    <div class="mb-3">
                        <label for="vat_description" class="form-label">Descrição</label>
                        <input type="text" class="form-control" id="vat_description" name="vat_description" required>
                        <div class="form-text">Ex: "Taxa Normal", "Taxa Reduzida", "Isento - Artigo 9º", etc.</div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="vat_is_default" name="vat_is_default" value="1">
                        <label class="form-check-label" for="vat_is_default">
                            Definir como taxa padrão
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="save-vat-rate-btn">Guardar</button>
            </div>
        </div>
    </div>
</div>

<!-- All VAT Rates JavaScript logic is now handled solely by public/assets/js/admin-vat-rates.js -->
<!-- The initVatRateManagement function is called by admin-navigation.js when the settings section is loaded via AJAX -->

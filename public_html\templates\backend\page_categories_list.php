<?php

?>

<h1><PERSON><PERSON><PERSON> Categorias de Páginas</h1>
<a href="admin.php?section=page_categories&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Criar Nova Categoria
</a>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Lista de Categorias</h5>
        <?php if (!empty($categories)): ?>
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome</th>
                        <th>Slug</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category): ?>
                        <tr>
                            <td><?= htmlspecialchars($category['id']) ?></td>
                            <td><?= htmlspecialchars($category['name']) ?></td>
                            <td><?= htmlspecialchars($category['slug']) ?></td>
                            <td>
                                <a href="admin.php?section=page_categories&action=edit&id=<?= $category['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-primary me-1" title="Editar">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <a href="admin.php?section=page_categories&action=delete&id=<?= $category['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-danger" title="Eliminar" onclick="return confirm('Tem a certeza que deseja eliminar esta categoria? As páginas associadas ficarão sem categoria.');">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>Não existem categorias de páginas criadas.</p>
        <?php endif; ?>
    </div>
</div>

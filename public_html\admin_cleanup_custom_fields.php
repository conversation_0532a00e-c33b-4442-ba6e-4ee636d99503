<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/custom_field_cleanup.php';

if (!is_admin_logged_in()) {
    header('Location: admin.php?action=login&' . get_session_id_param());
    exit;
}

header('Content-Type: text/plain');

echo "=== Custom Fields Cleanup Script ===\n\n";

echo "Step 1: Cleaning up duplicate custom fields...\n";
$cleanup_result = cleanup_duplicate_custom_fields();

echo "Duplicates found: " . $cleanup_result['duplicates_found'] . "\n";
echo "Fields merged: " . $cleanup_result['fields_merged'] . "\n";

if (!empty($cleanup_result['errors'])) {
    echo "\nErrors encountered:\n";
    foreach ($cleanup_result['errors'] as $error) {
        echo "- $error\n";
    }
    echo "\nCleanup process completed with errors.\n";
    exit;
}

echo "\nDuplicate custom fields cleanup completed successfully.\n";

echo "\nStep 2: Adding unique constraint to custom_fields table...\n";
$constraint_result = add_unique_constraint_to_custom_fields();

if ($constraint_result) {
    echo "Unique constraint added successfully.\n";
} else {
    echo "Failed to add unique constraint. Please check the error log.\n";
    exit;
}

echo "\n=== Custom Fields Cleanup Completed ===\n";
echo "\nYou can now return to the admin panel: <a href='admin.php?section=custom_fields&" . get_session_id_param() . "'>Go to Custom Fields</a>\n";

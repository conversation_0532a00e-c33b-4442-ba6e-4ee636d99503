<?php
display_flash_messages();

$sessions_result = $sessions_result ?? ['success' => false, 'message' => 'Variável $sessions_result não foi definida no template.', 'sessions' => []];
$sessions = $sessions ?? [];
$csrf_token = $csrf_token ?? '';

$country_filter = $country_filter ?? '';
$ip_filter = $ip_filter ?? '';
$sort = $sort ?? 'recent';
$current_page = $current_page ?? 1;
$total_pages = $total_pages ?? 1;
$total_sessions = $total_sessions ?? 0;
$sessions_per_page = $sessions_per_page ?? 20;

$filters_active = !empty($country_filter) || !empty($ip_filter) || $sort !== 'recent';

?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <form method="post" action="admin.php?section=sessions&action=delete_expired_empty_sessions&<?= get_session_id_param() ?>"
                  onsubmit="return confirm('Tem a certeza que deseja eliminar todas as sessões expiradas e sem conteúdo no carrinho? Esta ação não pode ser desfeita.');"
                  style="display: inline-block; margin-right: 10px;">
                <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                <button type="submit" class="btn btn-warning">
                    <i class="bi bi-trash2"></i> Eliminar Expiradas e Vazias
                </button>
            </form>
            
            <form method="post" action="admin.php?section=sessions&action=delete_non_portuguese_sessions&<?= get_session_id_param() ?>"
                  onsubmit="return confirm('Tem a certeza que deseja eliminar todas as sessões de IPs externos a Portugal? Esta ação não pode ser desfeita.');"
                  style="display: inline-block;">
                <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-globe"></i> Eliminar Externos a PT
                </button>
            </form>
        </div>
        
        <!-- Select All and Bulk Actions -->
        <div class="d-flex align-items-center gap-2">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="select_all_table">
                <label class="form-check-label" for="select_all_table">
                    Selecionar Todas
                </label>
            </div>
            <button type="button" class="btn btn-danger btn-sm" id="bulk_delete_btn" disabled>
                <i class="bi bi-trash"></i> Eliminar Selecionadas
            </button>
        </div>
    </div>
    
    <h1>Gerir Sessões Ativas</h1>
    <p class="lead">Lista de todas as sessões de utilizador ativas no sistema.</p>
    
    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-header">
            <button class="btn btn-link p-0 text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="<?= $filters_active ? 'true' : 'false' ?>" aria-controls="filtersCollapse">
                <i class="bi bi-funnel"></i> Filtros
                <?php if ($filters_active): ?>
                    <span class="badge bg-primary ms-2">Filtros ativos</span>
                <?php endif; ?>
            </button>
            <?php if ($filters_active): ?>
                <a href="admin.php?section=sessions&<?= get_session_id_param() ?>" class="btn btn-outline-secondary btn-sm ms-2">
                    <i class="bi bi-x-circle"></i> Limpar Filtros
                </a>
            <?php endif; ?>
        </div>
        <div class="collapse <?= $filters_active ? 'show' : '' ?>" id="filtersCollapse">
            <div class="card-body">
                <form method="get" action="admin.php">
                    <input type="hidden" name="section" value="sessions">
                    <input type="hidden" name="<?= SESSION_PARAM_NAME ?>" value="<?= session_id() ?>">
                    
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="country" class="form-label">País</label>
                            <input type="text" class="form-control" id="country" name="country" value="<?= htmlspecialchars($country_filter) ?>" placeholder="Ex: Portugal, PT">
                        </div>
                        <div class="col-md-3">
                            <label for="ip" class="form-label">IP</label>
                            <input type="text" class="form-control" id="ip" name="ip" value="<?= htmlspecialchars($ip_filter) ?>" placeholder="Ex: 192.168">
                        </div>
                        <div class="col-md-3">
                            <label for="sort" class="form-label">Ordenar por</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="recent" <?= $sort === 'recent' ? 'selected' : '' ?>>Último Acesso (Recente)</option>
                                <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Último Acesso (Antigo)</option>
                                <option value="created_desc" <?= $sort === 'created_desc' ? 'selected' : '' ?>>Criação (Recente)</option>
                                <option value="created_asc" <?= $sort === 'created_asc' ? 'selected' : '' ?>>Criação (Antigo)</option>
                                <option value="ip_asc" <?= $sort === 'ip_asc' ? 'selected' : '' ?>>IP (A-Z)</option>
                                <option value="ip_desc" <?= $sort === 'ip_desc' ? 'selected' : '' ?>>IP (Z-A)</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i> Filtrar
                            </button>
                            <a href="admin.php?section=sessions&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Limpar
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php if (!($sessions_result['success'] ?? false)): ?>
        <div class="alert alert-danger">
            Erro ao carregar sessões: <?= htmlspecialchars($sessions_result['message'] ?? 'Erro desconhecido ao processar $sessions_result no template.') ?>
        </div>
    <?php elseif (empty($sessions)): ?>
        <div class="alert alert-info">
            <?php if ($filters_active): ?>
                Nenhuma sessão encontrada com os filtros aplicados. <a href="admin.php?section=sessions&<?= get_session_id_param() ?>">Limpar filtros</a>
            <?php else: ?>
                Não existem sessões ativas de momento.
            <?php endif; ?>
        </div>
    <?php else: ?>
        <!-- Results Summary -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <span class="text-muted">
                    Mostrando <?= count($sessions) ?> de <?= $total_sessions ?> sessões
                    <?php if ($total_pages > 1): ?>
                        (Página <?= $current_page ?> de <?= $total_pages ?>)
                    <?php endif; ?>
                </span>
            </div>
        </div>
        
        <!-- Bulk Delete Form -->
        <form id="bulk_delete_form" method="post" action="admin.php?section=sessions&action=bulk_delete&<?= get_session_id_param() ?>" style="display: none;">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            <input type="hidden" name="session_ids" id="bulk_session_ids" value="">
        </form>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th width="40">
                        </th>
                        <th>ID da Sessão</th>
                        <th>IP do Utilizador</th>
                        <th>Criada Em</th>
                        <th>Último Acesso</th>
                        <th>Expira Em</th>
                        <th>Conteúdo do Carrinho</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sessions as $session): ?>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input session-checkbox" type="checkbox" value="<?= htmlspecialchars($session['session_id']) ?>">
                                </div>
                            </td>
                            <td>
                                <small>
                                    <span title="<?= htmlspecialchars($session['session_id']) ?>">
                                        <?= htmlspecialchars(substr($session['session_id'], 0, 10)) . '...' ?>
                                    </span>
                                </small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <span class="flag-container" data-ip="<?= htmlspecialchars($session['user_ip'] ?? '') ?>">
                                        <span class="fi fi-<?= htmlspecialchars($session['country_code'] ?? 'xx') ?>" 
                                              style="width: 20px; height: 15px; display: inline-block;" 
                                              title="<?= htmlspecialchars($session['country_name'] ?? 'Unknown') ?>"></span>
                                    </span>
                                    <span class="badge bg-info text-dark">
                                        <?= htmlspecialchars($session['user_ip'] ?? 'N/A') ?>
                                    </span>
                                </div>
                            </td>
                            <td><?= htmlspecialchars(format_date($session['created_at'], 'd/m/Y H:i:s')) ?></td>
                            <td><?= htmlspecialchars(format_date($session['last_access'], 'd/m/Y H:i:s')) ?></td>
                            <td><?= htmlspecialchars(format_date($session['expires_at'], 'd/m/Y H:i:s')) ?></td>
                            <td>
                                <?php $cart_list = $session['cart_items_list'] ?? []; ?>
                                <?php if (!empty($cart_list)): ?>
                                    <small><?= implode('<br>', array_map('htmlspecialchars', $cart_list)) ?></small>
                                    <span class="badge bg-success ms-1"><?= count($cart_list) ?></span>
                                <?php else: ?>
                                    <small class="text-muted">Vazio</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <form method="post" action="admin.php?section=sessions&action=delete_session&<?= get_session_id_param() ?>"
                                      onsubmit="return confirm('Tem a certeza que deseja eliminar esta sessão (<?= htmlspecialchars($session['session_id']) ?>) e os seus tokens associados? Esta ação não pode ser desfeita.');"
                                      style="display: inline-block;">
                                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                                    <input type="hidden" name="session_id_to_delete" value="<?= htmlspecialchars($session['session_id']) ?>">
                                    <button type="submit" class="btn btn-danger btn-sm" title="Eliminar Sessão">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Navegação de páginas">
                <ul class="pagination justify-content-center">
                    <!-- Previous Page -->
                    <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="admin.php?section=sessions&p=<?= $current_page - 1 ?>&sessions_per_page=<?= $sessions_per_page ?>&sort=<?= urlencode($sort) ?>&country=<?= urlencode($country_filter) ?>&ip=<?= urlencode($ip_filter) ?>&<?= get_session_id_param() ?>" onclick="preserveSelections(this)">
                                <i class="bi bi-chevron-left"></i> Anterior
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="bi bi-chevron-left"></i> Anterior</span>
                        </li>
                    <?php endif; ?>
                    
                    <!-- Page Numbers -->
                    <?php
                    $start_page = max(1, $current_page - 2);
                    $end_page = min($total_pages, $current_page + 2);
                    
                    if ($start_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="admin.php?section=sessions&p=1&sessions_per_page=<?= $sessions_per_page ?>&sort=<?= urlencode($sort) ?>&country=<?= urlencode($country_filter) ?>&ip=<?= urlencode($ip_filter) ?>&<?= get_session_id_param() ?>" onclick="preserveSelections(this)">1</a>
                        </li>
                        <?php if ($start_page > 2): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                            <a class="page-link" href="admin.php?section=sessions&p=<?= $i ?>&sessions_per_page=<?= $sessions_per_page ?>&sort=<?= urlencode($sort) ?>&country=<?= urlencode($country_filter) ?>&ip=<?= urlencode($ip_filter) ?>&<?= get_session_id_param() ?>" onclick="preserveSelections(this)"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="admin.php?section=sessions&p=<?= $total_pages ?>&sessions_per_page=<?= $sessions_per_page ?>&sort=<?= urlencode($sort) ?>&country=<?= urlencode($country_filter) ?>&ip=<?= urlencode($ip_filter) ?>&<?= get_session_id_param() ?>" onclick="preserveSelections(this)"><?= $total_pages ?></a>
                        </li>
                    <?php endif; ?>
                    
                    <!-- Next Page -->
                    <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="admin.php?section=sessions&p=<?= $current_page + 1 ?>&sessions_per_page=<?= $sessions_per_page ?>&sort=<?= urlencode($sort) ?>&country=<?= urlencode($country_filter) ?>&ip=<?= urlencode($ip_filter) ?>&<?= get_session_id_param() ?>" onclick="preserveSelections(this)">
                                Próxima <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link">Próxima <i class="bi bi-chevron-right"></i></span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
// Sessions management will be initialized by admin-navigation.js when loaded via AJAX
function initSessionsManagement() {
    // Bulk selection functionality
    const selectAllCheckbox = document.getElementById('select_all_table');
    const sessionCheckboxes = document.querySelectorAll('.session-checkbox');
    const bulkDeleteBtn = document.getElementById('bulk_delete_btn');
    const bulkDeleteForm = document.getElementById('bulk_delete_form');
    const bulkSessionIds = document.getElementById('bulk_session_ids');
    
    // Restore selections from URL parameters
    restoreSelections();
    
    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            sessionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkDeleteButton();
        });
    }
    
    // Handle individual checkboxes
    sessionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkDeleteButton();
        });
    });
    
    // Update select all checkbox state
    function updateSelectAllState() {
        if (!selectAllCheckbox) return;
        
        const checkedCount = document.querySelectorAll('.session-checkbox:checked').length;
        const totalCount = sessionCheckboxes.length;
        
        selectAllCheckbox.checked = checkedCount === totalCount && totalCount > 0;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }
    
    // Update bulk delete button visibility and state
    function updateBulkDeleteButton() {
        if (!bulkDeleteBtn) return;
        
        const checkedBoxes = document.querySelectorAll('.session-checkbox:checked');
        const hasSelected = checkedBoxes.length > 0;
        
        bulkDeleteBtn.disabled = !hasSelected;
        
        if (hasSelected) {
            const selectedIds = Array.from(checkedBoxes).map(cb => cb.value);
            if (bulkSessionIds) {
                bulkSessionIds.value = selectedIds.join(',');
            }
        }
    }
    
    // Handle bulk delete button click
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.session-checkbox:checked');
            const count = checkedBoxes.length;
            
            if (count === 0) {
                alert('Por favor, selecione pelo menos uma sessão para eliminar.');
                return;
            }
            
            const message = `Tem a certeza que deseja eliminar ${count} sessão${count > 1 ? 'ões' : ''} selecionada${count > 1 ? 's' : ''}? Esta ação não pode ser desfeita.`;
            
            if (confirm(message)) {
                bulkDeleteForm.submit();
            }
        });
    }
    
    // IP Geolocation and Flag Display
    const flagContainers = document.querySelectorAll('.flag-container');
    
    // Cache for country data to avoid repeated API calls - using localStorage for persistence
    const CACHE_KEY = 'sessions_country_cache';
    const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    function getCountryCache() {
        try {
            const cached = localStorage.getItem(CACHE_KEY);
            if (cached) {
                const data = JSON.parse(cached);
                // Check if cache is expired
                if (Date.now() - data.timestamp < CACHE_EXPIRY) {
                    return new Map(data.entries);
                }
            }
        } catch (error) {
            console.warn('Error loading country cache:', error);
        }
        return new Map();
    }
    
    function saveCountryCache(cache) {
        try {
            const data = {
                timestamp: Date.now(),
                entries: Array.from(cache.entries())
            };
            localStorage.setItem(CACHE_KEY, JSON.stringify(data));
        } catch (error) {
            console.warn('Error saving country cache:', error);
        }
    }
    
    const countryCache = getCountryCache();
    
    // Country code to country name mapping (for common countries)
    const countryNames = {
        'US': 'United States',
        'GB': 'United Kingdom',
        'DE': 'Germany',
        'FR': 'France',
        'IT': 'Italy',
        'ES': 'Spain',
        'PT': 'Portugal',
        'BR': 'Brazil',
        'CA': 'Canada',
        'AU': 'Australia',
        'JP': 'Japan',
        'CN': 'China',
        'IN': 'India',
        'RU': 'Russia',
        'NL': 'Netherlands',
        'BE': 'Belgium',
        'CH': 'Switzerland',
        'AT': 'Austria',
        'SE': 'Sweden',
        'NO': 'Norway',
        'DK': 'Denmark',
        'FI': 'Finland',
        'PL': 'Poland',
        'CZ': 'Czech Republic',
        'HU': 'Hungary',
        'GR': 'Greece',
        'IE': 'Ireland',
        'LU': 'Luxembourg',
        'SK': 'Slovakia',
        'SI': 'Slovenia',
        'EE': 'Estonia',
        'LV': 'Latvia',
        'LT': 'Lithuania',
        'MT': 'Malta',
        'CY': 'Cyprus',
        'BG': 'Bulgaria',
        'RO': 'Romania',
        'HR': 'Croatia'
    };
    
    async function getCountryFromIP(ip) {
        if (!ip || ip === 'N/A' || ip === 'LEGACY_SESSION') {
            return { country: 'XX', name: 'Unknown' };
        }
        
        // Check cache first
        if (countryCache.has(ip)) {
            return countryCache.get(ip);
        }
        
        try {
            // Using country.is API (free, no API key required)
            const response = await fetch(`https://api.country.is/${ip}`);
            if (response.ok) {
                const data = await response.json();
                const countryCode = data.country || 'XX';
                const countryName = countryNames[countryCode] || countryCode;
                const result = { country: countryCode.toLowerCase(), name: countryName };
                
                // Cache the result
                countryCache.set(ip, result);
                saveCountryCache(countryCache);
                return result;
            }
        } catch (error) {
            console.warn('Failed to get country for IP:', ip, error);
        }
        
        // Fallback
        const fallback = { country: 'xx', name: 'Unknown' };
        countryCache.set(ip, fallback);
        saveCountryCache(countryCache);
        return fallback;
    }
    
    // Process each flag container
    flagContainers.forEach(async (container) => {
        const ip = container.dataset.ip;
        const flagElement = container.querySelector('.fi');
        
        if (!ip || ip === 'N/A' || ip === '') {
            flagElement.className = 'fi fi-xx';
            flagElement.title = 'Unknown Location';
            return;
        }
        
        // Check if we already have valid country data from server
        const currentClass = flagElement.className;
        const currentTitle = flagElement.title;
        
        // Only fetch from API if we don't have valid data or if it's marked as unknown
        if (currentClass === 'fi fi-xx' || currentTitle === 'Unknown' || currentTitle === 'Unknown Location') {
            try {
                const countryData = await getCountryFromIP(ip);
                
                // Update flag only if we got better data
                if (countryData.country !== 'xx') {
                    flagElement.className = `fi fi-${countryData.country}`;
                    flagElement.title = countryData.name;
                }
                
                // Add hover effect
                flagElement.style.cursor = 'help';
                
            } catch (error) {
                console.warn('Error processing IP:', ip, error);
                // Don't overwrite existing data on error
                if (currentClass === 'fi fi-xx') {
                    flagElement.title = 'Error loading country';
                }
            }
        } else {
            // We have valid data from server, just add hover effect
            flagElement.style.cursor = 'help';
        }
    });
    
    // Functions for preserving selections across pagination
    function getSelectedSessionIds() {
        const checkedBoxes = document.querySelectorAll('.session-checkbox:checked');
        return Array.from(checkedBoxes).map(cb => cb.value);
    }
    
    function restoreSelections() {
        const urlParams = new URLSearchParams(window.location.search);
        const selectedIds = urlParams.get('selected');
        
        if (selectedIds) {
            const idsArray = selectedIds.split(',');
            sessionCheckboxes.forEach(checkbox => {
                if (idsArray.includes(checkbox.value)) {
                    checkbox.checked = true;
                }
            });
            updateSelectAllState();
            updateBulkDeleteButton();
        }
    }
    
    // Make preserveSelections function global so it can be called from onclick
    window.preserveSelections = function(linkElement) {
        const selectedIds = getSelectedSessionIds();
        if (selectedIds.length > 0) {
            const url = new URL(linkElement.href);
            url.searchParams.set('selected', selectedIds.join(','));
            linkElement.href = url.toString();
        }
    };
}

// Make function globally available
window.initSessionsManagement = initSessionsManagement;

// Initialize immediately if not loaded via AJAX (direct page access)
document.addEventListener('DOMContentLoaded', function() {
    initSessionsManagement();
});

// Also preserve selections when using filters
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form[action="admin.php"]');
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const selectedIds = Array.from(document.querySelectorAll('.session-checkbox:checked')).map(cb => cb.value);
            if (selectedIds.length > 0) {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'selected';
                hiddenInput.value = selectedIds.join(',');
                filterForm.appendChild(hiddenInput);
            }
        });
    }
});
</script>
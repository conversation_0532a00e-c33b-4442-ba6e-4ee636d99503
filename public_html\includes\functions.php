<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/security.php';

require_once __DIR__ . '/../phpmailer_lib/Exception.php';
require_once __DIR__ . '/../phpmailer_lib/PHPMailer.php';
require_once __DIR__ . '/../phpmailer_lib/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception as PHPMailerException;

$app_settings = null;

function sanitize_input(?string $input): string
{
    return htmlspecialchars(trim($input ?? ''), ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

function format_date(?string $dateString, string $format = 'd/m/Y H:i'): string
{
    if (empty($dateString)) {
        return '';
    }
    try {
        $date = new DateTime($dateString);
        return $date->format($format);
    } catch (Exception $e) {
        return '';
    }
}

function format_datetime(?string $dateString, string $format = 'd/m/Y H:i'): string
{
    return format_date($dateString, $format);
}

function generate_slug(string $text): string
{
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    $text = preg_replace('~[^-\w]+~', '', $text);
    $text = trim($text, '-');
    $text = preg_replace('~-+~', '-', $text);
    $text = strtolower($text);

    if (empty($text)) {
        return 'n-a-' . time();
    }

    return $text;
}

function format_price(float $amount, string $currency_symbol = '€'): string
{
    $formatted_number = number_format($amount, 2, ',', '.');
    return $currency_symbol . $formatted_number;
}

function limit_words(string $string, int $word_limit, string $ellipsis = ''): string
{
    $string = strip_tags($string);
    $words = preg_split("/\s+/", $string);
    if (count($words) > $word_limit) {
        return implode(' ', array_slice($words, 0, $word_limit)) . $ellipsis;
    }
    return $string;
}

function load_settings(bool $force_reload = false): array
{
    global $app_settings, $default_settings;

    if ($app_settings !== null && !$force_reload) {
        return $app_settings;
    }

    $pdo = get_db_connection();
    $db_settings = [];

    if ($pdo) {
        try {
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($rows as $row) {
                $db_settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (PDOException $e) {
        }
    }

    if (!isset($default_settings) || !is_array($default_settings)) {
        $default_settings = [];
    }

    $app_settings = array_merge($default_settings, $db_settings);

    if (isset($app_settings['watermark_enabled'])) {
        $app_settings['watermark_enabled'] = (bool) $app_settings['watermark_enabled'];
    }
    if (isset($app_settings['watermark_opacity'])) {
        $app_settings['watermark_opacity'] = (float) $app_settings['watermark_opacity'];
    }
    if (isset($app_settings['smtp_port'])) {
        $app_settings['smtp_port'] = (int) $app_settings['smtp_port'];
    }
    if (isset($app_settings['items_per_page'])) {
        $app_settings['items_per_page'] = (int) $app_settings['items_per_page'];
    }

    return $app_settings;
}

function get_setting(string $key, mixed $default = null): mixed
{
    global $app_settings;
    if ($app_settings === null) {
        load_settings();
    }
    return $app_settings[$key] ?? $default;
}

function update_setting(string $key, mixed $value): bool
{
    global $app_settings;
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    $isVatIdSetting = ($key === 'vat_id_required_enabled');
    if ($isVatIdSetting) {
        try {
            $checkStmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = :key");
            $checkStmt->execute([':key' => $key]);
            $existingValue = $checkStmt->fetchColumn();
        } catch (PDOException $e) {
        }
    }

    $sql = "INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (:key, :value)";
    $params = [':key' => $key, ':value' => $value];

    try {
        $rowCount = db_query($sql, $params);

        if ($rowCount === false) {
            if ($isVatIdSetting) {
            }
            return false;
        }

        if ($rowCount < 1) {
             if ($isVatIdSetting) {
                try {
                    $directStmt = $pdo->prepare("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (:key, :value)");
                    $directResult = $directStmt->execute([':key' => $key, ':value' => $value]);
                } catch (PDOException $e) {
                }
            }
        }

        if ($app_settings !== null) {
            load_settings(true);
        }

        if ($isVatIdSetting) {
            try {
                $verifyStmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = :key");
                $verifyStmt->execute([':key' => $key]);
                $savedValue = $verifyStmt->fetchColumn();
            } catch (PDOException $e) {
            }
        }

        return true;

    } catch (PDOException $e) {
        return false;
    }
}

function get_page_url(string $slug): string
{
    return BASE_URL . '/index.php?page=' . urlencode($slug) . '&' . get_session_id_param();
}

function get_product_url(string $slug): string
{
    return BASE_URL . '/index.php?product=' . urlencode($slug) . '&' . get_session_id_param();
}

function get_asset_url(string $path): string
{
    if ($path === 'images/placeholder.png') {
        $fallback_sql = "SELECT filename FROM product_images ORDER BY id ASC LIMIT 1";
        $fallback_result = db_query($fallback_sql, [], true);
        if ($fallback_result && !empty($fallback_result['filename'])) {
            return PRODUCT_IMAGES_URL . '/' . ltrim($fallback_result['filename'], '/');
        } else {
            return PRODUCT_IMAGES_URL . '/prod_1_1745335482_0_medalha_redonda_aco_com_patina_2020__2.jpg';
        }
    }

    return ASSETS_URL . '/' . ltrim($path, '/');
}

function get_product_image_url(string $image_filename): string
{
    return defined('PRODUCT_IMAGES_URL') ? PRODUCT_IMAGES_URL . '/' . ltrim($image_filename, '/') : '';
}

function get_current_session_id(): string
{
    $current_session_id = session_id();
    if (empty($current_session_id)) {
        return '';
    }
    return $current_session_id;
}

function add_session_param_to_url(string $url): string
{
    $session_param = get_session_id_param();
    if (empty($session_param)) {
        return $url;
    }

    if (strpos($url, '?') === false) {
        return $url . '?' . $session_param;
    } else {
        if (strpos($url, SESSION_PARAM_NAME . '=') === false) {
             return $url . '&' . $session_param;
        } else {
            return $url;
        }
    }
}

function readfile_chunked(string $file, bool $retbytes = true, int $chunk_size = 1024 * 1024): int|bool
{
    $buffer = '';
    $cnt = 0;
    $handle = @fopen($file, 'rb');

    if ($handle === false) {
        return false;
    }

    while (!feof($handle)) {
        $buffer = fread($handle, $chunk_size);
        if ($buffer === false) {
            fclose($handle);
            return false;
        }

        echo $buffer;
        ob_flush();
        flush();

        if ($retbytes) {
            $cnt += strlen($buffer);
        }
    }

    $status = fclose($handle);

    if ($retbytes && $status) {
        return $cnt;
    }

    return $status;
}

function include_template(string $template_path, array $data = []): void
{
    $full_path = PROJECT_ROOT . '/templates/' . ltrim($template_path, '/');
    if (file_exists($full_path)) {

        extract($data);
        include $full_path;
    } else {
        echo "<!-- Template not found: {$template_path} -->";
    }
}

function set_admin_redirect(string $url, bool $exit = false): void
{
    if (strpos($url, SESSION_PARAM_NAME . '=') === false) {
        $url = add_session_param_to_url($url);
    }

    $_SESSION['redirect_to'] = $url;

    if ($exit) {
        exit;
    }
}

function add_flash_message(string $message, string $type = 'info'): void
{
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    $_SESSION['flash_messages'][] = ['message' => $message, 'type' => $type];
}

function display_flash_messages(): void
{
    if (isset($_SESSION['flash_messages']) && !empty($_SESSION['flash_messages'])) {
        foreach ($_SESSION['flash_messages'] as $flash) {
            
            $message = $flash['message'];
            $type = sanitize_input($flash['type']);
            $valid_types = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark'];
            if (!in_array($type, $valid_types)) {
                $type = 'info';
            }
            echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>";
            echo $message; 
            echo "<button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>";
            echo "</div>";
        }
        unset($_SESSION['flash_messages']);
    }
}

function displayMessages(): void
{
    display_flash_messages();
}

function calculate_cart_totals(): array
{
    $cart_items = $_SESSION['cart'] ?? [];
    
    
    $processed_cart_items = [];
    $all_digital_products = true; 

    if (empty($cart_items)) {
        
        
        
    } else {
        foreach ($cart_items as $key => $original_item) {
            $current_item = $original_item; 
            if (!isset($current_item['product_type'])) {
                
                if (isset($current_item['product_id']) && is_numeric($current_item['product_id']) && $current_item['product_id'] > 0) {
                    $product_db_data = db_query("SELECT product_type FROM products WHERE id = ?", [$current_item['product_id']], true);
                    $current_item['product_type'] = $product_db_data ? $product_db_data['product_type'] : 'regular';
                } else {
                    
                    $current_item['product_type'] = 'regular';
                }
            }

            if ($current_item['product_type'] !== 'digital') {
                $all_digital_products = false;
                
            }
            $processed_cart_items[$key] = $current_item;
        }
        $cart_items = $processed_cart_items; 
    }

    $cart_subtotal = 0.0;
    $total_items_count = 0;
    $vat_groups = [];

    foreach ($cart_items as $item) { 
        $item_total = $item['price'] * $item['quantity'];
        $cart_subtotal += $item_total;
        $total_items_count += $item['quantity'];

        $vat_rate = $item['vat_rate'] ?? null;
        $vat_description = $item['vat_description'] ?? null;

        if ($vat_rate === null) {
            if (!function_exists('get_default_vat_rate')) {
                require_once __DIR__ . '/vat_functions.php';
            }

            $default_vat = get_default_vat_rate();
            
            
            $vat_rate = $default_vat ? $default_vat['rate'] : 23.0;
            $vat_description = $default_vat ? $default_vat['description'] : 'Taxa Normal';
        }

        if (!isset($vat_groups[$vat_rate])) {
            $vat_groups[$vat_rate] = [
                'rate' => $vat_rate,
                'description' => $vat_description,
                'subtotal' => 0,
                'tax' => 0
            ];
        }
        $vat_groups[$vat_rate]['subtotal'] += $item_total;
        $vat_groups[$vat_rate]['tax'] += $item_total * ($vat_rate / 100);
    }

    $shipping_cost = 0.0;
    $free_shipping_threshold = (float)get_setting('free_shipping_threshold', 0);

    if (!$all_digital_products) {

        
        if ($free_shipping_threshold > 0 && $cart_subtotal < $free_shipping_threshold) {
            $fixed_shipping_cost_value = (float)get_setting('fixed_shipping_cost', 0); 

            if ($fixed_shipping_cost_value > 0) {
                $shipping_cost = $fixed_shipping_cost_value;
            } else {
                
                $pdo_ship = get_db_connection(); 
                if ($pdo_ship) {
                    try {
                        $stmt_ship = $pdo_ship->query("SELECT cost FROM shipping_methods WHERE is_active = 1 ORDER BY is_default DESC, id ASC LIMIT 1");
                        $shipping_method = $stmt_ship->fetch(PDO::FETCH_ASSOC);
                        if ($shipping_method && isset($shipping_method['cost'])) {
                            $shipping_cost = (float)$shipping_method['cost'];
                        } else {
                            
                            $shipping_cost = (float)get_setting('shipping_cost_flat', 5.0); 
                        }
                    } catch (PDOException $e) {
                        
                        $shipping_cost = (float)get_setting('shipping_cost_flat', 5.0); 
                        
                    }
                } else {
                     
                     $shipping_cost = (float)get_setting('shipping_cost_flat', 5.0); 
                }
            }
        }
        
        
    }
    

    $tax_amount = 0;
    foreach ($vat_groups as $group) {
        $tax_amount += $group['tax'];
    }

    $discount_amount = $_SESSION['cart_discount'] ?? 0.0;

    $grand_total = $cart_subtotal + $tax_amount + $shipping_cost - $discount_amount;

    $min_order_value = (float)get_setting('min_order_value', 0);

    return [
        'subtotal' => $cart_subtotal,
        'tax_amount' => $tax_amount,
        'shipping_cost' => $shipping_cost,
        'discount_amount' => $discount_amount,
        'grand_total' => $grand_total,
        'total_items' => $total_items_count,
        'vat_groups' => $vat_groups,
        'min_order_value' => $min_order_value,
        'free_shipping_threshold' => $free_shipping_threshold,
        'meets_min_order' => ($min_order_value <= 0 || $grand_total >= $min_order_value),
        'has_free_shipping' => ($free_shipping_threshold <= 0 || $cart_subtotal >= $free_shipping_threshold)
    ];
}

function imagecopy_alpha($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $src_w, $src_h, $pct): bool
{
    if (!isset($pct)) {
        return false;
    }
    $pct /= 100;

    $w = imagesx($src_im);
    $h = imagesy($src_im);

    imagealphablending($src_im, false);

    $minalpha = 127;
    for ($x = 0; $x < $w; $x++) {
        for ($y = 0; $y < $h; $y++) {
            $alpha = (imagecolorat($src_im, $x, $y) >> 24) & 0xFF;
            if ($alpha < $minalpha) {
                $minalpha = $alpha;
            }
        }
    }

    for ($x = 0; $x < $w; $x++) {
        for ($y = 0; $y < $h; $y++) {

            $colorxy = imagecolorat($src_im, $x, $y);
            $alpha = ($colorxy >> 24) & 0xFF;

            if ($minalpha !== 127) {
                $alpha = 127 + 127 * $pct * ($alpha - 127) / (127 - $minalpha);
            } else {
                $alpha += 127 * $pct;
            }

            $alphacolorxy = imagecolorallocatealpha($src_im, ($colorxy >> 16) & 0xFF, ($colorxy >> 8) & 0xFF, $colorxy & 0xFF, (int)$alpha);

            if (!imagesetpixel($src_im, $x, $y, $alphacolorxy)) {
                return false;
            }
        }
    }

     imagealphablending($dst_im, true);
     imagealphablending($src_im, true);

    return imagecopy($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $src_w, $src_h);
}

function apply_watermark(string $targetImagePath, string $watermarkImagePath, ?string $position = null, int $margin = 10, ?float $opacity = null): bool
{
    if (!extension_loaded('gd')) {
        return false;
    }
    if (!file_exists($targetImagePath) || !file_exists($watermarkImagePath)) {
        return false;
    }

    $watermark = @imagecreatefrompng($watermarkImagePath);
    if (!$watermark) {
        return false;
    }
    $watermark_width = imagesx($watermark);
    $watermark_height = imagesy($watermark);

    $targetInfo = @getimagesize($targetImagePath);
    if (!$targetInfo) {
        imagedestroy($watermark);
        return false;
    }
    $mime = $targetInfo['mime'];
    $targetImage = null;
    try {
        switch ($mime) {
            case 'image/jpeg':
                $targetImage = imagecreatefromjpeg($targetImagePath);
                break;
            case 'image/png':
                $targetImage = imagecreatefrompng($targetImagePath);

                imagealphablending($targetImage, true);
                imagesavealpha($targetImage, true);
                break;
            case 'image/gif':
                $targetImage = imagecreatefromgif($targetImagePath);
                break;
            case 'image/webp':
                 if (function_exists('imagecreatefromwebp')) {
                     $targetImage = imagecreatefromwebp($targetImagePath);
                 } else {
                     imagedestroy($watermark);
                     return false;
                 }
                break;
            default:
                imagedestroy($watermark);
                return false;
        }
    } catch (Exception $e) {
         if ($watermark) imagedestroy($watermark);
         return false;
    }

    if (!$targetImage) {
        imagedestroy($watermark);
        return false;
    }

    $target_width = imagesx($targetImage);
    $target_height = imagesy($targetImage);

    $size_percent = (int) get_setting('watermark_size_percent', 15);
    $target_watermark_width = (int) (($target_width * $size_percent) / 100);

    if ($watermark_width > 0) {
        $aspect_ratio = $watermark_height / $watermark_width;
        $target_watermark_height = (int) ($target_watermark_width * $aspect_ratio);
    } else {
        $target_watermark_height = 0;
    }

    $resized_watermark = $watermark;
    $resized_watermark_width = $watermark_width;
    $resized_watermark_height = $watermark_height;

    if ($target_watermark_width > 0 && $target_watermark_height > 0 && ($target_watermark_width != $watermark_width || $target_watermark_height != $watermark_height)) {
        $resized_watermark = imagecreatetruecolor($target_watermark_width, $target_watermark_height);
        if (!$resized_watermark) {
             imagedestroy($targetImage);
             imagedestroy($watermark);
             return false;
        }

        imagealphablending($resized_watermark, false);
        imagesavealpha($resized_watermark, true);
        $transparent = imagecolorallocatealpha($resized_watermark, 255, 255, 255, 127);
        imagefilledrectangle($resized_watermark, 0, 0, $target_watermark_width, $target_watermark_height, $transparent);

        if (!imagecopyresampled($resized_watermark, $watermark, 0, 0, 0, 0, $target_watermark_width, $target_watermark_height, $watermark_width, $watermark_height)) {
             imagedestroy($targetImage);
             imagedestroy($watermark);
             imagedestroy($resized_watermark);
             return false;
        }
        $resized_watermark_width = $target_watermark_width;
        $resized_watermark_height = $target_watermark_height;
    } else {
    }

    if ($position === null) {
        $position = get_setting('watermark_position', 'bottom-right');
    }

    switch (strtolower($position)) {
        case 'center':
             $dest_x = ($target_width - $watermark_width) / 2;
             $dest_x = ($target_width - $resized_watermark_width) / 2;
             $dest_y = ($target_height - $resized_watermark_height) / 2;
             break;
        case 'top-left':
             $dest_x = $margin;
             $dest_y = $margin;
             break;
        case 'top-right':
             $dest_x = $target_width - $resized_watermark_width - $margin;
             $dest_y = $margin;
             break;
        case 'bottom-left':
             $dest_x = $margin;
             $dest_y = $target_height - $resized_watermark_height - $margin;
             break;
        case 'bottom-right':
        default:
             $dest_x = $target_width - $resized_watermark_width - $margin;
             $dest_y = $target_height - $resized_watermark_height - $margin;
             $position = 'bottom-right';
             break;
    }

    if ($opacity === null) {

        $opacity_float = (float) get_setting('watermark_opacity', 0.5);
    } else {
        $opacity_float = $opacity;
    }

    $opacity_float = max(0.0, min(1.0, $opacity_float));

    $opacity_percent = (int) ($opacity_float * 100);

    if (!imagecopy_alpha(
        $targetImage, $resized_watermark,
        (int)$dest_x, (int)$dest_y,
        0, 0,
        $resized_watermark_width, $resized_watermark_height,
        $opacity_percent
    )) {
         imagedestroy($targetImage);
         imagedestroy($watermark);
         if ($resized_watermark !== $watermark) imagedestroy($resized_watermark);
         return false;
    }

    $save_success = false;
    switch ($mime) {
        case 'image/jpeg':
            $save_success = imagejpeg($targetImage, $targetImagePath, 90);
            break;
        case 'image/png':

             imagealphablending($targetImage, false);
             imagesavealpha($targetImage, true);
             $save_success = imagepng($targetImage, $targetImagePath, 7);
            break;
        case 'image/gif':
            $save_success = imagegif($targetImage, $targetImagePath);
            break;
        case 'image/webp':
             if (function_exists('imagewebp')) {
                 $save_success = imagewebp($targetImage, $targetImagePath, 90);
             } else {
             }
            break;
    }

    if ($save_success) {
    } else {
    }

    imagedestroy($targetImage);
    imagedestroy($watermark);
    if ($resized_watermark !== $watermark) {
        imagedestroy($resized_watermark);
    }

    return $save_success;
}

function upload_error_message(int $error_code): string
{
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return "O ficheiro excede a diretiva upload_max_filesize no php.ini.";
        case UPLOAD_ERR_FORM_SIZE:
            return "O ficheiro excede a diretiva MAX_FILE_SIZE especificada no formulário HTML.";
        case UPLOAD_ERR_PARTIAL:
            return "O ficheiro foi apenas parcialmente carregado.";
        case UPLOAD_ERR_NO_FILE:
            return "Nenhum ficheiro foi carregado.";
        case UPLOAD_ERR_NO_TMP_DIR:
            return "Falta uma pasta temporária no servidor.";
        case UPLOAD_ERR_CANT_WRITE:
            return "Falha ao escrever o ficheiro no disco.";
        case UPLOAD_ERR_EXTENSION:
            return "Uma extensão PHP parou o upload do ficheiro.";
        default:
            return "Erro desconhecido no upload do ficheiro.";
    }
}

function set_default_product_image(int $product_id, ?int $default_image_id): bool
{
    if ($product_id <= 0) {
        return false;
    }

    try {

        $unset_sql = "UPDATE product_images SET is_default = 0 WHERE product_id = :pid";
        db_query($unset_sql, [':pid' => $product_id]);

        if ($default_image_id !== null && $default_image_id > 0) {
            $set_sql = "UPDATE product_images SET is_default = 1 WHERE id = :img_id AND product_id = :pid";
            $result = db_query($set_sql, [':img_id' => $default_image_id, ':pid' => $product_id]);

            return ($result !== false && $result > 0);
        }

        return true;

    } catch (Exception $e) {
        return false;
    }
}

function count_pages(array $filters = []): int
{
    $conditions = [];
    $params = [];

    
    if (!empty($filters['title'])) {
        $conditions[] = "p.title_pt LIKE ?";
        $params[] = "%" . $filters['title'] . "%";
    }

    
    if (!empty($filters['category_id'])) {
        $conditions[] = "p.category_id = ?";
        $params[] = $filters['category_id'];
    }

    
    if (isset($filters['is_active']) && $filters['is_active'] !== '') {
        $conditions[] = "p.is_active = ?";
        $params[] = (int)$filters['is_active'];
    }

    
    if (isset($filters['location']) && $filters['location'] !== '') {
        switch ($filters['location']) {
            case 'header':
                $conditions[] = "p.show_in_header = 1";
                break;
            case 'footer':
                $conditions[] = "p.show_in_footer = 1";
                break;
            case 'checkout':
                $conditions[] = "p.require_agreement_checkout = 1";
                break;
            case 'digital_checkout':
                $conditions[] = "p.require_agreement_digital_checkout = 1";
                break;
        }
    }

    $sql = "SELECT COUNT(*) FROM pages p";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $stmt = db_query($sql, $params);
    if ($stmt === false) {
        return 0;
    }

    return (int)$stmt->fetchColumn();
}

function get_all_pages(array $filters = []): array
{
    $conditions = [];
    $params = [];

    
    if (!empty($filters['title'])) {
        $conditions[] = "p.title_pt LIKE ?";
        $params[] = "%" . $filters['title'] . "%";
    }

    
    if (!empty($filters['category_id'])) {
        $conditions[] = "p.category_id = ?";
        $params[] = $filters['category_id'];
    }

    
    if (isset($filters['is_active']) && $filters['is_active'] !== '') {
        $conditions[] = "p.is_active = ?";
        $params[] = (int)$filters['is_active'];
    }

    
    if (isset($filters['location']) && $filters['location'] !== '') {
        switch ($filters['location']) {
            case 'header':
                $conditions[] = "p.show_in_header = 1";
                break;
            case 'footer':
                $conditions[] = "p.show_in_footer = 1";
                break;
            case 'checkout':
                $conditions[] = "p.require_agreement_checkout = 1";
                break;
            case 'digital_checkout':
                $conditions[] = "p.require_agreement_digital_checkout = 1";
                break;
        }
    }

    $sql = "SELECT
                p.id, p.title_pt, p.slug, p.is_active,
                p.show_in_header, p.show_in_footer, p.require_agreement_checkout,
                p.require_agreement_digital_checkout,
                pc.name as category_name
            FROM pages p
            LEFT JOIN page_categories pc ON p.category_id = pc.id";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $sql .= " ORDER BY p.title_pt ASC";

    
    if (isset($filters['page']) && isset($filters['per_page'])) {
        $page = max(1, (int)$filters['page']);
        $per_page = max(1, (int)$filters['per_page']);
        $offset = ($page - 1) * $per_page;
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $per_page;
        $params[] = $offset;
    }

    $pages = db_query($sql, $params, false, true);
    if ($pages === false) {
        return [];
    }

    return is_array($pages) ? $pages : [];
}

function get_page_by_id(int $id): array|false
{
    $sql = "SELECT * FROM pages WHERE id = :id";
    $stmt = db_query($sql, [':id' => $id]);

    if (is_object($stmt) && method_exists($stmt, 'fetch')) {
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } else {

        return false;
    }
}

function get_page_by_slug(string $slug): array|false
{
    $sql = "SELECT * FROM pages WHERE slug = :slug AND is_active = 1";
    $stmt = db_query($sql, [':slug' => $slug]);

    if (is_object($stmt) && method_exists($stmt, 'fetch')) {
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } else {

        return false;
    }
}

function create_page(
    string $title,
    string $content,
    bool $is_active,
    ?string $slug = null,
    ?int $category_id = null,
    bool $show_in_header = false,
    bool $show_in_footer = false,
    bool $require_agreement = false,
    ?int $placeholder_id = null,
    bool $require_agreement_digital = false,
    bool $show_title = true, 
    
    ?string $seo_title = null,
    ?string $seo_description = null,
    ?string $seo_keywords = null,
    ?string $og_title = null,
    ?string $og_description = null,
    ?string $og_image = null,
    ?string $twitter_card = null,
    ?string $twitter_title = null,
    ?string $twitter_description = null,
    ?string $twitter_image = null
): int|false {
    if (empty($slug)) {
        $slug = generate_slug($title);
    } else {
        $slug = generate_slug($slug);
    }

    $category_id = ($category_id > 0) ? $category_id : null;

    $placeholder_id = ($placeholder_id > 0) ? $placeholder_id : null;

    $sql = "INSERT INTO pages (
                title_pt, slug, content_pt, is_active, category_id,
                show_in_header, show_in_footer, require_agreement_checkout,
                require_agreement_digital_checkout, placeholder_id, show_title,
                seo_title, seo_description, seo_keywords,
                og_title, og_description, og_image,
                twitter_card, twitter_title, twitter_description, twitter_image,
                created_at, updated_at
            ) VALUES (
                :title_pt, :slug, :content_pt, :is_active, :category_id,
                :show_header, :show_footer, :req_agreement,
                :req_agreement_digital, :placeholder_id, :show_title,
                :seo_title, :seo_description, :seo_keywords,
                :og_title, :og_description, :og_image,
                :twitter_card, :twitter_title, :twitter_description, :twitter_image,
                datetime('now', 'localtime'), datetime('now', 'localtime')
            )";
    $params = [
        ':title_pt' => $title,
        ':slug' => $slug,
        ':content_pt' => $content,
        ':is_active' => (int)$is_active,
        ':category_id' => $category_id,
        ':show_header' => (int)$show_in_header,
        ':show_footer' => (int)$show_in_footer,
        ':req_agreement' => (int)$require_agreement,
        ':req_agreement_digital' => (int)$require_agreement_digital,
        ':placeholder_id' => $placeholder_id,
        ':show_title' => (int)$show_title,
        
        ':seo_title' => $seo_title,
        ':seo_description' => $seo_description,
        ':seo_keywords' => $seo_keywords,
        ':og_title' => $og_title,
        ':og_description' => $og_description,
        ':og_image' => $og_image,
        ':twitter_card' => $twitter_card,
        ':twitter_title' => $twitter_title,
        ':twitter_description' => $twitter_description,
        ':twitter_image' => $twitter_image,
    ];

    $pdo = get_db_connection();
    if (db_query($sql, $params)) {
        return (int)$pdo->lastInsertId();
    }
    return false;
}

function update_page(
    int $id,
    string $title,
    string $content,
    bool $is_active,
    ?string $slug = null,
    ?int $category_id = null,
    bool $show_in_header = false,
    bool $show_in_footer = false,
    bool $require_agreement = false,
    ?int $placeholder_id = null,
    bool $require_agreement_digital = false,
    bool $show_title = true, 
    
    ?string $seo_title = null,
    ?string $seo_description = null,
    ?string $seo_keywords = null,
    ?string $og_title = null,
    ?string $og_description = null,
    ?string $og_image = null,
    ?string $twitter_card = null,
    ?string $twitter_title = null,
    ?string $twitter_description = null,
    ?string $twitter_image = null
): bool {

    if (empty($slug)) {
        $slug = generate_slug($title);
    } else {
        $slug = generate_slug($slug);
    }

    $category_id = ($category_id > 0) ? $category_id : null;

    $placeholder_id = ($placeholder_id > 0) ? $placeholder_id : null;

    $sql = "UPDATE pages SET
                title_pt = :title_pt,
                slug = :slug,
                content_pt = :content_pt,
                is_active = :is_active,
                category_id = :category_id,
                show_in_header = :show_header,
                show_in_footer = :show_footer,
                require_agreement_checkout = :req_agreement,
                require_agreement_digital_checkout = :req_agreement_digital,
                placeholder_id = :placeholder_id,
                show_title = :show_title,
                seo_title = :seo_title,
                seo_description = :seo_description,
                seo_keywords = :seo_keywords,
                og_title = :og_title,
                og_description = :og_description,
                og_image = :og_image,
                twitter_card = :twitter_card,
                twitter_title = :twitter_title,
                twitter_description = :twitter_description,
                twitter_image = :twitter_image,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";
    $params = [
        ':id' => $id,
        ':title_pt' => $title,
        ':slug' => $slug,
        ':content_pt' => $content,
        ':is_active' => (int)$is_active,
        ':category_id' => $category_id,
        ':show_header' => (int)$show_in_header,
        ':show_footer' => (int)$show_in_footer,
        ':req_agreement' => (int)$require_agreement,
        ':req_agreement_digital' => (int)$require_agreement_digital,
        ':placeholder_id' => $placeholder_id,
        ':show_title' => (int)$show_title,
        
        ':seo_title' => $seo_title,
        ':seo_description' => $seo_description,
        ':seo_keywords' => $seo_keywords,
        ':og_title' => $og_title,
        ':og_description' => $og_description,
        ':og_image' => $og_image,
        ':twitter_card' => $twitter_card,
        ':twitter_title' => $twitter_title,
        ':twitter_description' => $twitter_description,
        ':twitter_image' => $twitter_image,
    ];

    return db_query($sql, $params) !== false;
}

function delete_page(int $id): bool
{
    $sql = "DELETE FROM pages WHERE id = :id";
    return db_query($sql, [':id' => $id]) !== false;
}

function get_pages_for_location(string $location): array
{
    $column = '';
    if ($location === 'header') {
        $column = 'show_in_header';
    } elseif ($location === 'footer') {
        $column = 'show_in_footer';
    } else {
        return [];
    }

    $sql = "SELECT id, title_pt as title, slug FROM pages -- Changed 'title' to 'title_pt as title'
            WHERE is_active = 1 AND {$column} = 1
            ORDER BY title_pt ASC";

    $stmt = db_query($sql);
    if ($stmt === false) {
        return [];
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: [];
}

function get_all_page_placeholders(): array
{
    $sql = "SELECT id, name, slug, created_at, updated_at FROM page_placeholders ORDER BY name ASC";

    $stmt = db_query($sql);
    if ($stmt === false) {
        return [];
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: [];
}

function get_page_placeholder_by_id(int $id): array|false
{
    $sql = "SELECT * FROM page_placeholders WHERE id = :id";
    $stmt = db_query($sql, [':id' => $id]);
    if (is_object($stmt) && method_exists($stmt, 'fetch')) {
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        return false;
    }
}

function get_page_placeholder_by_slug(string $slug): array|false
{
    $sql = "SELECT * FROM page_placeholders WHERE slug = :slug";
    $stmt = db_query($sql, [':slug' => $slug]);
    if (is_object($stmt) && method_exists($stmt, 'fetch')) {
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        return false;
    }
}

function create_page_placeholder(string $name, ?string $slug = null): int|false
{

    if (empty($slug)) {
        $slug = generate_slug($name);
    }

    $check_sql = "SELECT id FROM page_placeholders WHERE slug = :slug";
    $check_stmt = db_query($check_sql, [':slug' => $slug], true);
    if ($check_stmt) {
        return false;
    }

    $sql = "INSERT INTO page_placeholders (name, slug, created_at, updated_at)
            VALUES (:name, :slug, datetime('now', 'localtime'), datetime('now', 'localtime'))";
    $params = [
        ':name' => $name,
        ':slug' => $slug
    ];

    $result = db_query($sql, $params);
    if ($result === false) {
        return false;
    }

    $pdo = get_db_connection();
    return $pdo ? (int)$pdo->lastInsertId() : false;
}

function update_page_placeholder(int $id, string $name, ?string $slug = null): bool
{

    if (empty($slug)) {
        $slug = generate_slug($name);
    }

    $check_sql = "SELECT id FROM page_placeholders WHERE slug = :slug AND id != :id";
    $check_stmt = db_query($check_sql, [':slug' => $slug, ':id' => $id], true);
    if ($check_stmt) {
        return false;
    }

    $sql = "UPDATE page_placeholders SET
                name = :name,
                slug = :slug,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";
    $params = [
        ':id' => $id,
        ':name' => $name,
        ':slug' => $slug
    ];

    return db_query($sql, $params) !== false;
}

function delete_page_placeholder(int $id): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {

        $pdo->beginTransaction();

        $update_sql = "UPDATE pages SET placeholder_id = NULL WHERE placeholder_id = :id";
        $update_stmt = $pdo->prepare($update_sql);
        $update_stmt->execute([':id' => $id]);

        $delete_sql = "DELETE FROM page_placeholders WHERE id = :id";
        $delete_stmt = $pdo->prepare($delete_sql);
        $delete_stmt->execute([':id' => $id]);

        $pdo->commit();
        return true;
    } catch (PDOException $e) {

        $pdo->rollBack();
        return false;
    }
}

function get_pages_by_placeholder_id(int $placeholder_id): array
{
    $sql = "SELECT id, title_pt as title, slug FROM pages
            WHERE is_active = 1 AND placeholder_id = :placeholder_id
            ORDER BY title_pt ASC";

    $stmt = db_query($sql, [':placeholder_id' => $placeholder_id]);
    if ($stmt === false) {
        return [];
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: [];
}

function get_links_by_placeholder_id(int $placeholder_id): array
{
    $sql = "SELECT id, title, url, target FROM placeholder_links
            WHERE placeholder_id = :placeholder_id
            ORDER BY title ASC";

    $stmt = db_query($sql, [':placeholder_id' => $placeholder_id]);
    if ($stmt === false) {
        return [];
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: [];
}

function create_placeholder_link(string $title, string $url, int $placeholder_id, string $target = '_blank'): int|false
{

    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }

    if ($target !== '_blank' && $target !== '_self') {
        $target = '_blank';
    }

    $sql = "INSERT INTO placeholder_links (title, url, placeholder_id, target, created_at, updated_at)
            VALUES (:title, :url, :placeholder_id, :target, datetime('now', 'localtime'), datetime('now', 'localtime'))";
    $params = [
        ':title' => $title,
        ':url' => $url,
        ':placeholder_id' => $placeholder_id,
        ':target' => $target
    ];

    $result = db_query($sql, $params);
    if ($result === false) {
        return false;
    }

    $pdo = get_db_connection();
    return $pdo ? (int)$pdo->lastInsertId() : false;
}

function update_placeholder_link(int $id, string $title, string $url, int $placeholder_id, string $target = '_blank'): bool
{

    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }

    if ($target !== '_blank' && $target !== '_self') {
        $target = '_blank';
    }

    $sql = "UPDATE placeholder_links SET
                title = :title,
                url = :url,
                placeholder_id = :placeholder_id,
                target = :target,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";
    $params = [
        ':id' => $id,
        ':title' => $title,
        ':url' => $url,
        ':placeholder_id' => $placeholder_id,
        ':target' => $target
    ];

    return db_query($sql, $params) !== false;
}

function get_placeholder_link_by_id(int $id): array|false
{
    $sql = "SELECT * FROM placeholder_links WHERE id = :id";
    $stmt = db_query($sql, [':id' => $id]);
    if (is_object($stmt) && method_exists($stmt, 'fetch')) {
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        return false;
    }
}

function delete_placeholder_link(int $id): bool
{
    $sql = "DELETE FROM placeholder_links WHERE id = :id";
    return db_query($sql, [':id' => $id]) !== false;
}

function get_all_placeholders_with_pages(): array
{
    $placeholders = get_all_page_placeholders();
    $result = [];

    foreach ($placeholders as $placeholder) {
        $pages = get_pages_by_placeholder_id($placeholder['id']);
        $links = get_links_by_placeholder_id($placeholder['id']);

        $items = [];

        foreach ($pages as $page) {
            $items[] = [
                'type' => 'page',
                'id' => $page['id'],
                'title' => $page['title'],
                'slug' => $page['slug'],
                'url' => get_page_url($page['slug']),
                'target' => '_self'
            ];
        }

        foreach ($links as $link) {
            $items[] = [
                'type' => 'link',
                'id' => $link['id'],
                'title' => $link['title'],
                'url' => $link['url'],
                'target' => $link['target']
            ];
        }

        usort($items, function($a, $b) {
            return strcasecmp($a['title'], $b['title']);
        });

        $result[] = [
            'id' => $placeholder['id'],
            'name' => $placeholder['name'],
            'slug' => $placeholder['slug'],
            'items' => $items
        ];
    }

    return $result;
}

function get_required_agreement_pages(bool $has_digital_products = false): array
{

    $sql = "SELECT id, title_pt as title, slug FROM pages -- Changed 'title' to 'title_pt as title'
            WHERE is_active = 1 AND (require_agreement_checkout = 1";

    if ($has_digital_products) {
        $sql .= " OR require_agreement_digital_checkout = 1";
    }

    $sql .= ") ORDER BY title_pt ASC";

    $stmt = db_query($sql);
    if ($stmt === false) {
        return [];
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: [];
}

function get_all_page_categories(): array
{
    $sql = "SELECT * FROM page_categories ORDER BY name ASC";

    $categories = db_query($sql, [], false, true);

    if (is_array($categories)) {
        return $categories;
    } else {

        return [];
    }
}

function get_page_category_by_id(int $id): array|false
{
    $sql = "SELECT * FROM page_categories WHERE id = :id";

    $category_data = db_query($sql, [':id' => $id], true);

    if (is_array($category_data)) {
        return $category_data;
    } else {
        return false;
    }
}

function check_and_update_page_categories_schema(PDO $pdo): void
{
    try {
        $stmt = $pdo->query("PRAGMA table_info(page_categories);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('show_in_header', $columns)) {
            $pdo->exec("ALTER TABLE page_categories ADD COLUMN show_in_header INTEGER NOT NULL DEFAULT 0;");
        }

    } catch (PDOException $e) {

    }
}

function create_page_category(string $name, ?string $slug = null, bool $show_in_header = false): int|false
{
    $pdo = get_db_connection();
    if (!$pdo) return false;
    check_and_update_page_categories_schema($pdo);

    if (empty($slug)) {
        $slug = generate_slug($name);
    } else {
        $slug = generate_slug($slug);
    }

    $sql = "INSERT INTO page_categories (name, slug, show_in_header, created_at, updated_at)
            VALUES (:name, :slug, :show_in_header, datetime('now', 'localtime'), datetime('now', 'localtime'))";
    $params = [
        ':name' => $name,
        ':slug' => $slug,
        ':show_in_header' => (int)$show_in_header,
    ];

    $result = db_query($sql, $params, false, false, $pdo);

    if ($result !== false) {
         return (int)$pdo->lastInsertId();
    }
    return false;
}

function update_page_category(int $id, string $name, ?string $slug = null, bool $show_in_header = false): bool
{
    $pdo = get_db_connection();
    if (!$pdo) return false;
    check_and_update_page_categories_schema($pdo);

    if (empty($slug)) {
        $slug = generate_slug($name);
    } else {
        $slug = generate_slug($slug);
    }

    $sql = "UPDATE page_categories SET
                name = :name,
                slug = :slug,
                show_in_header = :show_in_header,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";
    $params = [
        ':id' => $id,
        ':name' => $name,
        ':slug' => $slug,
        ':show_in_header' => (int)$show_in_header,
    ];

    $result = db_query($sql, $params);
    return $result !== false;
}

function delete_page_category(int $id): bool
{
    $pdo = get_db_connection();
    try {
        $pdo->beginTransaction();

        $update_pages_sql = "UPDATE pages SET category_id = NULL WHERE category_id = :id";
        db_query($update_pages_sql, [':id' => $id], $pdo);

        $delete_cat_sql = "DELETE FROM page_categories WHERE id = :id";
        db_query($delete_cat_sql, [':id' => $id], $pdo);

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_header_categories_with_pages(): array
{
    $sql = "SELECT
                c.id as category_id,
                c.name as category_name,
                c.slug as category_slug,
                p.id as page_id,
                p.title_pt as page_title,
                p.slug as page_slug
            FROM
                page_categories c
            JOIN
                pages p ON c.id = p.category_id
            WHERE
                c.show_in_header = 1
                AND p.is_active = 1
                -- Removed: AND p.is_system_page = 0
            ORDER BY
                c.name ASC, -- Order categories alphabetically
                p.title_pt ASC";

    $results = db_query($sql, [], false, true);

    if ($results === false) {
        return [];
    }

    $categories_with_pages = [];
    foreach ($results as $row) {
        $cat_id = $row['category_id'];
        if (!isset($categories_with_pages[$cat_id])) {
            $categories_with_pages[$cat_id] = [
                'name' => $row['category_name'],
                'slug' => $row['category_slug'],
                'pages' => []
            ];
        }
        $categories_with_pages[$cat_id]['pages'][] = [
            'id' => $row['page_id'],
            'title' => $row['page_title'],
            'slug' => $row['page_slug']
        ];
    }

    return $categories_with_pages;
}

function getAllCategories(string $order_by = 'name', string $order_dir = 'ASC'): array
{

    $allowed_order_by = ['id', 'name', 'created_at'];
    if (!in_array(strtolower($order_by), $allowed_order_by)) {
        $order_by = 'name';
    }
    $order_dir = strtoupper($order_dir) === 'DESC' ? 'DESC' : 'ASC';

    $sql = "SELECT * FROM categories ORDER BY {$order_by} {$order_dir}";
    $categories = db_query($sql, [], false, true);

    if ($categories === false) {
        return [];
    }
    return is_array($categories) ? $categories : [];
}

function getCategoryById(int $id): array|false
{
    if ($id <= 0) return false;
    $sql = "SELECT * FROM categories WHERE id = :id";
    $category = db_query($sql, [':id' => $id], true);
    return is_array($category) ? $category : false;
}

function addCategory(array $data): int|false
{
    if (empty($data['name']) || empty($data['slug'])) {
        return false;
    }

    $sql = "INSERT INTO categories (name, slug, created_at, updated_at) VALUES (:name, :slug, datetime('now', 'localtime'), datetime('now', 'localtime'))";
    $params = [
        ':name' => trim($data['name']),
        ':slug' => trim($data['slug'])
    ];

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $stmt = $pdo->prepare($sql);
        if ($stmt->execute($params)) {
            return (int)$pdo->lastInsertId();
        } else {
            return false;
        }
    } catch (PDOException $e) {

        return false;
    }
}

function updateCategory(int $id, array $data): bool
{
    if ($id <= 0 || empty($data['name']) || empty($data['slug'])) {
        return false;
    }

    $sql = "UPDATE categories SET name = :name, slug = :slug, updated_at = datetime('now', 'localtime') WHERE id = :id";
    $params = [
        ':id' => $id,
        ':name' => trim($data['name']),
        ':slug' => trim($data['slug'])
    ];

    try {
        $result = db_query($sql, $params);

        return $result !== false;
    } catch (PDOException $e) {

        return false;
    }
}

function deleteCategory(int $id): bool
{
    if ($id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $delete_cat_sql = "DELETE FROM categories WHERE id = :id";
        $stmt_delete = $pdo->prepare($delete_cat_sql);
        $stmt_delete->execute([':id' => $id]);

        $pdo->commit();
        return true;

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_product_breadcrumbs(int $product_id): array
{
    $breadcrumbs = [
        ['name' => 'Início', 'url' => BASE_URL . '/?' . get_session_id_param()]
    ];

    if ($product_id <= 0) {
        return $breadcrumbs;
    }

    $sql = "SELECT c.id, c.name, c.slug
            FROM categories c
            JOIN product_categories pc ON c.id = pc.category_id
            WHERE pc.product_id = :pid
            ORDER BY c.id -- Or some other criteria if needed
            LIMIT 1";

    $category = db_query($sql, [':pid' => $product_id], true);

    if ($category) {

        $breadcrumbs[] = [
            'name' => sanitize_input($category['name']),
            'url' => BASE_URL . '/index.php?category_id=' . urlencode($category['id']) . '&' . get_session_id_param()
        ];
    }

    return $breadcrumbs;
}

function create_order(array $order_data, array $cart_items): int|false
{
    require_once __DIR__ . '/email_functions.php'; 

    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $pdo->beginTransaction();

        $order_ref = 'ENC-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));

        $customer_info = [
            'customer_name' => $order_data['customer_name'],
            'customer_email' => $order_data['customer_email'],
            'customer_phone' => $order_data['customer_phone'] ?? '',
            'customer_vat_id' => $order_data['customer_vat_id'] ?? '',
            'shipping_address' => $order_data['shipping_address'] ?? '',
            'shipping_city' => $order_data['shipping_city'] ?? '',
            'shipping_zip' => $order_data['shipping_zip'] ?? '',
            'shipping_country' => $order_data['shipping_country'] ?? '',
            'order_notes' => $order_data['order_notes'] ?? ''
        ];

        $currency = get_setting('default_currency', 'EUR');

        $coupon_code = $_SESSION['applied_promo_code'] ?? null;
        $coupon_id = $_SESSION['applied_coupon_id'] ?? null;

        $has_digital_products = false;
        foreach ($cart_items as $item) {
            $product_id = $item['product_id'] ?? 0;
            if ($product_id > 0) {
                $product = db_query(
                    "SELECT product_type FROM products WHERE id = :id",
                    [':id' => $product_id],
                    true
                );

                if ($product && $product['product_type'] === 'digital') {
                    $has_digital_products = true;
                    break;
                }
            }
        }

        $sql = "INSERT INTO orders (
                    order_ref,
                    customer_info_json,
                    total_amount,
                    currency,
                    status,
                    payment_method,
                    shipping_method,
                    shipping_cost,
                    coupon_code,
                    discount_amount,
                    tax_amount,
                    tax_details_json,
                    terms_log_json,
                    has_digital_products,
                    ip_address,
                    created_at,
                    updated_at
                ) VALUES (
                    :order_ref,
                    :customer_info_json,
                    :total_amount,
                    :currency,
                    :status,
                    :payment_method,
                    :shipping_method,
                    :shipping_cost,
                    :coupon_code,
                    :discount_amount,
                    :tax_amount,
                    :tax_details_json,
                    :terms_log_json,
                    :has_digital_products,
                    :ip_address,
                    datetime('now', 'localtime'),
                    datetime('now', 'localtime')
                )";

        $stmt = $pdo->prepare($sql);

        $vat_groups = [];
        if (isset($order_data['vat_groups'])) {
            $vat_groups = $order_data['vat_groups'];
        } else {

            foreach ($cart_items as $item) {
                $vat_rate = $item['vat_rate'] ?? null;
                $vat_description = $item['vat_description'] ?? null;

                if ($vat_rate === null) {

                    if (!function_exists('get_default_vat_rate')) {
                        require_once __DIR__ . '/vat_functions.php';
                    }

                    $default_vat = get_default_vat_rate();
                    $vat_rate = $default_vat ? $default_vat['rate'] : get_setting('tax_rate_percent', 23.0);
                    $vat_description = $default_vat ? $default_vat['description'] : 'Taxa Normal';
                }

                $item_total = $item['price'] * $item['quantity'];

                if (!isset($vat_groups[$vat_rate])) {
                    $vat_groups[$vat_rate] = [
                        'rate' => $vat_rate,
                        'description' => $vat_description,
                        'subtotal' => 0,
                        'tax' => 0
                    ];
                }
                $vat_groups[$vat_rate]['subtotal'] += $item_total;
                $vat_groups[$vat_rate]['tax'] += $item_total * ($vat_rate / 100);
            }
        }

        $params = [
            ':order_ref' => $order_ref,
            ':customer_info_json' => json_encode($customer_info),
            ':total_amount' => $order_data['total_amount'],
            ':currency' => $currency,
            ':status' => $order_data['order_status'] ?? 'pending',
            ':payment_method' => $order_data['payment_method'] ?? null,
            ':shipping_method' => $order_data['shipping_method'] ?? null,
            ':shipping_cost' => $order_data['shipping_cost'] ?? 0.0,
            ':coupon_code' => $coupon_code,
            ':discount_amount' => $order_data['discount_amount'] ?? 0.0,
            ':tax_amount' => $order_data['tax_amount'] ?? 0.0,
            ':tax_details_json' => json_encode($vat_groups),
            ':terms_log_json' => $order_data['terms_log'] ?? null,
            ':has_digital_products' => $has_digital_products ? 1 : 0,
            ':ip_address' => $order_data['ip_address'] ?? null
        ];

        $stmt->execute($params);

        $order_id = $pdo->lastInsertId();

        foreach ($cart_items as $item) {

            $product_details = [
                'name' => $item['name'],
                'sku' => $item['sku'] ?? '',
                'attributes' => $item['attributes'] ?? '',
                'attributes_display' => $item['attributes_display'] ?? '',
                'vat_rate' => $item['vat_rate'] ?? null,
                'vat_description' => $item['vat_description'] ?? null,
                'custom_fields' => $item['custom_fields'] ?? []
            ];

            $sql = "INSERT INTO order_items (
                        order_id,
                        variation_id,
                        product_id,
                        quantity,
                        price_at_purchase,
                        product_details_json
                    ) VALUES (
                        :order_id,
                        :variation_id,
                        :product_id,
                        :quantity,
                        :price_at_purchase,
                        :product_details_json
                    )";

            
            $variation_id = $item['variation_id'] ?? null;
            $product_id = $item['product_id'];

            
            if ($variation_id) {
                $verify_stmt = $pdo->prepare("SELECT id, product_id FROM product_variations WHERE id = ?");
                $verify_stmt->execute([$variation_id]);
                $variation_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$variation_data) {

                    
                    $check_stmt = $pdo->prepare("SELECT id FROM product_variations WHERE id = ?");
                    $check_stmt->execute([$product_id]);
                    $check_result = $check_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($check_result) {
                        $temp = $product_id;
                        $product_id = $variation_id;
                        $variation_id = $temp;
                    } else {
                        $variation_id = null;
                    }
                } else if ($variation_data['product_id'] != $product_id) {
                    $product_id = $variation_data['product_id'];
                }
            }

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':order_id' => $order_id,
                ':variation_id' => $variation_id,
                ':product_id' => $product_id,
                ':quantity' => $item['quantity'],
                ':price_at_purchase' => $item['price'],
                ':product_details_json' => json_encode($product_details)
            ]);

            $order_item_id = $pdo->lastInsertId();

            if (!empty($item['custom_fields'])) {

                if (!function_exists('save_order_item_custom_field')) {
                    require_once __DIR__ . '/custom_field_functions.php';
                }

                foreach ($item['custom_fields'] as $custom_field) {
                    $field_id = $custom_field['field_id'] ?? 0;
                    $field_name = $custom_field['field_name'] ?? '';
                    $field_type = $custom_field['field_type'] ?? '';
                    $price_modifier = $custom_field['price_modifier'] ?? 0.0;

                    try {

                        $custom_field_exists = db_query(
                            "SELECT id FROM custom_fields WHERE id = :id",
                            [':id' => $field_id],
                            true
                        );

                        if (!$custom_field_exists) {
                            $existing_field_by_name = db_query(
                                "SELECT id FROM custom_fields WHERE name = :name",
                                [':name' => $field_name],
                                true
                            );

                            if ($existing_field_by_name) {

                                $field_id = $existing_field_by_name['id'];
                            } else {

                                $field_type_id = 1;
                                if ($field_type === 'file-upload') {
                                    $field_type_id = 2;
                                }

                                $new_field_id = create_custom_field([
                                    'name' => $field_name,
                                    'description' => 'Auto-created from order',
                                    'field_type_id' => $field_type_id,
                                    'is_active' => 1
                                ]);

                                if ($new_field_id) {
                                    $field_id = $new_field_id;
                                }
                            }
                        }

                        if ($field_type === 'custom-text') {

                            $field_value = $custom_field['text_value'] ?? '';
                            $font_id = $custom_field['font_id'] ?? null;

                            $result = save_order_item_custom_field(
                                $order_item_id,
                                $field_id,
                                $field_name,
                                $field_value,
                                null,
                                $font_id,
                                $price_modifier
                            );

                            if ($result) {
                            } else {
                            }
                        } elseif ($field_type === 'file-upload') {

                            $file_path = $custom_field['file_path'] ?? '';

                            if (!empty($file_path)) {
                                $result = save_order_item_custom_field(
                                    $order_item_id,
                                    $field_id,
                                    $field_name,
                                    null,
                                    $file_path,
                                    null,
                                    $price_modifier
                                );

                                if ($result) {
                                } else {
                                }
                            }
                        } elseif ($field_type === 'texto-dropdown') {

                            $dropdown_value = $custom_field['dropdown_value'] ?? '';
                            $dropdown_text = $custom_field['dropdown_text'] ?? '';
                            
                            
                            $field_value = $dropdown_text;

                            $result = save_order_item_custom_field(
                                $order_item_id,
                                $field_id,
                                $field_name,
                                $field_value,
                                null,
                                null,
                                $price_modifier
                            );

                            if ($result) {
                            } else {
                            }
                        }
                    } catch (Exception $e) {
                    }
                }
            }

            
            $product_type = db_query(
                "SELECT product_type FROM products WHERE id = :id",
                [':id' => $item['product_id']],
                true
            );

            
            if ($product_type && $product_type['product_type'] !== 'digital') {
                require_once __DIR__ . '/stock_functions.php';
                reduce_order_item_stock($order_item_id, $pdo);
            }
        }

        if ($coupon_id) {

            require_once __DIR__ . '/coupon_functions.php';
            increment_coupon_usage($coupon_id);
        }

        $_SESSION['cart'] = [];
        unset($_SESSION['cart_discount']);
        unset($_SESSION['applied_promo_code']);
        unset($_SESSION['applied_coupon_id']);

        if (session_status() === PHP_SESSION_ACTIVE) {
            session_write_close();

        }

        $pdo->commit();

        
        require_once __DIR__ . '/order_functions.php';
        $access_token = create_order_access_token($order_id);

        if ($has_digital_products) {
            require_once __DIR__ . '/digital_order_functions.php';

            $customer_info = [
                'customer_name' => $order_data['customer_name'],
                'customer_email' => $order_data['customer_email']
            ];

            $license_ids = create_licenses_for_order($order_id, $customer_info);
            if (!empty($license_ids)) {
            } else {
            }

            
            send_digital_product_terms_email($order_id);
        }

        try {
            $session_id = $order_data['session_id'] ?? null;
            if ($session_id) {

                $fresh_pdo = get_db_connection();
                if ($fresh_pdo) {

                    $stmt = $fresh_pdo->prepare("UPDATE sessions SET data = :data WHERE id = :id");
                    $session_data = serialize(['cart' => []]);
                    $stmt->execute([':data' => $session_data, ':id' => $session_id]);
                }
            }
        } catch (Exception $e) {

        }

        return $order_id;

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        if (strpos($e->getMessage(), "no such column") !== false) {
        }

        return false;
    }
}

function get_all_orders(string $order_by = 'created_at', string $order_dir = 'DESC'): array
{

    $allowed_order_by = ['id', 'total_amount', 'status', 'created_at', 'updated_at'];
    if (!in_array(strtolower($order_by), $allowed_order_by)) {
        $order_by = 'created_at';
    }
    $order_dir = strtoupper($order_dir) === 'ASC' ? 'ASC' : 'DESC';

    $sql = "SELECT
                id,
                order_ref,
                json_extract(customer_info_json, '$.customer_name') as customer_name,
                json_extract(customer_info_json, '$.customer_email') as customer_email,
                created_at,
                total_amount,
                status
            FROM orders
            ORDER BY {$order_by} {$order_dir}";

    $orders = db_query($sql, [], false, true);

    if ($orders === false) {
        return [];
    }
    return is_array($orders) ? $orders : [];
}

function send_email(
    string $to_email,
    string $to_name,
    string $subject,
    string $body_html,
    string $body_plain = '',
    ?string $reply_to_email = null,
    ?string $reply_to_name = null,
    array $attachments = [] 
): bool {
    $mail = new PHPMailer(true);

    try {

        $smtp_host = get_setting('smtp_host');
        $smtp_username = get_setting('smtp_username');
        $smtp_password = get_setting('smtp_password');
        $smtp_port = (int) get_setting('smtp_port', 587);
        $smtp_secure = get_setting('smtp_secure', 'tls');

        $from_email = get_setting('from_email');
        $from_name = get_setting('from_name', get_setting('store_name', 'A Nossa Loja'));

        $reply_to_email = $reply_to_email ?? get_setting('reply_to_email');
        $reply_to_name = $reply_to_name ?? get_setting('reply_to_name');

        if (empty($smtp_host) || empty($smtp_username) || empty($from_email)) {
            return false;
        }

        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        if ($smtp_secure === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        }
        $mail->Port = $smtp_port;
        $mail->CharSet = PHPMailer::CHARSET_UTF8;

        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($to_email, $to_name);

        if (!empty($reply_to_email)) {
            $mail->addReplyTo($reply_to_email, $reply_to_name ?? '');
        }

        
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && isset($attachment['name']) && file_exists($attachment['path'])) {
                    try {
                        $mail->addAttachment($attachment['path'], $attachment['name']);
                    } catch (PHPMailerException $e) {
                        
                    }
                } else {
                }
            }
        }

        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body_html;

        $mail->AltBody = !empty($body_plain) ? $body_plain : strip_tags(str_replace('<br>', "\n", $body_html));
        
        $mail->send();
        return true;
    } catch (PHPMailerException $e) {
        return false;
    }
}

function get_contact_messages(?string $status = null, string $order_by = 'created_at', string $order_dir = 'DESC'): array
{
    $sql = "SELECT * FROM contacts";
    $params = [];

    if ($status !== null && in_array($status, ['new', 'read', 'replied', 'archived'])) {
        $sql .= " WHERE status = :status";
        $params[':status'] = $status;
    }

    $allowed_order_by = ['id', 'name', 'email', 'subject', 'status', 'created_at'];
    if (!in_array(strtolower($order_by), $allowed_order_by)) {
        $order_by = 'created_at';
    }
    $order_dir = strtoupper($order_dir) === 'ASC' ? 'ASC' : 'DESC';

    $sql .= " ORDER BY " . $order_by . " " . $order_dir;

    $messages = db_query($sql, $params, false, true);

    if ($messages === false) {
        return [];
    }

    return is_array($messages) ? $messages : [];
}

function handle_contact_form_submission(array $post_data): bool
{

    $first_name = trim($post_data['first_name'] ?? '');
    $last_name = trim($post_data['last_name'] ?? '');
    $email = trim($post_data['email'] ?? '');
    $subject = trim($post_data['subject'] ?? '');
    $message = trim($post_data['message'] ?? '');
    $phone = trim($post_data['phone'] ?? '');
    $product_ref = trim($post_data['product_ref'] ?? '');

    $name = $first_name . ' ' . $last_name;

    if (empty($first_name) || empty($last_name) || empty($email) || empty($subject) || empty($message)) {
        add_flash_message('Por favor, preencha todos os campos obrigatórios.', 'danger');
        return false;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        add_flash_message('Por favor, insira um endereço de email válido.', 'danger');
        return false;
    }

    if (!validate_csrf_token($post_data['csrf_token'] ?? '')) {
        add_flash_message('Erro de segurança. Por favor, tente novamente.', 'danger');
        return false;
    }

    $recaptcha_secret_key = get_setting('recaptcha_secret_key');
    $recaptcha_enabled = get_setting('recaptcha_enabled', false);

    if ($recaptcha_enabled && !empty($recaptcha_secret_key)) {
        $recaptcha_response = $post_data['g-recaptcha-response'] ?? '';
        if (empty($recaptcha_response)) {
            add_flash_message('Por favor, complete a verificação reCAPTCHA.', 'danger');
            return false;
        }

        $verify_url = 'https://www.google.com/recaptcha/api/siteverify';
        $data = [
            'secret'   => $recaptcha_secret_key,
            'response' => $recaptcha_response,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        ];

        $options = [
            'http' => [
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($data),
            ],
             'ssl' => ['verify_peer' => true, 'verify_peer_name' => true]
        ];
        $context = stream_context_create($options);
        $verify_result = @file_get_contents($verify_url, false, $context);

        if ($verify_result === FALSE) {
             add_flash_message('Não foi possível verificar o reCAPTCHA. Tente novamente mais tarde.', 'danger');
             return false;
        }

        $result_json = json_decode($verify_result, true);

        if (!isset($result_json['success']) || $result_json['success'] !== true) {
            add_flash_message('Falha na verificação reCAPTCHA. Por favor, tente novamente.', 'danger');
            return false;
        }

    }

    $pdo = get_db_connection();
    if ($pdo) {
        try {

            $stmt = $pdo->query("PRAGMA table_info(contacts);");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

            if (!in_array('phone', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN phone TEXT;");
            }

            if (!in_array('product_ref', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN product_ref TEXT;");
            }
        } catch (PDOException $e) {
        }
    }

    $sanitized_name = sanitize_input($name);
    $sanitized_email = sanitize_input($email);
    $sanitized_phone = sanitize_input($phone);
    $sanitized_subject = sanitize_input($subject);
    $sanitized_message = sanitize_input($message);
    $sanitized_product_ref = sanitize_input($product_ref);

    $sql = "INSERT INTO contacts (name, email, phone, subject, message, product_ref, status, created_at)
            VALUES (:name, :email, :phone, :subject, :message, :product_ref, 'new', datetime('now'))";
    $params = [
        ':name' => $sanitized_name,
        ':email' => $sanitized_email,
        ':phone' => !empty($sanitized_phone) ? $sanitized_phone : null,
        ':subject' => $sanitized_subject,
        ':message' => $sanitized_message,
        ':product_ref' => !empty($sanitized_product_ref) ? $sanitized_product_ref : null,
    ];

    if (db_query($sql, $params)) {

        $send_copy = isset($post_data['send_copy']) && $post_data['send_copy'] == '1';

        if ($send_copy) {

            $store_name = get_setting('store_name', 'A Nossa Loja');
            $email_subject = "Cópia da sua mensagem: " . $sanitized_subject;

            $email_body_html = "<p>Olá " . $sanitized_name . ",</p>";
            $email_body_html .= "<p>Abaixo está uma cópia da mensagem que nos enviou:</p>";
            $email_body_html .= "<p><strong>Assunto:</strong> " . $sanitized_subject . "</p>";
            $email_body_html .= "<blockquote style='border-left: 4px solid #ccc; padding-left: 15px; margin-left: 5px;'>";
            $email_body_html .= nl2br($sanitized_message);
            $email_body_html .= "</blockquote>";
            $email_body_html .= "<p>Responderemos à sua mensagem o mais brevemente possível.</p>";
            $email_body_html .= "<p>Com os melhores cumprimentos,<br>" . $store_name . "</p>";

            $email_sent = send_email(
                $sanitized_email,
                $sanitized_name,
                $email_subject,
                $email_body_html
            );

            if (!$email_sent) {

            }
        }

        $_SESSION['contact_form_status'] = 'success';
        $_SESSION['contact_form_message'] = 'Mensagem enviada com sucesso! Obrigado pelo seu contacto. Responderei assim que possível...';
        return true;
    } else {

        $_SESSION['contact_form_status'] = 'error';
        $_SESSION['contact_form_message'] = 'Ocorreu um erro ao enviar a sua mensagem. Por favor, tente novamente mais tarde.';
        return false;
    }
}

function get_messages_with_replies(string $order_by = 'created_at', string $order_dir = 'DESC'): array
{

    $allowed_order_by = ['id', 'name', 'email', 'subject', 'status', 'created_at', 'replied_at'];
    if (!in_array(strtolower($order_by), $allowed_order_by)) {
        $order_by = 'created_at';
    }
    $order_dir = strtoupper($order_dir) === 'ASC' ? 'ASC' : 'DESC';

    $sql = "SELECT
                cm.id AS message_id, cm.name AS sender_name, cm.email AS sender_email,
                cm.phone AS sender_phone, cm.subject, cm.message AS original_message,
                cm.created_at AS message_sent_at, cm.status, cm.replied_at, cm.product_ref,
                mr.id AS reply_id, mr.reply_body, mr.sent_at AS reply_sent_at
            FROM
                contacts cm
            LEFT JOIN
                message_replies mr ON cm.id = mr.message_id
            ORDER BY
                cm.{$order_by} {$order_dir},
                mr.sent_at ASC";

    $results = db_query($sql, [], false, true);

    if ($results === false) {
        return [];
    }

    $messages_history = [];
    foreach ($results as $row) {
        $message_id = $row['message_id'];

        if (!isset($messages_history[$message_id])) {
            $messages_history[$message_id] = [
                'message_data' => [
                    'id' => $message_id,
                    'name' => $row['sender_name'],
                    'email' => $row['sender_email'],
                    'phone' => $row['sender_phone'],
                    'subject' => $row['subject'],
                    'message' => $row['original_message'],
                    'created_at' => $row['message_sent_at'],
                    'status' => $row['status'],
                    'replied_at' => $row['replied_at'],
                    'product_ref' => $row['product_ref']
                ],
                'replies' => []
            ];
        }

        if ($row['reply_id'] !== null) {
            $messages_history[$message_id]['replies'][] = [
                'id' => $row['reply_id'],
                'reply_body' => $row['reply_body'],
                'sent_at' => $row['reply_sent_at']

            ];
        }
    }

    return $messages_history;
}

function get_message_replies(int $message_id): array
{
    if ($message_id <= 0) {
        return [];
    }

    $sql = "SELECT * FROM message_replies WHERE message_id = :message_id ORDER BY sent_at ASC";
    $params = [':message_id' => $message_id];

    $replies = db_query($sql, $params, false, true);

    if ($replies === false) {
        return [];
    }

    return is_array($replies) ? $replies : [];
}

function search_site_content(string $query): array
{
    if (empty(trim($query))) {
        return [];
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return [];
    }

    $search_term_raw = trim($query);
    if (empty($search_term_raw)) {
        return [];
    }

    // Create word boundary patterns for exact word matching (handles spaces and commas)
    $search_term_lower = mb_strtolower($search_term_raw, 'UTF-8');
    $word_patterns = [
        '% ' . $search_term_lower . ' %',  // word surrounded by spaces
        $search_term_lower . ' %',        // word at beginning with space
        '% ' . $search_term_lower,        // word at end with space
        $search_term_lower,               // exact match (single word)
        '%, ' . $search_term_lower . ', %', // word surrounded by commas (for SEO keywords)
        $search_term_lower . ', %',       // word at beginning with comma
        '%, ' . $search_term_lower,       // word at end with comma
        '%,' . $search_term_lower . ',%', // word surrounded by commas without spaces
        $search_term_lower . ',%',        // word at beginning with comma (no space)
        '%,' . $search_term_lower         // word at end with comma (no space)
    ];

    $product_sql = "SELECT
                        p.id, p.name_pt as name, p.slug, p.description_pt as description, p.base_price as price, -- Use name_pt, description_pt, base_price
                        pi.filename as image_filename,
                        'product' as type -- Add type identifier
                    FROM products p
                    LEFT JOIN (
                        SELECT product_id, filename, ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY is_default DESC, id ASC) as rn
                        FROM product_images
                    ) pi ON p.id = pi.product_id AND pi.rn = 1
                    WHERE p.is_active = 1
                      AND (
                        (LOWER(p.name_pt) LIKE :query1 OR LOWER(p.name_pt) LIKE :query2 OR LOWER(p.name_pt) LIKE :query3 OR LOWER(p.name_pt) = :query4 OR
                         LOWER(p.name_pt) LIKE :query5 OR LOWER(p.name_pt) LIKE :query6 OR LOWER(p.name_pt) LIKE :query7 OR
                         LOWER(p.name_pt) LIKE :query8 OR LOWER(p.name_pt) LIKE :query9 OR LOWER(p.name_pt) LIKE :query10) OR
                        (LOWER(p.description_pt) LIKE :query11 OR LOWER(p.description_pt) LIKE :query12 OR LOWER(p.description_pt) LIKE :query13 OR LOWER(p.description_pt) = :query14 OR
                         LOWER(p.description_pt) LIKE :query15 OR LOWER(p.description_pt) LIKE :query16 OR LOWER(p.description_pt) LIKE :query17 OR
                         LOWER(p.description_pt) LIKE :query18 OR LOWER(p.description_pt) LIKE :query19 OR LOWER(p.description_pt) LIKE :query20) OR
                        (LOWER(p.sku) LIKE :query21 OR LOWER(p.sku) LIKE :query22 OR LOWER(p.sku) LIKE :query23 OR LOWER(p.sku) = :query24 OR
                         LOWER(p.sku) LIKE :query25 OR LOWER(p.sku) LIKE :query26 OR LOWER(p.sku) LIKE :query27 OR
                         LOWER(p.sku) LIKE :query28 OR LOWER(p.sku) LIKE :query29 OR LOWER(p.sku) LIKE :query30) OR
                        (LOWER(p.seo_keywords) LIKE :query31 OR LOWER(p.seo_keywords) LIKE :query32 OR LOWER(p.seo_keywords) LIKE :query33 OR LOWER(p.seo_keywords) = :query34 OR
                         LOWER(p.seo_keywords) LIKE :query35 OR LOWER(p.seo_keywords) LIKE :query36 OR LOWER(p.seo_keywords) LIKE :query37 OR
                         LOWER(p.seo_keywords) LIKE :query38 OR LOWER(p.seo_keywords) LIKE :query39 OR LOWER(p.seo_keywords) LIKE :query40)
                      )

                    UNION ALL

                    SELECT
                        p.id, p.name_pt as name, p.slug, p.description_pt as description, p.base_price as price,
                        pi.filename as image_filename,
                        'product' as type
                    FROM products p
                    JOIN product_variations pv ON p.id = pv.product_id
                    LEFT JOIN (
                        SELECT product_id, filename, ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY is_default DESC, id ASC) as rn
                        FROM product_images
                    ) pi ON p.id = pi.product_id AND pi.rn = 1
                    WHERE p.is_active = 1 AND pv.is_active = 1
                      AND (LOWER(pv.sku) LIKE :query41 OR LOWER(pv.sku) LIKE :query42 OR LOWER(pv.sku) LIKE :query43 OR LOWER(pv.sku) = :query44 OR
                           LOWER(pv.sku) LIKE :query45 OR LOWER(pv.sku) LIKE :query46 OR LOWER(pv.sku) LIKE :query47 OR
                           LOWER(pv.sku) LIKE :query48 OR LOWER(pv.sku) LIKE :query49 OR LOWER(pv.sku) LIKE :query50)";

    $page_sql = "SELECT
                     pg.id, pg.title_pt as name, pg.slug, pg.content_pt as description,
                     NULL as price, -- Pages don't have price
                     NULL as image_filename, -- Pages don't have images this way
                     'page' as type -- Add type identifier
                 FROM pages pg
                 WHERE pg.is_active = 1
                   AND (LOWER(pg.title_pt) LIKE :query51 OR LOWER(pg.title_pt) LIKE :query52 OR LOWER(pg.title_pt) LIKE :query53 OR LOWER(pg.title_pt) = :query54 OR
                        LOWER(pg.title_pt) LIKE :query55 OR LOWER(pg.title_pt) LIKE :query56 OR LOWER(pg.title_pt) LIKE :query57 OR
                        LOWER(pg.title_pt) LIKE :query58 OR LOWER(pg.title_pt) LIKE :query59 OR LOWER(pg.title_pt) LIKE :query60)";

    $combined_sql = "{$product_sql} UNION ALL {$page_sql} ORDER BY type ASC, name ASC";

    // Build parameters array with all word boundary patterns
    $params = [];
    for ($i = 1; $i <= 60; $i++) {
        $pattern_index = (($i - 1) % 10);
        $params[':query' . $i] = $word_patterns[$pattern_index];
    }

    try {

        $results = db_query($combined_sql, $params, false, true);

        if ($results === false) {

            return [];
        }

        foreach ($results as &$result) {
            if ($result['type'] === 'product') {
                $result['url'] = get_product_url($result['slug']);

                $result['image_url'] = $result['image_filename'] ? get_product_image_url($result['image_filename']) : get_asset_url('images/placeholder.png');
            } elseif ($result['type'] === 'page') {
                $result['url'] = get_page_url($result['slug']);
                $result['image_url'] = null;
            }
        }
        unset($result);

        return is_array($results) ? $results : [];

    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Search site content with pagination support
 *
 * @param string $query Search query
 * @param int $page Current page number (1-based)
 * @param int $per_page Number of results per page
 * @return array Array with 'results' and 'total_count' keys
 */
function search_site_content_paginated(string $query, int $page = 1, int $per_page = 20): array
{
    if (empty(trim($query))) {
        return ['results' => [], 'total_count' => 0];
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return ['results' => [], 'total_count' => 0];
    }

    $search_term_raw = trim($query);
    if (empty($search_term_raw)) {
        return ['results' => [], 'total_count' => 0];
    }

    // Create word boundary patterns for exact word matching (handles spaces and commas)
    $search_term_lower = mb_strtolower($search_term_raw, 'UTF-8');
    $word_patterns = [
        '% ' . $search_term_lower . ' %',  // word surrounded by spaces
        $search_term_lower . ' %',        // word at beginning with space
        '% ' . $search_term_lower,        // word at end with space
        $search_term_lower,               // exact match (single word)
        '%, ' . $search_term_lower . ', %', // word surrounded by commas (for SEO keywords)
        $search_term_lower . ', %',       // word at beginning with comma
        '%, ' . $search_term_lower,       // word at end with comma
        '%,' . $search_term_lower . ',%', // word surrounded by commas without spaces
        $search_term_lower . ',%',        // word at beginning with comma (no space)
        '%,' . $search_term_lower         // word at end with comma (no space)
    ];

    // Ensure valid pagination parameters
    $page = max(1, $page);
    $per_page = max(1, min(100, $per_page)); // Limit to reasonable range
    $offset = ($page - 1) * $per_page;

    $product_sql = "SELECT
                        p.id, p.name_pt as name, p.slug, p.description_pt as description, p.base_price as price,
                        pi.filename as image_filename,
                        'product' as type
                    FROM products p
                    LEFT JOIN (
                        SELECT product_id, filename, ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY is_default DESC, id ASC) as rn
                        FROM product_images
                    ) pi ON p.id = pi.product_id AND pi.rn = 1
                    WHERE p.is_active = 1
                      AND (
                        (LOWER(p.name_pt) LIKE :query1 OR LOWER(p.name_pt) LIKE :query2 OR LOWER(p.name_pt) LIKE :query3 OR LOWER(p.name_pt) = :query4 OR
                         LOWER(p.name_pt) LIKE :query5 OR LOWER(p.name_pt) LIKE :query6 OR LOWER(p.name_pt) LIKE :query7 OR
                         LOWER(p.name_pt) LIKE :query8 OR LOWER(p.name_pt) LIKE :query9 OR LOWER(p.name_pt) LIKE :query10) OR
                        (LOWER(p.description_pt) LIKE :query11 OR LOWER(p.description_pt) LIKE :query12 OR LOWER(p.description_pt) LIKE :query13 OR LOWER(p.description_pt) = :query14 OR
                         LOWER(p.description_pt) LIKE :query15 OR LOWER(p.description_pt) LIKE :query16 OR LOWER(p.description_pt) LIKE :query17 OR
                         LOWER(p.description_pt) LIKE :query18 OR LOWER(p.description_pt) LIKE :query19 OR LOWER(p.description_pt) LIKE :query20) OR
                        (LOWER(p.sku) LIKE :query21 OR LOWER(p.sku) LIKE :query22 OR LOWER(p.sku) LIKE :query23 OR LOWER(p.sku) = :query24 OR
                         LOWER(p.sku) LIKE :query25 OR LOWER(p.sku) LIKE :query26 OR LOWER(p.sku) LIKE :query27 OR
                         LOWER(p.sku) LIKE :query28 OR LOWER(p.sku) LIKE :query29 OR LOWER(p.sku) LIKE :query30) OR
                        (LOWER(p.seo_keywords) LIKE :query31 OR LOWER(p.seo_keywords) LIKE :query32 OR LOWER(p.seo_keywords) LIKE :query33 OR LOWER(p.seo_keywords) = :query34 OR
                         LOWER(p.seo_keywords) LIKE :query35 OR LOWER(p.seo_keywords) LIKE :query36 OR LOWER(p.seo_keywords) LIKE :query37 OR
                         LOWER(p.seo_keywords) LIKE :query38 OR LOWER(p.seo_keywords) LIKE :query39 OR LOWER(p.seo_keywords) LIKE :query40)
                      )

                    UNION ALL

                    SELECT
                        p.id, p.name_pt as name, p.slug, p.description_pt as description, p.base_price as price,
                        pi.filename as image_filename,
                        'product' as type
                    FROM products p
                    JOIN product_variations pv ON p.id = pv.product_id
                    LEFT JOIN (
                        SELECT product_id, filename, ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY is_default DESC, id ASC) as rn
                        FROM product_images
                    ) pi ON p.id = pi.product_id AND pi.rn = 1
                    WHERE p.is_active = 1 AND pv.is_active = 1
                      AND (LOWER(pv.sku) LIKE :query41 OR LOWER(pv.sku) LIKE :query42 OR LOWER(pv.sku) LIKE :query43 OR LOWER(pv.sku) = :query44 OR
                           LOWER(pv.sku) LIKE :query45 OR LOWER(pv.sku) LIKE :query46 OR LOWER(pv.sku) LIKE :query47 OR
                           LOWER(pv.sku) LIKE :query48 OR LOWER(pv.sku) LIKE :query49 OR LOWER(pv.sku) LIKE :query50)";

    $page_sql = "SELECT
                     pg.id, pg.title_pt as name, pg.slug, pg.content_pt as description,
                     NULL as price,
                     NULL as image_filename,
                     'page' as type
                 FROM pages pg
                 WHERE pg.is_active = 1
                   AND (LOWER(pg.title_pt) LIKE :query51 OR LOWER(pg.title_pt) LIKE :query52 OR LOWER(pg.title_pt) LIKE :query53 OR LOWER(pg.title_pt) = :query54 OR
                        LOWER(pg.title_pt) LIKE :query55 OR LOWER(pg.title_pt) LIKE :query56 OR LOWER(pg.title_pt) LIKE :query57 OR
                        LOWER(pg.title_pt) LIKE :query58 OR LOWER(pg.title_pt) LIKE :query59 OR LOWER(pg.title_pt) LIKE :query60)";

    // Build parameters array with all word boundary patterns
    $count_params = [];
    for ($i = 1; $i <= 60; $i++) {
        $pattern_index = (($i - 1) % 10);
        $count_params[':query' . $i] = $word_patterns[$pattern_index];
    }

    // First, get the total count
    $count_sql = "SELECT COUNT(*) as total FROM ({$product_sql} UNION ALL {$page_sql}) as combined_results";

    try {
        $count_result = db_query($count_sql, $count_params, false, false);
        $total_count = $count_result ? (int)$count_result->fetchColumn() : 0;

        // If no results, return early
        if ($total_count === 0) {
            return ['results' => [], 'total_count' => 0];
        }

        // Now get the paginated results
        $combined_sql = "{$product_sql} UNION ALL {$page_sql} ORDER BY type ASC, name ASC LIMIT :limit OFFSET :offset";
        $params = $count_params; // Use the same parameters as count query
        $params[':limit'] = $per_page;
        $params[':offset'] = $offset;

        $results = db_query($combined_sql, $params, false, true);

        if ($results === false) {
            return ['results' => [], 'total_count' => 0];
        }

        // Process results to add URLs and image URLs
        foreach ($results as &$result) {
            if ($result['type'] === 'product') {
                $result['url'] = get_product_url($result['slug']);
                $result['image_url'] = $result['image_filename'] ? get_product_image_url($result['image_filename']) : get_asset_url('images/placeholder.png');
            } elseif ($result['type'] === 'page') {
                $result['url'] = get_page_url($result['slug']);
                $result['image_url'] = null;
            }
        }
        unset($result);

        return [
            'results' => is_array($results) ? $results : [],
            'total_count' => $total_count
        ];

    } catch (PDOException $e) {
        return ['results' => [], 'total_count' => 0];
    }
}

function get_country_from_ip($ip) {
    
    if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) || 
        $ip === 'UNKNOWN' || $ip === 'N/A' || $ip === 'LEGACY_SESSION') {
        return 'xx';
    }
    
    
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        return 'xx'; 
    }
    
    $sqlite_path = dirname(__DIR__, 2) . '/IPs.sqlite';
    
    if (!file_exists($sqlite_path)) {
        return 'xx';
    }
    
    try {
        
        $pdo = new PDO('sqlite:' . $sqlite_path);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        
        
        $ip_numeric = ip2long($ip);
        if ($ip_numeric === false) {
            return 'xx'; 
        }
        
        
        
        $stmt = $pdo->prepare("
            SELECT Country_Code 
            FROM IPs 
            WHERE CAST(Starting_IP AS INTEGER) <= :ip_numeric 
              AND CAST(Ending_IP AS INTEGER) >= :ip_numeric 
            LIMIT 1
        ");
        
        $stmt->bindValue(':ip_numeric', $ip_numeric, PDO::PARAM_INT);
        $stmt->execute();
        
        $result = $stmt->fetch();
        if ($result && !empty($result['Country_Code'])) {
            return strtolower(trim($result['Country_Code']));
        }
        
        return 'xx'; 
        
    } catch (PDOException $e) {
        return 'xx';
    }
}

function get_country_name($country_code) {
    $countries = [
        'ad' => 'Andorra', 'ae' => 'United Arab Emirates', 'af' => 'Afghanistan',
        'ag' => 'Antigua and Barbuda', 'ai' => 'Anguilla', 'al' => 'Albania',
        'am' => 'Armenia', 'ao' => 'Angola', 'aq' => 'Antarctica', 'ar' => 'Argentina',
        'as' => 'American Samoa', 'at' => 'Austria', 'au' => 'Australia', 'aw' => 'Aruba',
        'ax' => 'Åland Islands', 'az' => 'Azerbaijan', 'ba' => 'Bosnia and Herzegovina',
        'bb' => 'Barbados', 'bd' => 'Bangladesh', 'be' => 'Belgium', 'bf' => 'Burkina Faso',
        'bg' => 'Bulgaria', 'bh' => 'Bahrain', 'bi' => 'Burundi', 'bj' => 'Benin',
        'bl' => 'Saint Barthélemy', 'bm' => 'Bermuda', 'bn' => 'Brunei', 'bo' => 'Bolivia',
        'bq' => 'Caribbean Netherlands', 'br' => 'Brazil', 'bs' => 'Bahamas', 'bt' => 'Bhutan',
        'bv' => 'Bouvet Island', 'bw' => 'Botswana', 'by' => 'Belarus', 'bz' => 'Belize',
        'ca' => 'Canada', 'cc' => 'Cocos Islands', 'cd' => 'DR Congo', 'cf' => 'Central African Republic',
        'cg' => 'Republic of the Congo', 'ch' => 'Switzerland', 'ci' => 'Côte d\'Ivoire',
        'ck' => 'Cook Islands', 'cl' => 'Chile', 'cm' => 'Cameroon', 'cn' => 'China',
        'co' => 'Colombia', 'cr' => 'Costa Rica', 'cu' => 'Cuba', 'cv' => 'Cape Verde',
        'cw' => 'Curaçao', 'cx' => 'Christmas Island', 'cy' => 'Cyprus', 'cz' => 'Czech Republic',
        'de' => 'Germany', 'dj' => 'Djibouti', 'dk' => 'Denmark', 'dm' => 'Dominica',
        'do' => 'Dominican Republic', 'dz' => 'Algeria', 'ec' => 'Ecuador', 'ee' => 'Estonia',
        'eg' => 'Egypt', 'eh' => 'Western Sahara', 'er' => 'Eritrea', 'es' => 'Spain',
        'et' => 'Ethiopia', 'fi' => 'Finland', 'fj' => 'Fiji', 'fk' => 'Falkland Islands',
        'fm' => 'Micronesia', 'fo' => 'Faroe Islands', 'fr' => 'France', 'ga' => 'Gabon',
        'gb' => 'United Kingdom', 'gd' => 'Grenada', 'ge' => 'Georgia', 'gf' => 'French Guiana',
        'gg' => 'Guernsey', 'gh' => 'Ghana', 'gi' => 'Gibraltar', 'gl' => 'Greenland',
        'gm' => 'Gambia', 'gn' => 'Guinea', 'gp' => 'Guadeloupe', 'gq' => 'Equatorial Guinea',
        'gr' => 'Greece', 'gs' => 'South Georgia', 'gt' => 'Guatemala', 'gu' => 'Guam',
        'gw' => 'Guinea-Bissau', 'gy' => 'Guyana', 'hk' => 'Hong Kong', 'hm' => 'Heard Island',
        'hn' => 'Honduras', 'hr' => 'Croatia', 'ht' => 'Haiti', 'hu' => 'Hungary',
        'id' => 'Indonesia', 'ie' => 'Ireland', 'il' => 'Israel', 'im' => 'Isle of Man',
        'in' => 'India', 'io' => 'British Indian Ocean Territory', 'iq' => 'Iraq', 'ir' => 'Iran',
        'is' => 'Iceland', 'it' => 'Italy', 'je' => 'Jersey', 'jm' => 'Jamaica',
        'jo' => 'Jordan', 'jp' => 'Japan', 'ke' => 'Kenya', 'kg' => 'Kyrgyzstan',
        'kh' => 'Cambodia', 'ki' => 'Kiribati', 'km' => 'Comoros', 'kn' => 'Saint Kitts and Nevis',
        'kp' => 'North Korea', 'kr' => 'South Korea', 'kw' => 'Kuwait', 'ky' => 'Cayman Islands',
        'kz' => 'Kazakhstan', 'la' => 'Laos', 'lb' => 'Lebanon', 'lc' => 'Saint Lucia',
        'li' => 'Liechtenstein', 'lk' => 'Sri Lanka', 'lr' => 'Liberia', 'ls' => 'Lesotho',
        'lt' => 'Lithuania', 'lu' => 'Luxembourg', 'lv' => 'Latvia', 'ly' => 'Libya',
        'ma' => 'Morocco', 'mc' => 'Monaco', 'md' => 'Moldova', 'me' => 'Montenegro',
        'mf' => 'Saint Martin', 'mg' => 'Madagascar', 'mh' => 'Marshall Islands', 'mk' => 'North Macedonia',
        'ml' => 'Mali', 'mm' => 'Myanmar', 'mn' => 'Mongolia', 'mo' => 'Macao',
        'mp' => 'Northern Mariana Islands', 'mq' => 'Martinique', 'mr' => 'Mauritania', 'ms' => 'Montserrat',
        'mt' => 'Malta', 'mu' => 'Mauritius', 'mv' => 'Maldives', 'mw' => 'Malawi',
        'mx' => 'Mexico', 'my' => 'Malaysia', 'mz' => 'Mozambique', 'na' => 'Namibia',
        'nc' => 'New Caledonia', 'ne' => 'Niger', 'nf' => 'Norfolk Island', 'ng' => 'Nigeria',
        'ni' => 'Nicaragua', 'nl' => 'Netherlands', 'no' => 'Norway', 'np' => 'Nepal',
        'nr' => 'Nauru', 'nu' => 'Niue', 'nz' => 'New Zealand', 'om' => 'Oman',
        'pa' => 'Panama', 'pe' => 'Peru', 'pf' => 'French Polynesia', 'pg' => 'Papua New Guinea',
        'ph' => 'Philippines', 'pk' => 'Pakistan', 'pl' => 'Poland', 'pm' => 'Saint Pierre and Miquelon',
        'pn' => 'Pitcairn Islands', 'pr' => 'Puerto Rico', 'ps' => 'Palestine', 'pt' => 'Portugal',
        'pw' => 'Palau', 'py' => 'Paraguay', 'qa' => 'Qatar', 're' => 'Réunion',
        'ro' => 'Romania', 'rs' => 'Serbia', 'ru' => 'Russia', 'rw' => 'Rwanda',
        'sa' => 'Saudi Arabia', 'sb' => 'Solomon Islands', 'sc' => 'Seychelles', 'sd' => 'Sudan',
        'se' => 'Sweden', 'sg' => 'Singapore', 'sh' => 'Saint Helena', 'si' => 'Slovenia',
        'sj' => 'Svalbard and Jan Mayen', 'sk' => 'Slovakia', 'sl' => 'Sierra Leone', 'sm' => 'San Marino',
        'sn' => 'Senegal', 'so' => 'Somalia', 'sr' => 'Suriname', 'ss' => 'South Sudan',
        'st' => 'São Tomé and Príncipe', 'sv' => 'El Salvador', 'sx' => 'Sint Maarten', 'sy' => 'Syria',
        'sz' => 'Eswatini', 'tc' => 'Turks and Caicos Islands', 'td' => 'Chad', 'tf' => 'French Southern Territories',
        'tg' => 'Togo', 'th' => 'Thailand', 'tj' => 'Tajikistan', 'tk' => 'Tokelau',
        'tl' => 'East Timor', 'tm' => 'Turkmenistan', 'tn' => 'Tunisia', 'to' => 'Tonga',
        'tr' => 'Turkey', 'tt' => 'Trinidad and Tobago', 'tv' => 'Tuvalu', 'tw' => 'Taiwan',
        'tz' => 'Tanzania', 'ua' => 'Ukraine', 'ug' => 'Uganda', 'um' => 'U.S. Minor Outlying Islands',
        'us' => 'United States', 'uy' => 'Uruguay', 'uz' => 'Uzbekistan', 'va' => 'Vatican City',
        'vc' => 'Saint Vincent and the Grenadines', 've' => 'Venezuela', 'vg' => 'British Virgin Islands',
        'vi' => 'U.S. Virgin Islands', 'vn' => 'Vietnam', 'vu' => 'Vanuatu', 'wf' => 'Wallis and Futuna',
        'ws' => 'Samoa', 'ye' => 'Yemen', 'yt' => 'Mayotte', 'za' => 'South Africa',
        'zm' => 'Zambia', 'zw' => 'Zimbabwe', 'xx' => 'Unknown'
    ];
    
    return $countries[strtolower($country_code)] ?? 'Unknown';
}

function is_mobile_device(): bool {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    
    $mobile_patterns = [
        '/Mobile/i',
        '/Android.*Mobile/i', 
        '/iPhone/i',
        '/iPod/i',
        '/BlackBerry/i',
        '/Windows Phone/i',
        '/Opera Mini/i',
        '/IEMobile/i',
        '/Mobile Safari/i',
        '/webOS/i',
        '/Fennec/i',
        '/Maemo/i'
    ];
    
    
    $is_mobile_ua = false;
    foreach ($mobile_patterns as $pattern) {
        if (preg_match($pattern, $user_agent)) {
            $is_mobile_ua = true;
            break;
        }
    }
    
    
    if (!$is_mobile_ua) {
        return false;
    }
    
    
    
    $orientation = $_SESSION['device_orientation'] ?? $_GET['orientation'] ?? $_POST['orientation'] ?? null;
    $screen_width = $_SESSION['screen_width'] ?? $_GET['screen_width'] ?? $_POST['screen_width'] ?? null;
    $screen_height = $_SESSION['screen_height'] ?? $_GET['screen_height'] ?? $_POST['screen_height'] ?? null;
    
    
    if ($orientation === null || $screen_width === null || $screen_height === null) {
        return true; 
    }
    
    
    $is_landscape = ($orientation === 'landscape') || ($screen_width > $screen_height);
    
    
    $is_small_resolution = ($screen_width <= 768) || ($screen_height <= 480);
    
    
    return $is_mobile_ua && !$is_landscape && $is_small_resolution;
}

?>

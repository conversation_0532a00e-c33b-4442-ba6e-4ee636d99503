<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/captcha_functions.php';
require_once __DIR__ . '/../../includes/license_encryption_functions.php';

$license_code = '';
$error_message = '';
$success_message = '';
$download_ready = false;
$download_info = null;
$license = null;
$requires_token = false;
$requires_email_verification = false;
$security_token = '';
$verification_email = '';
$verification_step = 'license_check'; 
$captcha = null;

if (isset($_GET['email_token'])) {
    $email_token = sanitize_input($_GET['email_token']);
    $verification_step = 'token_verification';

    
    $token_verification = verify_email_download_token($email_token);

    if ($token_verification['valid']) {
        
        $license_code = $token_verification['license_code'];

        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];
            $success_message = 'Token de download verificado. Clique no botão abaixo para fazer o download.';
            $verification_step = 'download_ready';

            
            $download_result = process_download_request($license_code, null, $email_token, $verification_email);

            if ($download_result['success']) {
                
                $_SESSION['download_file'] = [
                    'path' => $download_result['file_path'],
                    'name' => $download_result['file_name'],
                    'expires' => time() + 300 
                ];

                
                session_write_close();

                
                $token = bin2hex(random_bytes(16));
                $download_url = BASE_URL . '/index.php?view=download_file&token=' . $token;
                $download_url = add_session_param_to_url($download_url);

                
                header('Location: ' . $download_url);
                exit;
            } else {
                $error_message = $download_result['message'];
            }
        } else {
            $error_message = $validity['message'];
            $license = get_license_by_code($license_code);
        }
    } else {
        $error_message = $token_verification['message'];
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['security_token']) && isset($_POST['license_code'])) {
    $license_code = sanitize_input($_POST['license_code']);
    $security_token = sanitize_input($_POST['security_token']);
    $verification_step = 'token_verification';

    if (empty($license_code)) {
        $error_message = 'Por favor, insira um código de licença.';
    } elseif (empty($security_token)) {
        $error_message = 'Por favor, insira o código de segurança.';
    } else {
        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            $token_verification = verify_security_token($license['id'], $security_token);

            if ($token_verification['valid']) {
                $success_message = 'Código de segurança verificado. Clique no botão abaixo para fazer o download.';
                $verification_step = 'download_ready';

                
                $download_result = process_download_request($license_code, $security_token, null, $verification_email);

                if ($download_result['success']) {
                    
                    $_SESSION['download_file'] = [
                        'path' => $download_result['file_path'],
                        'name' => $download_result['file_name'],
                        'expires' => time() + 300 
                    ];

                    
                    session_write_close();

                    
                    $token = bin2hex(random_bytes(16));
                    $download_url = BASE_URL . '/index.php?view=download_file&token=' . $token;
                    $download_url = add_session_param_to_url($download_url);

                    
                    header('Location: ' . $download_url);
                    exit;
                } else {
                    $error_message = $download_result['message'];
                }
            } else {
                $error_message = $token_verification['message'];
                $requires_token = true;
            }
        } else {
            $error_message = $validity['message'];
            $license = get_license_by_code($license_code);
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['license_code']) && isset($_POST['verification_email']) && isset($_POST['captcha_answer'])) {
    $license_code = sanitize_input($_POST['license_code']);
    $verification_email = sanitize_input($_POST['verification_email']);
    $captcha_answer = (int)sanitize_input($_POST['captcha_answer']);
    $verification_step = 'email_verification';

    if (empty($license_code)) {
        $error_message = 'Por favor, insira um código de licença.';
    } elseif (empty($verification_email)) {
        $error_message = 'Por favor, insira o email associado à licença.';
    } elseif (!verify_captcha($captcha_answer)) {
        $error_message = 'O código de verificação está incorreto. Por favor, tente novamente.';
        $requires_email_verification = true;

        
        $license = get_license_by_code($license_code);

        
        $captcha = null;
    } else {
        
        $validity = check_license_validity($license_code, true, $verification_email);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            if (isset($validity['requires_email_verification']) && $validity['requires_email_verification']) {
                $requires_email_verification = true;
                $error_message = 'O email fornecido não corresponde ao email associado a esta licença.';

                
                $captcha = null;
            }
            
            elseif (isset($validity['requires_token']) && $validity['requires_token']) {
                $requires_token = true;
                $success_message = $validity['message'];
                $verification_step = 'token_verification';
            } else {
                $success_message = 'Licença válida. Clique no botão abaixo para fazer o download.';
                $verification_step = 'download_ready';
            }
        } else {
            
            $license_data = get_license_by_code($license_code);
            if ($license_data) {
                $license = $license_data;
                $error_message = $validity['message'];
            } else {
                $error_message = $validity['message'];
            }
        }
    }
}

elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['license_code'])) {
    $license_code = sanitize_input($_POST['license_code']);

    if (empty($license_code)) {
        $error_message = 'Por favor, insira um código de licença.';
    } else {
        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            $requires_email_verification = true;
            $verification_step = 'email_verification';
            $success_message = 'Licença encontrada. Por favor, forneça o email associado a esta licença.';
        } else {
            
            $license_data = get_license_by_code($license_code);
            if ($license_data) {
                $license = $license_data;
                $error_message = $validity['message'];
            } else {
                $error_message = $validity['message'];
            }
        }
    }
} elseif (isset($_GET['code'])) {
    
    $license_code = sanitize_input($_GET['code']);

    if (!empty($license_code)) {
        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            $requires_email_verification = true;
            $verification_step = 'email_verification';
            $success_message = 'Licença encontrada. Por favor, forneça o email associado a esta licença.';
        } else {
            
            $license_data = get_license_by_code($license_code);
            if ($license_data) {
                $license = $license_data;
                $error_message = $validity['message'];
            } else {
                $error_message = $validity['message'];
            }
        }
    } else {
        $error_message = 'Código de licença não fornecido.';
    }
}

if (isset($_GET['download']) && $_GET['download'] === '1' && !empty($license_code)) {
    $verification_step = 'download_ready';

    
    $download_result = process_download_request($license_code, null, null, $verification_email);

    if ($download_result['success']) {
        
        $_SESSION['download_file'] = [
            'path' => $download_result['file_path'],
            'name' => $download_result['file_name'],
            'expires' => time() + 300 
        ];

        
        session_write_close();

        
        $token = bin2hex(random_bytes(16));
        $download_url = BASE_URL . '/index.php?view=download_file&token=' . $token;
        $download_url = add_session_param_to_url($download_url);

        
        header('Location: ' . $download_url);
        exit;
    } else {
        $error_message = $download_result['message'];

        
        if (isset($download_result['requires_email_verification']) && $download_result['requires_email_verification']) {
            $requires_email_verification = true;
            $license = $download_result['license'];
            $verification_step = 'email_verification';
        }
        
        elseif (isset($download_result['requires_token']) && $download_result['requires_token']) {
            $requires_token = true;
            $license = $download_result['license'];
            $verification_step = 'token_verification';
        }
    }
}

if (isset($_GET['error'])) {
    $error_code = sanitize_input($_GET['error']);

    switch ($error_code) {
        case 'invalid_session':
            $error_message = 'Sessão de download inválida. Por favor, tente novamente.';
            break;
        case 'expired':
            $error_message = 'O link de download expirou. Por favor, tente novamente.';
            break;
        case 'file_not_found':
            $error_message = 'Arquivo não encontrado. Por favor, entre em contato com o suporte.';
            break;
        default:
            $error_message = 'Ocorreu um erro durante o download. Por favor, tente novamente.';
    }
}

$digital_products = [];
$file_types = [];

if ($license) {
    
    $digital_products = get_license_digital_products($license['id']);

    
    foreach ($digital_products as $product) {
        $product_file_types = get_digital_product_file_types($product['id']);
        $file_types = array_merge($file_types, $product_file_types);
    }

    
    $unique_file_types = [];
    foreach ($file_types as $type) {
        $unique_file_types[$type['id']] = $type;
    }
    $file_types = array_values($unique_file_types);
}
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold">Verificação de Licença e/ou Download de Produtos Digitais</h1>
    </div>

    <?php if (!empty($error_message)): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline">
                <?php
                
                if ($error_message === 'O período de download para esta licença expirou.' && isset($license) && $license['status'] === 'active'): ?>
                    O período de download para esta licença expirou, mas a licença continua ativa. Não é possível fazer o download neste momento.
                <?php else: ?>
                    <?= sanitize_input($error_message) ?>
                <?php endif; ?>
            </span>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Sucesso!</strong>
            <span class="block sm:inline"><?= sanitize_input($success_message) ?></span>
        </div>
    <?php endif; ?>

    <!-- Step 1: License Verification Form (Initial Step) -->
    <?php if ($verification_step === 'license_check' && !$license): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Como verificar uma licença :</h2>
            <p class="mb-4">Para verificar o status de uma licença, deve insirir o código da mesma no campo abaixo. Ao submeter a mesma, será verificada a sua validade! Em alguns casos é ainda possivél fazer o dos seus conteúdos aqui, mas para isso deve proceder a uma segunda fase de autenticação mais à frente.</p>

            <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                <?php if (function_exists('csrf_token_field')): ?>
                    <?= csrf_token_field() ?>
                <?php endif; ?>

                <div class="mb-4">
                    <label for="license_code" class="block text-sm font-medium text-gray-300 mb-1">Código de Licença</label>
                    <input type="text" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                           id="license_code" name="license_code" value="<?= sanitize_input($license_code) ?>" required
                           placeholder="Ex: XXXX-XXXX-JCS-XXXX-XXXX">
                    <p class="text-sm text-gray-400 mt-1">
                        O código de licença é único e pessoal! Juntamente com a sua fatura, é este código que serve de prova de compra e é aquilo o autoriza a usar os conteúdos digitais associados.
                    </p>
                </div>

                <div class="mt-6">
                    <button type="submit" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded font-medium">
                        <i class="ri-shield-check-line mr-2"></i> Verificar Licença
                    </button>
                </div>
            </form>
        </div>

        <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-0.5">
                    <i class="ri-information-line text-blue-400 text-lg"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-400">Não encontrou seu código de licença?</h3>
                    <div class="mt-2 text-sm text-blue-300">
                        <p>Se não recebeu (ou perdeu) o seu código de licença, ou se não consegue aceder aos seus conteúdos, por favor entre em contato com o suporte. Posso ajudá-lo a fazer a sua recuperação manualmente!</p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Step 2: License Details and Status -->
    <?php if ($license): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Detalhes da Licença</h2>

            <?php
            
            $status_class = 'bg-green-900 text-green-100';
            $status_text = 'Ativo';
            $is_revoked = false;
            $is_waiting_payment = false;
            $is_inactive = false;

            
            $download_status_class = 'bg-green-900 text-green-100';
            $download_status_text = 'Disponível';
            $is_download_expired = false;
            $is_limit_reached = false;

            
            if ($license['status'] === 'waiting_payment') {
                $status_class = 'bg-yellow-900 text-yellow-100';
                $status_text = 'Aguardando Pagamento';
                $is_waiting_payment = true;
            } elseif ($license['status'] === 'disabled' || $license['status'] === 'canceled') {
                $status_class = 'bg-red-900 text-red-100';
                $status_text = ($license['status'] === 'disabled') ? 'Desativada' : 'Cancelada';
                $is_revoked = true;
                $is_inactive = true;
            }

            
            if (strtotime($license['expiry_date']) < time()) {
                $download_status_class = 'bg-red-900 text-red-100';
                $download_status_text = 'Expirado';
                $is_download_expired = true;
            } elseif ($license['downloads_used'] >= $license['download_limit']) {
                $download_status_class = 'bg-orange-900 text-orange-100';
                $download_status_text = 'Limite Atingido';
                $is_limit_reached = true;
            }

            
            
            $decrypted_name = get_decrypted_license_name($license);
            $decrypted_email = get_decrypted_license_email($license);

            
            if ($is_inactive) {
                $censored_name = get_license_censored_name($decrypted_name, 'canceled');
                $censored_email = get_license_censored_email($decrypted_email, 'canceled');
            } else {
                $censored_name = get_license_censored_name($decrypted_name, $license['status']);
                $censored_email = get_license_censored_email($decrypted_email, $license['status']);
            }
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm mb-1">Código de Licença:</p>
                    <p class="font-medium"><?= sanitize_input($license['license_code']) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Status da Licença:</p>
                    <p class="font-medium">
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $status_class ?>"><?= $status_text ?></span>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                    <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Email:</p>
                    <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Validade do Download:</p>
                    <p class="font-medium">
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $download_status_class ?>"><?= $download_status_text ?></span>
                        <?php if ($is_download_expired): ?>
                            <span class="text-sm text-gray-400 ml-2">
                                (Expirou em <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>)
                            </span>
                        <?php elseif (!$is_inactive && !$is_waiting_payment): ?>
                            <span class="text-sm text-gray-400 ml-2">
                                (Até <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>)
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Downloads:</p>
                    <p class="font-medium"><?= $license['downloads_used'] ?> de <?= $license['download_limit'] ?> utilizados</p>
                </div>
            </div>

            <?php if ($verification_step === 'download_ready' && $license['status'] === 'active' &&
                      strtotime($license['expiry_date']) > time() &&
                      $license['downloads_used'] < $license['download_limit']): ?>
                <!-- Show full license details only when ready to download -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">Data de Expiração:</p>
                        <p class="font-medium">
                            <?= date('d/m/Y H:i', strtotime($license['expiry_date'])) ?>
                            <?php if (strtotime($license['expiry_date']) > time()): ?>
                                <span class="text-sm text-gray-400">
                                    (<?= ceil((strtotime($license['expiry_date']) - time()) / (60 * 60 * 24)) ?> dias restantes)
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm mb-1">Downloads:</p>
                        <p class="font-medium"><?= $license['downloads_used'] ?> de <?= $license['download_limit'] ?> utilizados</p>
                    </div>
                </div>

                <?php if (!empty($file_types)): ?>
                    <div class="mb-6">
                        <h3 class="text-lg font-medium mb-2">Extensões incluídas neste download:</h3>
                        <div class="flex flex-wrap gap-2">
                            <?php foreach ($file_types as $type): ?>
                                <span class="inline-block px-3 py-1 text-sm rounded bg-gray-800 text-gray-300">
                                    <?= sanitize_input($type['extension']) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Step 2: Email Verification Form -->
            <?php if ($verification_step === 'email_verification' && $requires_email_verification):
                
                if ($captcha === null) {
                    $captcha = generate_simple_captcha();
                }
            ?>
                <div class="mt-6 bg-blue-900/30 border border-blue-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-blue-300">Verificação de Email (se desejar fazer o seu download)</h3>
                    <p class="mb-4 text-blue-200">
                        <u>Para proteger seus arquivos digitais, preciso de verificar que você é o proprietário desta licença.</u><br>
                        Para esse efeito, por favor, insira abaixo o email associado a esta licença para receber no mesmo um código de segurança que lhe permitirá proceder ao download dos ficheiros associados.<br><br>
                        Note que este método de recuperação apenas é possível durante um tempo limitados (e por vezes limitadas) após a compra!
                    </p>

                    <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                        <?php if (function_exists('csrf_token_field')): ?>
                            <?= csrf_token_field() ?>
                        <?php endif; ?>

                        <input type="hidden" name="license_code" value="<?= sanitize_input($license_code) ?>">

                        <div class="mb-4">
                            <label for="verification_email" class="block text-sm font-medium text-blue-300 mb-1">Email</label>
                            <input type="email" class="w-full px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                                   id="verification_email" name="verification_email" value="<?= sanitize_input($verification_email) ?>" required
                                   placeholder="Digite o email associado à licença">
                            <p class="text-sm text-blue-300 mt-1">
                                Este deve ser o mesmo email que foi usado na compra do produto.
                            </p>
                        </div>

                        <div class="mb-4">
                            <label for="captcha_answer" class="block text-sm font-medium text-blue-300 mb-1">Verificação de Segurança</label>
                            <div class="flex items-center gap-3">
                                <div class="bg-gray-700 px-4 py-2 rounded text-white font-medium">
                                    <?= $captcha['question'] ?>
                                </div>
                                <input type="number" class="w-20 px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                                       id="captcha_answer" name="captcha_answer" required>
                            </div>
                            <p class="text-sm text-blue-300 mt-1">
                                Por favor, resolva esta operação matemática simples para verificar que você não é um robô.
                            </p>
                        </div>

                        <div class="mt-4 flex items-center gap-3">
                            <button type="submit" class="inline-block bg-blue-700 hover:bg-blue-600 text-white py-3 px-6 rounded font-medium">
                                <i class="ri-mail-check-line mr-2"></i> Verificar Email
                            </button>

                            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="inline-block bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded text-sm">
                                <i class="ri-arrow-left-line mr-1"></i> Voltar à Loja
                            </a>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Step 3: Security Token Verification Form -->
            <?php if ($verification_step === 'token_verification' && $requires_token): ?>
                <div class="mt-6 bg-blue-900/30 border border-blue-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-blue-300">Verificação de Segurança</h3>
                    <p class="mb-4 text-blue-200">
                        Para proteger seus arquivos digitais, enviamos um código de segurança para o seu email.
                        Por favor, verifique sua caixa de entrada e insira o código abaixo para continuar com o download.
                    </p>

                    <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                        <?php if (function_exists('csrf_token_field')): ?>
                            <?= csrf_token_field() ?>
                        <?php endif; ?>

                        <input type="hidden" name="license_code" value="<?= sanitize_input($license_code) ?>">

                        <div class="mb-4">
                            <label for="security_token" class="block text-sm font-medium text-blue-300 mb-1">Código de Segurança</label>
                            <input type="text" class="w-full px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                                   id="security_token" name="security_token" value="<?= sanitize_input($security_token) ?>" required
                                   placeholder="Digite o código de 6 dígitos" maxlength="6" minlength="6">
                            <p class="text-sm text-blue-300 mt-1">
                                O código de segurança foi enviado para o email associado a esta licença.
                                Se não recebeu o código, verifique sua pasta de spam ou solicite um novo código.
                            </p>
                        </div>

                        <div class="mt-4 flex items-center gap-3">
                            <button type="submit" class="inline-block bg-blue-700 hover:bg-blue-600 text-white py-3 px-6 rounded font-medium">
                                <i class="ri-shield-check-line mr-2"></i> Verificar Código
                            </button>

                            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="inline-block bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded text-sm">
                                <i class="ri-arrow-left-line mr-1"></i> Voltar à Loja
                            </a>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Step 4: Download Button or Status Message -->
            <?php if ($verification_step === 'download_ready'): ?>
                <div class="mt-6">
                    <?php if ($license['status'] === 'active' &&
                              !$is_download_expired &&
                              !$is_limit_reached): ?>
                        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license_code) . '&download=1') ?>"
                           class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded font-medium">
                            <i class="ri-download-line mr-2"></i> Baixar Arquivos
                        </a>
                    <?php else: ?>
                        <button class="inline-block bg-gray-700 text-gray-300 py-3 px-6 rounded font-medium cursor-not-allowed" disabled>
                            <i class="ri-download-line mr-2"></i> Baixar Arquivos
                        </button>
                        <p class="text-sm text-gray-400 mt-2">
                            <?php if ($license['status'] === 'waiting_payment'): ?>
                                Aguardando confirmação de pagamento para autorizar o download.
                            <?php elseif ($license['status'] === 'disabled' || $license['status'] === 'canceled'): ?>
                                Esta licença foi <?= ($license['status'] === 'disabled') ? 'desativada' : 'cancelada' ?>.
                                Entre em contato com o suporte para mais informações.
                            <?php elseif (strtotime($license['expiry_date']) < time()): ?>
                                O período de download expirou em <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>.
                                A licença continua <?= strtolower($status_text) ?>, mas não é mais possível fazer o download.
                                <br>
                                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=license_extension&license_id=' . $license['id']) ?>" class="text-primary hover:underline mt-2 inline-block">
                                    Solicitar extensão do período de download
                                </a>
                            <?php elseif ($license['downloads_used'] >= $license['download_limit']): ?>
                                Você atingiu o limite máximo de downloads para este produto.
                                <br>
                                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=additional_downloads&license_id=' . $license['id']) ?>" class="text-primary hover:underline mt-2 inline-block">
                                    Solicitar downloads adicionais
                                </a>
                            <?php endif; ?>
                        </p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- License Terms -->
        <div class="bg-gray-900 rounded-lg p-6">
            <h2 class="text-xl font-medium mb-4">Termos de Licença</h2>
            <div class="prose prose-invert max-w-none">
                <?php
                
                $digital_files_placeholder = get_page_placeholder_by_slug('ficheiros-digitais');

                if ($digital_files_placeholder):
                    
                    $placeholder_pages = get_pages_by_placeholder_id($digital_files_placeholder['id']);

                    if (!empty($placeholder_pages)):
                ?>
                    <h3 class="text-lg font-medium text-gray-200 mb-3"><?= htmlspecialchars($digital_files_placeholder['name']) ?></h3>
                    <ul class="space-y-2">
                        <?php foreach ($placeholder_pages as $page): ?>
                            <li>
                                <a href="<?= get_page_url($page['slug']) ?>" class="text-gray-400 hover:text-white flex items-center" target="_blank" rel="noopener noreferrer">
                                    <i class="ri-file-text-line mr-2"></i>
                                    <?= htmlspecialchars($page['title']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php
                    else:
                        
                        echo nl2br(sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')));
                    endif;
                else:
                    
                    echo nl2br(sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')));
                endif;
                ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php

function add_stock_column_to_products(PDO $pdo): bool
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='products';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            return true; 
        }

        
        $stmt = $pdo->query("PRAGMA table_info(products);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1); 

        
        if (!in_array('stock', $columns)) {
            $pdo->exec("ALTER TABLE products ADD COLUMN stock INTEGER NOT NULL DEFAULT 0;");
        } else {
        }
        return true;
    } catch (PDOException $e) {
        return false;
    }
}
?>
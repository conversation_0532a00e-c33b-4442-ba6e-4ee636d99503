<?php
if (!isset($attribute_id) || !filter_var($attribute_id, FILTER_VALIDATE_INT)) {
    echo '<div class="alert alert-danger">ID do Atributo inválido ou em falta.</div>';
    echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Atributos</a>';
    return;
}

$attribute = db_query("SELECT * FROM attributes WHERE id = :id", [':id' => $attribute_id], true);
if (!$attribute) {
    echo '<div class="alert alert-danger">Atributo pai não encontrado (ID: ' . $attribute_id . ').</div>';
    echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Atributos</a>';
    return;
}

$value_filter = isset($_GET['value']) ? trim($_GET['value']) : '';
$price_filter = isset($_GET['price_range']) ? $_GET['price_range'] : '';
$sort_option = isset($_GET['sort']) ? $_GET['sort'] : 'value_asc';

$values_per_page = 20;
$current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
$offset = ($current_page - 1) * $values_per_page;

$conditions = ['attribute_id = ?'];
$params = [$attribute_id];
$count_params = [$attribute_id];

if (!empty($value_filter)) {
    $conditions[] = "value_pt LIKE ?";
    $params[] = "%$value_filter%";
    $count_params[] = "%$value_filter%";
}

if ($price_filter !== '') {
    switch ($price_filter) {
        case 'free':
            $conditions[] = "(price_modifier = 0 OR price_modifier IS NULL)";
            break;
        case 'positive':
            $conditions[] = "price_modifier > 0";
            break;
        case 'negative':
            $conditions[] = "price_modifier < 0";
            break;
    }
}

$count_sql = "SELECT COUNT(*) as total FROM attribute_values WHERE " . implode(" AND ", $conditions);
$total_values_result = db_query($count_sql, $count_params, true, true);
$total_values = $total_values_result ? (int)$total_values_result['total'] : 0;
$total_pages = ceil($total_values / $values_per_page);

$sql = "SELECT * FROM attribute_values WHERE " . implode(" AND ", $conditions);

$sort_options = [
    'value_asc' => 'value_pt ASC',
    'value_desc' => 'value_pt DESC',
    'price_asc' => 'price_modifier ASC',
    'price_desc' => 'price_modifier DESC',
    'recent' => 'id DESC'
];

$order_by = isset($sort_options[$sort_option]) ? $sort_options[$sort_option] : $sort_options['value_asc'];
$sql .= " ORDER BY " . $order_by;
$sql .= " LIMIT " . (int)$values_per_page . " OFFSET " . (int)$offset;

$attribute_values = db_query($sql, $params, false, true);

if (isset($_GET['delete_id'])) {
    $delete_id = (int)$_GET['delete_id'];
    
    try {
        $deleted = db_query("DELETE FROM attribute_values WHERE id = ? AND attribute_id = ?", [$delete_id, $attribute_id]);
        if ($deleted) {
            add_flash_message('Valor do atributo removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover valor do atributo.', 'danger');
        }
    } catch (Exception $e) {
        add_flash_message('Erro ao remover valor: ' . $e->getMessage(), 'danger');
    }
    
    $redirect_url = 'admin.php?section=attributes&action=values&attribute_id=' . $attribute_id . '&' . get_session_id_param();
    echo '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="refresh" content="0;url=' . $redirect_url . '">
    <script>window.location.href = "' . $redirect_url . '";</script>
</head>
<body>
    <p>Valor removido com sucesso. Redirecionando...</p>
    <p>Se não for redirecionado automaticamente, <a href="' . $redirect_url . '">clique aqui</a>.</p>
</body>
</html>';
    exit;
}
?>

<h1>Gerir Valores para: <?= sanitize_input($attribute['name_pt']) ?></h1>

<div class="d-flex justify-content-between mb-3">
    <div>
        <a href="admin.php?section=attributes&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Voltar aos Atributos
        </a>
        <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&action_type=add&<?= get_session_id_param() ?>" class="btn btn-success ms-2">
            <i class="bi bi-plus-circle"></i> Adicionar Novo Valor
        </a>
    </div>

    <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <i class="bi bi-funnel"></i> Filtros
    </button>
</div>

<div class="collapse mb-4" id="filterCollapse">
    <div class="card card-body">
        <form method="get" action="admin.php" class="row g-3">
            <input type="hidden" name="section" value="attributes">
            <input type="hidden" name="action" value="values">
            <input type="hidden" name="attribute_id" value="<?= $attribute_id ?>">
            <?php
            $session_param = get_session_id_param();
            if (!empty($session_param)) {
                list($name, $value) = explode('=', $session_param);
                echo '<input type="hidden" name="' . $name . '" value="' . $value . '">';
            }
            ?>

            <div class="col-md-4">
                <label for="value" class="form-label">Pesquisar Valor</label>
                <input type="text" class="form-control" id="value" name="value" value="<?= htmlspecialchars($value_filter) ?>" placeholder="Digite para pesquisar...">
            </div>

            <div class="col-md-3">
                <label for="price_range" class="form-label">Modificador de Preço</label>
                <select class="form-select" id="price_range" name="price_range">
                    <option value="" <?= $price_filter === '' ? 'selected' : '' ?>>Todos</option>
                    <option value="free" <?= $price_filter === 'free' ? 'selected' : '' ?>>Gratuito (€0.00)</option>
                    <option value="positive" <?= $price_filter === 'positive' ? 'selected' : '' ?>>Acréscimo (+)</option>
                    <option value="negative" <?= $price_filter === 'negative' ? 'selected' : '' ?>>Desconto (-)</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="sort" class="form-label">Ordenar por</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="value_asc" <?= $sort_option === 'value_asc' ? 'selected' : '' ?>>Valor (A-Z)</option>
                    <option value="value_desc" <?= $sort_option === 'value_desc' ? 'selected' : '' ?>>Valor (Z-A)</option>
                    <option value="price_asc" <?= $sort_option === 'price_asc' ? 'selected' : '' ?>>Preço (Menor-Maior)</option>
                    <option value="price_desc" <?= $sort_option === 'price_desc' ? 'selected' : '' ?>>Preço (Maior-Menor)</option>
                    <option value="recent" <?= $sort_option === 'recent' ? 'selected' : '' ?>>Mais recentes</option>
                </select>
            </div>

            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">Filtrar</button>
                <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">Limpar</a>
            </div>
        </form>
    </div>
</div>

<!-- Real-time search -->
<div class="mb-3">
    <div class="input-group">
        <span class="input-group-text"><i class="bi bi-search"></i></span>
        <input type="text" class="form-control" id="realtime-search" placeholder="Pesquisa rápida em tempo real..." value="<?= htmlspecialchars($value_filter) ?>">
        <button class="btn btn-outline-secondary" type="button" id="clear-search">
            <i class="bi bi-x-circle"></i>
        </button>
    </div>
    <div class="form-text">Digite para filtrar os valores em tempo real</div>
</div>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title mb-0">Valores do Atributo (<?= $total_values ?> total)</h5>
            <?php if (!empty($value_filter) || $price_filter !== '' || $sort_option !== 'value_asc'): ?>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2">Filtros ativos</span>
                    <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Limpar filtros
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($value_filter) || $price_filter !== '' || $sort_option !== 'value_asc'): ?>
        <script>
            // Auto-expand filter panel when filters are active
            document.addEventListener('DOMContentLoaded', function() {
                const filterCollapse = document.getElementById('filterCollapse');
                if (filterCollapse) {
                    const bsCollapse = new bootstrap.Collapse(filterCollapse, {
                        toggle: true
                    });
                }
            });
        </script>
        <?php endif; ?>

        <?php if (empty($attribute_values)): ?>
            <div class="alert alert-info">Nenhum valor encontrado para este atributo.</div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="values-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Valor (PT)</th>
                            <th>Modificador de Preço</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($attribute_values as $value): ?>
                            <tr class="value-row" data-value="<?= strtolower(sanitize_input($value['value_pt'])) ?>">
                                <td><?= $value['id'] ?></td>
                                <td><?= sanitize_input($value['value_pt']) ?></td>
                                <td>
                                    <?php if ($value['price_modifier'] == 0 || $value['price_modifier'] === null): ?>
                                        <span class="text-muted">€0.00</span>
                                    <?php elseif ($value['price_modifier'] > 0): ?>
                                        <span class="text-success">+<?= format_price($value['price_modifier']) ?></span>
                                    <?php else: ?>
                                        <span class="text-danger"><?= format_price($value['price_modifier']) ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&action_type=edit&value_id=<?= $value['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&delete_id=<?= $value['id'] ?>&<?= get_session_id_param() ?>"
                                       class="btn btn-sm btn-outline-danger"
                                       title="Remover"
                                       onclick="return confirm('Tem a certeza que quer remover este valor? Esta ação não pode ser desfeita.');"> 
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    <?php
                    $query_params = ['attribute_id' => $attribute_id];
                    if (!empty($value_filter)) $query_params['value'] = $value_filter;
                    if ($price_filter !== '') $query_params['price_range'] = $price_filter;
                    if ($sort_option !== 'value_asc') $query_params['sort'] = $sort_option;
                    
                    $base_query_string = http_build_query(array_merge(['section' => 'attributes', 'action' => 'values'], $query_params));
                    $session_param_str = get_session_id_param();
                    if (!empty($session_param_str)) {
                        $base_query_string .= '&' . $session_param_str;
                    }
                    ?>

                    <!-- Previous Page -->
                    <li class="page-item <?= $current_page <= 1 ? 'disabled' : '' ?>">
                        <a class="page-link" href="?<?= $base_query_string ?>&p=<?= $current_page - 1 ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>

                    <?php
                    $num_links_to_show = 5;
                    $start_page = max(1, $current_page - floor($num_links_to_show / 2));
                    $end_page = min($total_pages, $start_page + $num_links_to_show - 1);
                    
                    if ($end_page == $total_pages) {
                        $start_page = max(1, $total_pages - $num_links_to_show + 1);
                    }

                    if ($start_page > 1): ?>
                        <li class="page-item"><a class="page-link" href="?<?= $base_query_string ?>&p=1">1</a></li>
                        <?php if ($start_page > 2): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                            <a class="page-link" href="?<?= $base_query_string ?>&p=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                        <li class="page-item"><a class="page-link" href="?<?= $base_query_string ?>&p=<?= $total_pages ?>"><?= $total_pages ?></a></li>
                    <?php endif; ?>

                    <!-- Next Page -->
                    <li class="page-item <?= $current_page >= $total_pages ? 'disabled' : '' ?>">
                        <a class="page-link" href="?<?= $base_query_string ?>&p=<?= $current_page + 1 ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const realtimeSearch = document.getElementById('realtime-search');
    const clearSearch = document.getElementById('clear-search');
    const table = document.getElementById('values-table');
    const tbody = table.querySelector('tbody');
    const attributeId = <?= $attribute_id ?>;
    const sessionId = '<?= get_session_id_param() ?>';
    
    let isSearching = false;
    let currentSearchTerm = '';
    
    // Real-time search functionality using AJAX
    function performSearch(searchTerm) {
        const term = searchTerm.trim();
        currentSearchTerm = term;
        
        // If empty search, reload the page to show original results
        if (term === '') {
            window.location.href = window.location.pathname + '?section=attributes&action=values&attribute_id=' + attributeId + '&' + sessionId;
            return;
        }
        
        if (isSearching) return;
        isSearching = true;
        
        // Show loading state
        tbody.innerHTML = '<tr><td colspan="4" class="text-center py-4"><i class="bi bi-hourglass-split"></i> Procurando...</td></tr>';
        
        // Prepare form data
        const formData = new FormData();
        formData.append('action', 'search_attribute_values');
        formData.append('attribute_id', attributeId);
        formData.append('search_term', term);
        formData.append('page', 1);
        formData.append('csrf_token', '<?= generate_csrf_token() ?>');
        
        fetch('admin_ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            isSearching = false;
            
            if (data.success) {
                displaySearchResults(data.data.values, term);
                
                // Hide pagination during search
                const pagination = document.querySelector('.pagination');
                if (pagination) {
                    pagination.style.display = 'none';
                }
            } else {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger py-4"><i class="bi bi-exclamation-triangle"></i> Erro: ' + data.message + '</td></tr>';
            }
        })
        .catch(error => {
            isSearching = false;
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger py-4"><i class="bi bi-exclamation-triangle"></i> Erro na pesquisa. Tente novamente.</td></tr>';
        });
    }
    
    function displaySearchResults(values, searchTerm) {
        if (values.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted py-4"><i class="bi bi-search"></i> Nenhum valor encontrado para "' + searchTerm + '"</td></tr>';
            return;
        }
        
        let html = '';
        values.forEach(value => {
            const priceDisplay = value.price_modifier == 0 || value.price_modifier === null 
                ? '<span class="text-muted">€0.00</span>'
                : value.price_modifier > 0 
                    ? '<span class="text-success">+€' + parseFloat(value.price_modifier).toFixed(2) + '</span>'
                    : '<span class="text-danger">€' + parseFloat(value.price_modifier).toFixed(2) + '</span>';
            
            html += `
                <tr class="value-row">
                    <td>${value.id}</td>
                    <td>${escapeHtml(value.value_pt)}</td>
                    <td>${priceDisplay}</td>
                    <td>
                        <a href="admin.php?section=attributes&action=values&attribute_id=${attributeId}&action_type=edit&value_id=${value.id}&${sessionId}" class="btn btn-sm btn-outline-primary" title="Editar">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <a href="admin.php?section=attributes&action=values&attribute_id=${attributeId}&delete_id=${value.id}&${sessionId}"
                           class="btn btn-sm btn-outline-danger"
                           title="Remover"
                           onclick="return confirm('Tem a certeza que quer remover este valor? Esta ação não pode ser desfeita.');">
                            <i class="bi bi-trash"></i>
                        </a>
                    </td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Debounced search
    let searchTimeout;
    realtimeSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch(this.value);
        }, 300);
    });
    
    // Clear search
    clearSearch.addEventListener('click', function() {
        realtimeSearch.value = '';
        currentSearchTerm = '';
        // Reload page to show original results
        window.location.href = window.location.pathname + '?section=attributes&action=values&attribute_id=' + attributeId + '&' + sessionId;
    });
    
    // Focus search on Ctrl+F or Cmd+F
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            realtimeSearch.focus();
            realtimeSearch.select();
        }
    });
    
    // Show clear button when there's search text
    realtimeSearch.addEventListener('input', function() {
        clearSearch.style.display = this.value ? 'block' : 'none';
    });
    
    // Initial state
    clearSearch.style.display = realtimeSearch.value ? 'block' : 'none';
});
</script>
<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/session.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/security.php';

$current_session_id = start_cookieless_session();

$is_admin_logged_in = false;
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $is_admin_logged_in = true;
} else {
    
    header('Location: admin.php?' . get_session_id_param());
    exit;
}

$file = isset($_GET['file']) ? $_GET['file'] : '';

if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file)) {
    die('Invalid file name');
}

if (strpos($file, 'database_backup_') !== 0 || substr($file, -7) !== '.sqlite') {
    die('Invalid backup file');
}

$backup_dir = '../backups';

$file_path = $backup_dir . '/' . $file;

if (!file_exists($file_path)) {
    die('Backup file not found');
}

header('Content-Description: File Transfer');
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . basename($file_path) . '"');
header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($file_path));

ob_clean();
flush();

readfile($file_path);
exit;

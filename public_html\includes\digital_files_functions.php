<?php

function get_all_digital_files(array $filters = [], int $page = 0, int $per_page = 0): array
{
    $conditions = [];
    $params = [];

    
    if (!empty($filters['name'])) {
        $conditions[] = "(original_filename LIKE :name OR display_name LIKE :name OR short_name LIKE :name)";
        $params[':name'] = '%' . $filters['name'] . '%';
    }

    if (!empty($filters['file_type'])) {
        $conditions[] = "file_type LIKE :file_type";
        $params[':file_type'] = '%' . $filters['file_type'] . '%';
    }

    
    $sql = "SELECT * FROM digital_files";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $sql .= " ORDER BY created_at DESC";

    
    if ($page > 0 && $per_page > 0) {
        $offset = ($page - 1) * $per_page;
        $sql .= " LIMIT :limit OFFSET :offset";
        $params[':limit'] = $per_page;
        $params[':offset'] = $offset;
    }

    $result = db_query($sql, $params, false, true);

    if (is_array($result)) {
        
        foreach ($result as &$file) {
            $file['file_type_ids'] = get_digital_file_file_type_ids((int)$file['id']);
        }
        return $result;
    }

    return [];
}

function get_orphaned_digital_files(): array
{
    $sql = "SELECT df.* FROM digital_files df
            LEFT JOIN digital_products dp ON df.id = dp.digital_file_id
            WHERE dp.id IS NULL
            ORDER BY df.created_at DESC";
    $result = db_query($sql, [], false, true);
    return is_array($result) ? $result : [];
}

function get_digital_file_by_id(int $file_id): array|false
{
    if ($file_id <= 0) return false;

    $sql = "SELECT * FROM digital_files WHERE id = :id";
    return db_query($sql, [':id' => $file_id], true);
}

function create_digital_file(array $data): int|false
{
    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        
        $file_path = $data['file_path'];
        if (strpos($file_path, 'C:\\') === 0 || strpos($file_path, 'C:/') === 0) {
            
            $filename = basename($file_path);
            
            $file_path = '../digital_products/' . $filename;
        }

        $sql = "INSERT INTO digital_files (
                    original_filename, display_name, short_name, file_path, file_size, file_type, description,
                    created_at, updated_at
                ) VALUES (
                    :original_filename, :display_name, :short_name, :file_path, :file_size, :file_type, :description,
                    datetime('now', 'localtime'), datetime('now', 'localtime')
                )";

        $params = [
            ':original_filename' => $data['original_filename'],
            ':display_name' => $data['display_name'] ?? $data['original_filename'],
            ':short_name' => $data['short_name'] ?? '',
            ':file_path' => $file_path,
            ':file_size' => $data['file_size'],
            ':file_type' => $data['file_type'],
            ':description' => $data['description'] ?? ''
        ];

        $result = db_query($sql, $params);

        if ($result === false) {
            throw new Exception("Failed to insert digital file record");
        }

        $last_id = 0;
        if ($pdo instanceof PDO) {
            $last_id = (int)$pdo->lastInsertId();
            if ($last_id === 0) {
                throw new Exception("Failed to get last insert ID");
            }
        } else {
            throw new Exception("Invalid PDO connection");
        }

        
        if (isset($data['file_types']) && is_array($data['file_types']) && !empty($data['file_types'])) {
            
            
            update_digital_file_file_types($last_id, $data['file_types'], false);
        } else {
        }

        $pdo->commit();
        return $last_id;

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function update_digital_file(int $file_id, array $data): bool
{
    if ($file_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $sql = "UPDATE digital_files SET updated_at = datetime('now', 'localtime')";
        $params = [':id' => $file_id];

        
        if (isset($data['description'])) {
            $sql .= ", description = :description";
            $params[':description'] = $data['description'];
        }
        if (isset($data['display_name'])) {
            $sql .= ", display_name = :display_name";
            $params[':display_name'] = $data['display_name'];
        }
        if (isset($data['short_name'])) {
            $sql .= ", short_name = :short_name";
            $params[':short_name'] = $data['short_name'];
        }

        $sql .= " WHERE id = :id";

        $result = db_query($sql, $params);
        if ($result === false) {
            throw new Exception("Failed to update digital file record");
        }

        
        if (isset($data['file_types'])) {
            
            update_digital_file_file_types($file_id, $data['file_types']);
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function delete_digital_file(int $file_id): bool
{
    if ($file_id <= 0) return false;

    $file = get_digital_file_by_id($file_id);
    if (!$file) return false;

    $usage = get_digital_file_usage($file_id);
    if (count($usage) > 0) {
        return false;
    }

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $sql = "DELETE FROM digital_files WHERE id = :id";
        $result = db_query($sql, [':id' => $file_id]);

        if ($result === false) {
            throw new Exception("Failed to delete digital file record");
        }

        if (file_exists($file['file_path'])) {
            if (!@unlink($file['file_path'])) {
            }
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_digital_file_usage(int $file_id): array
{
    if ($file_id <= 0) return [];

    $sql = "SELECT p.id, p.name_pt FROM products p
            JOIN digital_products dp ON p.id = dp.product_id
            WHERE dp.digital_file_id = :file_id";

    $result = db_query($sql, [':file_id' => $file_id], false, true);
    return is_array($result) ? $result : [];
}

function get_all_digital_products(): array
{

    $pdo = get_db_connection();
    $stmt = $pdo->query("PRAGMA table_info(digital_files)");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

    $sql = "SELECT p.id as product_id, p.name_pt, p.slug, p.description_pt, p.base_price, p.is_active, p.created_at AS product_created_at, p.updated_at AS product_updated_at, p.product_type, p.seo_title, p.seo_description, p.seo_keywords, p.og_title, p.og_description, p.og_image, p.twitter_card, p.twitter_title, p.twitter_description, p.twitter_image,
                   dp.id as digital_product_id, dp.expiry_days, dp.download_limit, dp.digital_file_id,
                   df.file_path, df.original_filename AS df_original_filename";

    if (in_array('display_name', $columns)) {
        $sql .= ", df.display_name AS df_display_name";
    } else if (in_array('short_name', $columns)) {

        $sql .= ", df.short_name AS df_display_name";
    } else {

        $sql .= ", df.original_filename AS df_display_name";
    }

    $sql .= " FROM products p
              JOIN digital_products dp ON p.id = dp.product_id
              LEFT JOIN digital_files df ON dp.digital_file_id = df.id
              WHERE p.product_type = 'digital'
              ORDER BY p.name_pt ASC";

    $result = db_query($sql, [], false, true);
    return is_array($result) ? $result : [];
}

function format_file_size(int $size): string
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    return round($size, 2) . ' ' . $units[$i];
}

function count_digital_files(array $filters = []): int
{
    $conditions = [];
    $params = [];

    
    if (!empty($filters['name'])) {
        $conditions[] = "(original_filename LIKE :name OR display_name LIKE :name OR short_name LIKE :name)";
        $params[':name'] = '%' . $filters['name'] . '%';
    }

    if (!empty($filters['file_type'])) {
        $conditions[] = "file_type LIKE :file_type";
        $params[':file_type'] = '%' . $filters['file_type'] . '%';
    }

    
    $sql = "SELECT COUNT(*) as total FROM digital_files";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $result = db_query($sql, $params, true);
    return $result ? (int)$result['total'] : 0;
}

function get_digital_file_file_types(int $digital_file_id): array
{
    if ($digital_file_id <= 0) return [];

    
    $sql = "SELECT dfft.* FROM digital_files_file_types dfft
            JOIN digital_file_type_associations dfta ON dfft.id = dfta.file_type_id
            WHERE dfta.digital_file_id = :digital_file_id";

    $result = db_query($sql, [':digital_file_id' => $digital_file_id], false, true);
    return is_array($result) ? $result : [];
}

function get_digital_file_file_type_ids(int $digital_file_id): array
{
    if ($digital_file_id <= 0) {
        return [];
    }

    
    $sql = "SELECT file_type_id FROM digital_file_type_associations WHERE digital_file_id = :digital_file_id";
    $result = db_query($sql, [':digital_file_id' => $digital_file_id], false, true);

    if (is_array($result) && !empty($result)) {
        
        $ids = array_map(function($row) {
            return (int)$row['file_type_id'];
        }, $result);
        return $ids;
    }
    return [];
}

function update_digital_file_file_types(int $digital_file_id, array $file_type_ids, bool $manage_transaction = true): bool
{

    if ($digital_file_id <= 0) {
        return false;
    }

    
    $file = get_digital_file_by_id($digital_file_id);
    if (!$file) {
        return false;
    }

    
    
    if (empty($file_type_ids) && defined('PRESERVE_FILE_TYPE_ASSOCIATIONS') && PRESERVE_FILE_TYPE_ASSOCIATIONS) {
        
        $existing_file_type_ids = get_digital_file_file_type_ids($digital_file_id);
        if (!empty($existing_file_type_ids)) {
            $file_type_ids = $existing_file_type_ids;
        }
    }

    
    if (!empty($file_type_ids)) {
        $valid_types = [];
        $pdo = get_db_connection();
        if (!$pdo) {
            return false;
        }

        
        try {
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files_file_types'");
            $table_exists = $stmt->fetch();
            if (!$table_exists) {
                return false;
            }

            
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_file_type_associations'");
            $assoc_table_exists = $stmt->fetch();
            if (!$assoc_table_exists) {
                return false;
            }

            
            foreach ($file_type_ids as $type_id) {
                $stmt = $pdo->prepare("SELECT id FROM digital_files_file_types WHERE id = :id");
                $stmt->execute([':id' => $type_id]);
                $type = $stmt->fetch();
                if ($type) {
                    $valid_types[] = (int)$type_id;
                } else {
                }
            }

            
            $file_type_ids = $valid_types;
        } catch (Exception $e) {
            return false;
        }
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        $transaction_started = false;
        if ($manage_transaction && !$pdo->inTransaction()) {
            $pdo->beginTransaction();
            $transaction_started = true;
        } else {
        }

        
        $sql = "DELETE FROM digital_file_type_associations WHERE digital_file_id = :digital_file_id";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([':digital_file_id' => $digital_file_id]);

        if (!$result) {
        }

        
        if (!empty($file_type_ids)) {
            foreach ($file_type_ids as $file_type_id) {
                if (empty($file_type_id) || !is_numeric($file_type_id)) {
                    continue;
                }

                $sql = "INSERT INTO digital_file_type_associations (
                        digital_file_id, file_type_id, created_at
                    ) VALUES (
                        :digital_file_id, :file_type_id, datetime('now', 'localtime')
                    )";

                $params = [
                    ':digital_file_id' => $digital_file_id,
                    ':file_type_id' => $file_type_id
                ];

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute($params);

                if ($result) {
                } else {
                }
            }
        } else {
        }

        
        if ($transaction_started) {
            $pdo->commit();
        }

        
        $saved_types = get_digital_file_file_type_ids($digital_file_id);

        return true;
    } catch (Exception $e) {
        
        if ($transaction_started && $pdo->inTransaction()) {
            $pdo->rollBack();
        }

        
        if (!$manage_transaction) {
            throw $e;
        }
        return false;
    }
}

function get_upload_error_message(int $error_code): string
{
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return 'O arquivo excede o tamanho máximo permitido pelo servidor.';
        case UPLOAD_ERR_FORM_SIZE:
            return 'O arquivo excede o tamanho máximo permitido pelo formulário.';
        case UPLOAD_ERR_PARTIAL:
            return 'O arquivo foi apenas parcialmente carregado.';
        case UPLOAD_ERR_NO_FILE:
            return 'Nenhum arquivo foi carregado.';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Pasta temporária não encontrada.';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Falha ao gravar o arquivo no disco.';
        case UPLOAD_ERR_EXTENSION:
            return 'Uma extensão PHP interrompeu o upload do arquivo.';
        default:
            return 'Erro desconhecido ao carregar o arquivo.';
    }
}

function update_digital_product_file(int $digital_product_id, int $digital_file_id): bool
{
    if ($digital_product_id <= 0 || $digital_file_id <= 0) {
        return false;
    }

    $digital_file = get_digital_file_by_id($digital_file_id);
    if (!$digital_file) {
        return false;
    }

    $digital_product = db_query("SELECT * FROM digital_products WHERE id = :id", [':id' => $digital_product_id], true);
    if (!$digital_product) {
        return false;
    }

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        
        $sql = "UPDATE digital_products SET
                    digital_file_id = :digital_file_id,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $params = [
            ':id' => $digital_product_id,
            ':digital_file_id' => $digital_file_id
        ];

        $stmt = $pdo->prepare($sql);
        if (!$stmt->execute($params)) {
            throw new Exception("Failed to update digital product file");
        }

        
        
        

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

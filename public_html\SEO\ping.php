<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

$feedUrl    = "https://joaocesarsilva.com/feed_validated.xml";
$sitemapUrl = "https://joaocesarsilva.com/sitemap.xml";

$indexNowKey = "9968fc656ba0468bbfb0f3369373e219";

$keyLocation = "https://www.joaocesarsilva.com/9968fc656ba0468bbfb0f3369373e219.txt";
$host        = "www.joaocesarsilva.com"; 

$indexNowEndpoints = [
    'Bing'     => 'https://www.bing.com/indexnow',
    'Yep'      => 'https://indexnow.yep.com/indexnow',
    'IndexNow' => 'https://api.indexnow.org/indexnow'
];

function fetchContent($url) {
    $content = @file_get_contents($url);
    if ($content === false) {
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $content = curl_exec($ch);
        curl_close($ch);
    }
    return $content;
}

$feedContent = fetchContent($feedUrl);
if ($feedContent === false) {
    die("Error fetching feed content from $feedUrl");
}

$feedXml = simplexml_load_string($feedContent);
if ($feedXml === false) {
    die("Error parsing XML from feed file.");
}

$feedUrls = [];
if (isset($feedXml->channel->item)) {
    foreach ($feedXml->channel->item as $item) {
        
        if (isset($item->link)) {
            $linkUrl = trim((string)$item->link);
            if (!empty($linkUrl)) {
                $feedUrls[] = $linkUrl;
            }
        }
        
        if (isset($item->guid)) {
            $guidUrl = trim((string)$item->guid);
            if (!empty($guidUrl)) {
                $feedUrls[] = $guidUrl;
            }
        }
        
    }
}

$sitemapContent = fetchContent($sitemapUrl);
if ($sitemapContent === false) {
    die("Error fetching sitemap content from $sitemapUrl");
}

$sitemapXml = simplexml_load_string($sitemapContent);
if ($sitemapXml === false) {
    die("Error parsing XML from sitemap file.");
}

$sitemapUrls = [];

foreach ($sitemapXml->url as $urlNode) {
    if (isset($urlNode->loc)) {
        $url = trim((string)$urlNode->loc);
        if (!empty($url)) {
            $sitemapUrls[] = $url;
        }
    }
}

$allUrls = array_unique(array_merge($feedUrls, $sitemapUrls));

if (empty($allUrls)) {
    die("No URLs found in feed or sitemap.");
}

$payload = [
    "host"    => $host,
    "key"     => $indexNowKey,
    "urlList" => array_values($allUrls)
];

$jsonPayload = json_encode($payload, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
if ($jsonPayload === false) {
    die("Error encoding JSON payload.");
}

$results = []; 

foreach ($indexNowEndpoints as $engineName => $endpointUrl) {
    $ch = curl_init($endpointUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json; charset=utf-8",
        "Content-Length: " . strlen($jsonPayload)
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonPayload);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response   = curl_exec($ch);
    $httpCode   = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError  = curl_error($ch);
    curl_close($ch);

    $results[$engineName] = [
        'endpoint' => $endpointUrl,
        'httpCode' => $httpCode,
        'response' => $response ? $response : "[empty]",
        'error'    => $curlError
    ];
}

echo "<h2>IndexNow Submission Results</h2>";
foreach ($results as $engine => $result) {
    echo "<strong>$engine</strong> (Endpoint: {$result['endpoint']})<br>";
    if (!empty($result['error'])) {
        echo "Error: {$result['error']}<br>";
    } else {
        echo "HTTP Code: {$result['httpCode']}<br>";
        echo "Response: {$result['response']}<br>";
    }
    echo "<br>";
}

echo "<h3>Submitted URLs:</h3>";
foreach ($allUrls as $url) {
    echo "$url<br>";
}
?>

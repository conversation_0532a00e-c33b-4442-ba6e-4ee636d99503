<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JCS Studio - Galeria de Produtos</title>
    <meta name="description" content="Galeria de produtos JCS Studio - Visualização dos nossos produtos de criação digital, alojamentos web, apoio I.T. e personalização a laser.">
    <meta name="keywords" content="JCS Studio, galeria produtos, criação digital, alojamentos web, apoio IT, laser fibra, medalhas identificação animal">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://joaocesarsilva.com/">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="JCS Studio - Galeria de Produtos">
    <meta property="og:description" content="Galeria de produtos JCS Studio - Visualização dos nossos produtos de criação digital, alojamentos web, apoio I.T. e personalização a laser.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://joaocesarsilva.com/">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="JCS Studio - Galeria de Produtos">
    <meta name="twitter:description" content="Galeria de produtos JCS Studio - Visualização dos nossos produtos de criação digital, alojamentos web, apoio I.T. e personalização a laser.">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 3rem 0 2rem 0;
            gap: 1rem;
        }
        
        .pagination-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .pagination-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .pagination-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }
        
        .page-info {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            padding: 0 1rem;
        }
        
        .product-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            border-bottom: 1px solid #e9ecef;
        }
        
        .product-content {
            padding: 1.5rem;
        }
        
        .product-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            line-height: 1.4;
        }
        
        .product-category {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }
        
        .product-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .product-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .product-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            text-align: center;
        }
        
        .stats {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .stats h2 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .product-date {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
            font-style: italic;
        }
        
        .navigation-map {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 3rem 0 2rem 0;
            text-align: center;
        }
        
        .navigation-map h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .page-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .page-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .page-link.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transform: scale(1.1);
        }
        
        .footer-disclaimer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 3rem 2rem 2rem 2rem;
            margin-top: 4rem;
            border-radius: 15px 15px 0 0;
        }
        
        .disclaimer-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .disclaimer-content h4 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: #ecf0f1;
        }
        
        .disclaimer-content p {
            line-height: 1.8;
            margin-bottom: 1.5rem;
            color: #bdc3c7;
            font-size: 1.1rem;
        }
        
        .main-site-link {
            display: inline-block;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1.2rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        
        .main-site-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }
        
        .footer-note {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #34495e;
            color: #95a5a6;
        }
        
        .page-ellipsis {
            color: #6c757d;
            padding: 8px 12px;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>JCS Studio</h1>
            <p class="subtitle">Galeria de Produtos - Criação Digital & Personalização a Laser</p>
        </header>
        
        <div class="stats">
            <h2>Catálogo de Produtos</h2>
            <p>Explore nossa coleção completa de produtos personalizados</p>
            <div id="product-count"></div>
        </div>
        
        <div id="loading" class="loading">
            Carregando produtos...
        </div>
        
        <div id="error" class="error" style="display: none;">
            Erro ao carregar produtos. Tente novamente mais tarde.
        </div>
        
        <div id="products-container" class="products-grid"></div>
        
        <div id="pagination" class="pagination" style="display: none;">
            <button id="prev-btn" class="pagination-btn" onclick="changePage(-1)">« Anterior</button>
            <span id="page-info" class="page-info"></span>
            <button id="next-btn" class="pagination-btn" onclick="changePage(1)">Próximo »</button>
        </div>
        
        <!-- Navigation Map -->
        <div id="navigation-map" class="navigation-map" style="display: none;">
            <h3>Navegação Rápida</h3>
            <div id="page-links" class="page-links"></div>
        </div>
        
        <!-- Footer Disclaimer -->
        <footer class="footer-disclaimer">
            <div class="disclaimer-content">
                <h4>Aviso Legal</h4>
                <p>Esta é uma galeria de produtos destinada exclusivamente à visualização dos nossos produtos. Este site não utiliza cookies e destina-se apenas a fins de demonstração.</p>
                <p>Para informações detalhadas, compras e suporte técnico, visite o nosso site principal:</p>
                <a href="https://joaocesarsilva.com" target="_blank" rel="noopener noreferrer" class="main-site-link">
                    🌐 joaocesarsilva.com
                </a>
                <div class="footer-note">
                    <small>© 2024 JCS Studio - Todos os direitos reservados</small>
                </div>
            </div>
        </footer>
    </div>
    
    <script>
        // Function to create SEO-friendly URLs
        function createSEOUrl(title) {
            return title
                .toLowerCase()
                .replace(/[áàâãä]/g, 'a')
                .replace(/[éèêë]/g, 'e')
                .replace(/[íìîï]/g, 'i')
                .replace(/[óòôõö]/g, 'o')
                .replace(/[úùûü]/g, 'u')
                .replace(/[ç]/g, 'c')
                .replace(/[ñ]/g, 'n')
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '');
        }
        
        // Function to truncate description
        function truncateDescription(description, maxLength = 150) {
            if (description.length <= maxLength) return description;
            return description.substring(0, maxLength).replace(/\s+\S*$/, '') + '...';
        }
        
        // Function to extract product slug from GUID
        function extractProductSlug(guid) {
            const match = guid.match(/product=([^&]+)/);
            return match ? match[1] : '';
        }
        
        // Function to load and parse XML
        async function loadProducts() {
            try {
                const response = await fetch('https://joaocesarsilva.com/feed_validated.xml', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/xml'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const xmlText = await response.text();
                
                if (!xmlText || xmlText.trim() === '') {
                    throw new Error('Empty XML response');
                }
                
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
                
                // Check for XML parsing errors
                const parseError = xmlDoc.querySelector('parsererror');
                if (parseError) {
                    throw new Error('XML parsing error: ' + parseError.textContent);
                }
                
                const items = xmlDoc.querySelectorAll('item');
                const products = [];
                
                if (items.length === 0) {
                    throw new Error('No products found in XML feed');
                }
                
                items.forEach(item => {
                    try {
                        const title = item.querySelector('title')?.textContent?.trim() || '';
                        const description = item.querySelector('description')?.textContent?.trim() || '';
                        const guid = item.querySelector('guid')?.textContent?.trim() || '';
                        const category = item.querySelector('category')?.textContent?.trim() || 'Sem categoria';
                        const enclosure = item.querySelector('enclosure');
                        const imageUrl = enclosure?.getAttribute('url')?.trim() || '';
                        const pubDate = item.querySelector('pubDate')?.textContent?.trim() || '';
                        
                        if (title && guid && !guid.includes('?page=')) {
                            products.push({
                                title: title,
                                description: description,
                                guid: guid,
                                category: category,
                                imageUrl: imageUrl,
                                pubDate: pubDate,
                                slug: extractProductSlug(guid),
                                seoUrl: createSEOUrl(title)
                            });
                        }
                    } catch (itemError) {
                        console.warn('Error processing item:', itemError);
                    }
                });
                
                if (products.length === 0) {
                    throw new Error('No valid products could be processed');
                }
                
                displayProducts(products);
                
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('loading').style.display = 'none';
                const errorElement = document.getElementById('error');
                errorElement.style.display = 'block';
                errorElement.textContent = `Erro ao carregar produtos: ${error.message}`;
            }
        }
        
        // Global variables for pagination
        let allProducts = [];
        let currentPage = 1;
        const itemsPerPage = 24;
        
        // Function to display products with pagination
        function displayProducts(products) {
            allProducts = products;
            const loading = document.getElementById('loading');
            const productCount = document.getElementById('product-count');
            
            loading.style.display = 'none';
            productCount.innerHTML = `<strong>${products.length}</strong> produtos disponíveis`;
            
            displayPage(1);
        }
        
        // Function to display a specific page
        function displayPage(page) {
            const container = document.getElementById('products-container');
            const pagination = document.getElementById('pagination');
            const pageInfo = document.getElementById('page-info');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            // Clear container
            container.innerHTML = '';
            
            // Calculate pagination
            const totalPages = Math.ceil(allProducts.length / itemsPerPage);
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProducts = allProducts.slice(startIndex, endIndex);
            
            // Display products for current page
            pageProducts.forEach((product, index) => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.id = `product-${product.seoUrl}`;
                
                // Create structured data for SEO
                const structuredData = {
                    "@context": "https://schema.org/",
                    "@type": "Product",
                    "name": product.title,
                    "description": product.description,
                    "image": product.imageUrl,
                    "url": product.guid,
                    "category": product.category
                };
                
                // Create elements safely to avoid XSS
                const img = document.createElement('img');
                img.src = product.imageUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbSBuw6NvIGRpc3BvbsOtdmVsPC90ZXh0Pjwvc3ZnPg==';
                img.alt = product.title;
                img.className = 'product-image';
                img.loading = 'lazy';
                img.onerror = function() {
                    this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbSBuw6NvIGRpc3BvbsOtdmVsPC90ZXh0Pjwvc3ZnPg==';
                };
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'product-content';
                
                const title = document.createElement('h3');
                title.className = 'product-title';
                title.textContent = product.title;
                
                const category = document.createElement('span');
                category.className = 'product-category';
                category.textContent = product.category;
                
                const description = document.createElement('p');
                description.className = 'product-description';
                description.textContent = truncateDescription(product.description);
                
                const productDate = document.createElement('div');
                productDate.className = 'product-date';
                productDate.textContent = product.pubDate ? `Publicado: ${new Date(product.pubDate).toLocaleDateString('pt-PT')}` : 'Data não disponível';
                
                const link = document.createElement('a');
                link.href = product.guid;
                link.className = 'product-link';
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
                link.textContent = 'Ver Produto na JCS Studio →';
                
                // Add structured data script
                const structuredScript = document.createElement('script');
                structuredScript.type = 'application/ld+json';
                structuredScript.textContent = JSON.stringify(structuredData);
                
                contentDiv.appendChild(title);
                contentDiv.appendChild(category);
                contentDiv.appendChild(description);
                contentDiv.appendChild(productDate);
                contentDiv.appendChild(link);
                
                productCard.appendChild(img);
                productCard.appendChild(contentDiv);
                productCard.appendChild(structuredScript);
                
                container.appendChild(productCard);
            });
            
            // Update pagination controls
            if (totalPages > 1) {
                pagination.style.display = 'flex';
                pageInfo.textContent = `Página ${page} de ${totalPages}`;
                prevBtn.disabled = page === 1;
                nextBtn.disabled = page === totalPages;
                
                // Update navigation map
                updateNavigationMap(page, totalPages);
            } else {
                pagination.style.display = 'none';
                document.getElementById('navigation-map').style.display = 'none';
            }
            
            currentPage = page;
            
            // Update page title with product count
            document.title = `JCS Studio - ${allProducts.length} Produtos na Galeria`;
        }
        
        // Function to update navigation map
        function updateNavigationMap(currentPage, totalPages) {
            const navigationMap = document.getElementById('navigation-map');
            const pageLinks = document.getElementById('page-links');
            
            if (totalPages <= 1) {
                navigationMap.style.display = 'none';
                return;
            }
            
            navigationMap.style.display = 'block';
            pageLinks.innerHTML = '';
            
            // Show all pages if 10 or fewer, otherwise show smart pagination
            if (totalPages <= 10) {
                for (let i = 1; i <= totalPages; i++) {
                    const pageLink = document.createElement('button');
                    pageLink.className = 'page-link';
                    pageLink.textContent = i;
                    pageLink.onclick = () => displayPage(i);
                    
                    if (i === currentPage) {
                        pageLink.classList.add('active');
                    }
                    
                    pageLinks.appendChild(pageLink);
                }
            } else {
                // Smart pagination for many pages
                const addPageLink = (pageNum, text = pageNum) => {
                    const pageLink = document.createElement('button');
                    pageLink.className = 'page-link';
                    pageLink.textContent = text;
                    pageLink.onclick = () => displayPage(pageNum);
                    
                    if (pageNum === currentPage) {
                        pageLink.classList.add('active');
                    }
                    
                    pageLinks.appendChild(pageLink);
                };
                
                // Always show first page
                addPageLink(1);
                
                // Show ellipsis if needed
                if (currentPage > 4) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'page-ellipsis';
                    pageLinks.appendChild(ellipsis);
                }
                
                // Show pages around current page
                const start = Math.max(2, currentPage - 2);
                const end = Math.min(totalPages - 1, currentPage + 2);
                
                for (let i = start; i <= end; i++) {
                    addPageLink(i);
                }
                
                // Show ellipsis if needed
                if (currentPage < totalPages - 3) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'page-ellipsis';
                    pageLinks.appendChild(ellipsis);
                }
                
                // Always show last page
                if (totalPages > 1) {
                    addPageLink(totalPages);
                }
            }
        }
        
        // Function to change page
        function changePage(direction) {
            const totalPages = Math.ceil(allProducts.length / itemsPerPage);
            const newPage = currentPage + direction;
            
            if (newPage >= 1 && newPage <= totalPages) {
                displayPage(newPage);
                // Scroll to top of products for better navigation
                const productsContainer = document.getElementById('products-container');
                if (productsContainer) {
                    productsContainer.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                }
            }
        }
        
        // Function to handle navigation hash support
        function handleHashNavigation() {
            if (window.location.hash) {
                const targetId = window.location.hash.substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    setTimeout(() => {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                        targetElement.style.border = '3px solid #667eea';
                        setTimeout(() => {
                            targetElement.style.border = '1px solid #e9ecef';
                        }, 3000);
                    }, 500);
                }
            }
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            handleHashNavigation();
        });
        
        // Add smooth scrolling for anchor links
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const targetId = e.target.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    </script>
</body>
</html>
/* Product Images Management Styles */

.draggable-image {
    cursor: move;
    transition: transform 0.2s, box-shadow 0.2s;
}

.draggable-image.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    z-index: 1000;
}

.draggable-image.drag-over {
    border: 2px dashed #007bff;
    transform: scale(1.05);
}

.drag-handle {
    color: #6c757d;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.draggable-image:hover .drag-handle {
    opacity: 1;
}

/* Ensure the image container has position relative for absolute positioning of elements */
.existing-image-item .card {
    position: relative;
}

/* Add a subtle shadow effect when hovering over images */
.existing-image-item .card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.15) !important;
}

/* Style for the default image indicator */
.default-image-indicator {
    position: absolute;
    top: 5px;
    left: 5px;
    background-color: rgba(40, 167, 69, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    z-index: 10;
}

<?php

if (!$is_admin_logged_in) {
    header('Location: admin.php?section=login&' . get_session_id_param());
    exit;
}

$digital_products = $digital_products ?? [];

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Produtos Digitais</h1>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Lista de Produtos Digitais</h5>
        </div>
        <div class="card-body">
            <?php if (empty($digital_products)): ?>
                <div class="alert alert-info">
                    Nenhum produto digital encontrado. Adicione produtos digitais através da página de produtos.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nome</th>
                                <th>Arquivo</th>
                                <th>Expira em</th>
                                <th>Nº Downloads</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($digital_products as $product): ?>
                                <tr>
                                    <td><?= $product['digital_product_id'] ?></td>
                                    <td><?= sanitize_input($product['name_pt']) ?></td>
                                    <td>
                                        <?php if (!empty($product['df_display_name'])): ?>
                                            <?= sanitize_input($product['df_display_name']) ?>
                                        <?php elseif (!empty($product['df_original_filename'])): ?>
                                            <?= sanitize_input($product['df_original_filename']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">Sem arquivo</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $product['expiry_days'] ?> dias</td>
                                    <td><?= $product['download_limit'] ?></td>
                                    <td>
                                        <a href="admin.php?section=products&action=edit&id=<?= $product['product_id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i> Editar Prod.
                                        </a>
                                        <a href="admin.php?section=digital_products&action=edit&id=<?= $product['product_id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-info">
                                            <i class="bi bi-gear"></i> Confs Dig.
                                        </a>
                                        <a href="admin.php?section=digital_files&action=change_file&id=<?= $product['digital_product_id'] ?>&item_id=<?= $product['digital_product_id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-success">
                                            <i class="bi bi-file-earmark-arrow-up"></i> Alterar Arq.
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php

require_once 'config.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

start_session();

$is_mobile = is_mobile_device();

$orientation = $_SESSION['device_orientation'] ?? 'Not detected';
$screen_width = $_SESSION['screen_width'] ?? 'Not detected';
$screen_height = $_SESSION['screen_height'] ?? 'Not detected';
$device_pixel_ratio = $_SESSION['device_pixel_ratio'] ?? 'Not detected';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Not available';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Device Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .mobile {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .not-mobile {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .info-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .refresh-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background-color: #0056b3;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mobile Device Detection Test</h1>
        
        <div class="result <?= $is_mobile ? 'mobile' : 'not-mobile' ?>">
            Detection Result: <?= $is_mobile ? 'MOBILE DEVICE (Landscape + Small Resolution)' : 'NOT MOBILE DEVICE' ?>
        </div>
        
        <div class="instructions">
            <h3>How to Test:</h3>
            <ol>
                <li>Open this page on a mobile device</li>
                <li>Rotate the device to landscape orientation</li>
                <li>Refresh the page to see updated results</li>
                <li>The function should return TRUE only for mobile devices in landscape with small resolution</li>
            </ol>
        </div>
        
        <h2>Device Information</h2>
        <table class="info-table">
            <tr>
                <th>Property</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>User Agent</td>
                <td style="word-break: break-all;"><?= htmlspecialchars($user_agent) ?></td>
                <td>Browser/device identification string</td>
            </tr>
            <tr>
                <td>Orientation</td>
                <td><?= htmlspecialchars($orientation) ?></td>
                <td>Device orientation (portrait/landscape)</td>
            </tr>
            <tr>
                <td>Screen Width</td>
                <td><?= htmlspecialchars($screen_width) ?></td>
                <td>Screen width in pixels</td>
            </tr>
            <tr>
                <td>Screen Height</td>
                <td><?= htmlspecialchars($screen_height) ?></td>
                <td>Screen height in pixels</td>
            </tr>
            <tr>
                <td>Device Pixel Ratio</td>
                <td><?= htmlspecialchars($device_pixel_ratio) ?></td>
                <td>Device pixel density ratio</td>
            </tr>
        </table>
        
        <h2>Detection Logic</h2>
        <p>The function returns TRUE only when ALL of these conditions are met:</p>
        <ul>
            <li><strong>Mobile User Agent:</strong> User agent matches mobile device patterns</li>
            <li><strong>Landscape Orientation:</strong> Device is in landscape mode (width > height)</li>
            <li><strong>Small Resolution:</strong> Screen width ≤ 768px OR height ≤ 480px</li>
        </ul>
        
        <button class="refresh-btn" onclick="window.location.reload()">Refresh Page</button>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
            <strong>Note:</strong> If device information shows "Not detected", the JavaScript detection script may not have run yet. 
            Wait a moment and refresh the page.
        </div>
    </div>
    
    <!-- Include the device detection script -->
    <script src="<?= get_asset_url('js/device-detection.js') ?>"></script>
    
    <!-- Auto-refresh after device info is detected -->
    <script>
        // Auto-refresh the page once device info is available
        setTimeout(function() {
            if (<?= json_encode($orientation === 'Not detected') ?>) {
                window.location.reload();
            }
        }, 2000);
    </script>
</body>
</html>
<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/captcha_functions.php';
require_once __DIR__ . '/../../includes/license_encryption_functions.php';
require_once __DIR__ . '/../../includes/maintenance_functions.php';
require_once __DIR__ . '/../../includes/order_functions.php'; 
require_once __DIR__ . '/../../includes/digital_order_functions.php'; 

cleanup_expired_license_verification_tokens();
cleanup_expired_download_tokens();

$license_code = '';
$error_message = '';
$success_message = '';
$license = null;
$verification_step = 'license_check'; 
$verification_email = '';
$verification_code = '';
$user_verified = false;
$captcha = null;
$email_token = '';

if (isset($_GET['email_token'])) {
    $email_token = sanitize_input($_GET['email_token']);
    
    
    $token_verification = verify_email_download_token($email_token);
    
    if ($token_verification['valid']) {
        
        $license_code = $token_verification['license_code'];
        
        
        $validity = check_license_validity($license_code, false);
        
        if ($validity['valid']) {
            $license = $validity['license'];
            $user_verified = true;
            $verification_step = 'verified';
            $_SESSION['license_verified'][$license_code] = true;
            
            
            $download_result = process_download_request($license_code, null, $email_token);
            
            if ($download_result['success']) {
                
                $_SESSION['download_file'] = [
                    'path' => $download_result['file_path'],
                    'name' => $download_result['file_name'],
                    'expires' => time() + 300 
                ];
                
                
                $ds_token = bin2hex(random_bytes(16));
                $_SESSION['ds_token'] = $ds_token;
                $_SESSION['ds_token_expires'] = time() + 300;
                
                
                $download_redirect_url = BASE_URL . '/index.php?view=download_file&ds_token=' . $ds_token;
                header('Location: ' . $download_redirect_url);
                exit;
            } else {
                $error_message = $download_result['message'];
            }
        } else {
            $error_message = $validity['message'];
        }
    } else {
        $error_message = $token_verification['message'];
    }
}

if (isset($_GET['code'])) {
    $license_code = sanitize_input($_GET['code']);
} elseif (isset($_POST['license_code'])) {
    
    $license_code = sanitize_input($_POST['license_code']);
}

if (!empty($license_code) && isset($_SESSION['license_verified'][$license_code]) && $_SESSION['license_verified'][$license_code] === true) {
    $user_verified = true;
    $verification_step = 'verified';
    if (!$license) { 
        $license = get_license_by_code($license_code);
    }
    
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['license_code']) && !isset($_POST['verification_email']) && !isset($_POST['verification_code']) && !$user_verified) {
    $license_code = sanitize_input($_POST['license_code']); 

    if (empty($license_code)) {
        $error_message = 'Por favor, insira um código de licença.';
    } else {
        
        $license = get_license_by_code($license_code);

        if ($license) {
            $verification_step = 'email_verification';
            $success_message = 'Licença encontrada. Para verificar os detalhes, por favor forneça o email associado a esta licença.';
        } else {
            $error_message = 'Código de licença não encontrado.';
        }
    }
}

elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['verification_email']) && isset($_POST['captcha_answer'])) {
    $license_code = sanitize_input($_POST['license_code']);
    $verification_email = sanitize_input($_POST['verification_email']);
    $captcha_answer = (int)sanitize_input($_POST['captcha_answer']);

    if (empty($license_code) || empty($verification_email)) {
        $error_message = 'Por favor, preencha todos os campos obrigatórios.';
    } elseif (!verify_captcha($captcha_answer)) {
        $error_message = 'O código de verificação está incorreto. Por favor, tente novamente.';
        $license = get_license_by_code($license_code);
        $verification_step = 'email_verification';
    } else {
        $license = get_license_by_code($license_code);

        if ($license) {
            
            $email_verification = verify_license_email($license['id'], $verification_email);

            if ($email_verification['valid']) {
                
                $token_data = create_license_verification_token($license['id']);

                if ($token_data) {
                    
                    $decrypted_email = get_decrypted_license_email($license);
                    $decrypted_name = get_decrypted_license_name($license);

                    $subject = 'Código de Verificação de Licença';

                    
                    $email_template = get_setting('license_verification_email_template',
'Olá {customer_name},

Seu código de verificação para acessar os detalhes da licença {license_code} é:

**{verification_code}**

Este código expira em 15 minutos.

Se você não solicitou este código, ignore este email.');

                    
                    $message = str_replace([
                        '{customer_name}',
                        '{license_code}',
                        '{verification_code}'
                    ], [
                        $decrypted_name,
                        $license['license_code'],
                        $token_data['verification_code']
                    ], $email_template);

                    require_once __DIR__ . '/../../includes/email_functions.php';
                    send_email($decrypted_email, $decrypted_name, $subject, $message);

                    $verification_step = 'code_verification';
                    $success_message = 'Um código de verificação foi enviado para o seu email. Por favor, verifique sua caixa de entrada.';
                } else {
                    $error_message = 'Erro ao gerar código de verificação. Por favor, tente novamente.';
                    $verification_step = 'email_verification';
                }
            } else {
                $error_message = 'O email fornecido não corresponde ao email associado a esta licença.';
                $verification_step = 'email_verification';
            }
        } else {
            $error_message = 'Licença não encontrada.';
        }
    }
}

elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['verification_code'])) {
    $license_code = sanitize_input($_POST['license_code']);
    $verification_code = sanitize_input($_POST['verification_code']);

    if (empty($license_code) || empty($verification_code)) {
        $error_message = 'Por favor, preencha todos os campos obrigatórios.';
    } else {
        $license = get_license_by_code($license_code);

        if ($license) {
            $token_verification = verify_license_verification_token($license['id'], $verification_code);

            if ($token_verification['valid']) {
                $verification_step = 'verified';
                $user_verified = true;
                $_SESSION['license_verified'][$license_code] = true; 
                $success_message = 'Verificação concluída com sucesso! Agora você pode ver os detalhes completos da licença.';
            } else {
                $error_message = $token_verification['message'];
                $verification_step = 'code_verification';
            }
        } else {
            $error_message = 'Licença não encontrada.';
        }
    }
}

elseif (isset($_GET['code']) && !$user_verified) {
    
    if (!empty($license_code)) {
        $license = get_license_by_code($license_code);

        if ($license) {
            $verification_step = 'email_verification';
            
            
        } else {
            $error_message = 'Código de licença não encontrado.';
        }
    }
}

elseif (isset($_GET['send_order_details']) && $_GET['send_order_details'] === '1' && !empty($license_code) && $user_verified) {
    
    if (!$license) $license = get_license_by_code($license_code); 

    if ($license) {
        
        if (!empty($license['order_id'])) {
            $order = get_order_by_id($license['order_id']); 

            if ($order) {
                
                
                $email_result = send_license_order_email($license);

                if ($email_result) {
                    $success_message = 'Os detalhes da encomenda foram enviados para o seu email.';
                } else {
                    $error_message = 'Erro ao enviar email com os detalhes da encomenda. Por favor, tente novamente.';
                }
            } else {
                $error_message = 'Encomenda associada à licença não encontrada.';
            }
        } else {
            $error_message = 'Não existe encomenda associada diretamente a esta licença para reenviar detalhes.';
        }
    } else {
        $error_message = 'Licença não encontrada.';
    }
}

elseif (isset($_GET['download']) && $_GET['download'] === '1' && !empty($license_code) && $user_verified) {
    
    if (!$license) $license = get_license_by_code($license_code); 

    $validity = check_license_validity($license_code, false); 

    if ($validity['valid'] && $license) {
        
        
        $new_download_token_data = create_download_token($license['id']);

        if ($new_download_token_data) {
            
            db_query("UPDATE download_tokens SET is_verified = 1 WHERE id = :id", [':id' => $new_download_token_data['id']]);

            
            $download_result = process_download_request($license_code, $new_download_token_data['security_token']);

            if ($download_result['success']) {
                $_SESSION['download_file'] = [
                    'path' => $download_result['file_path'],
                    'name' => $download_result['file_name'],
                    'expires' => time() + 300 
                ];
                
                session_write_close();

                
                
                $ds_token_for_url = bin2hex(random_bytes(8));
                $_SESSION['download_page_access_token'] = $ds_token_for_url; 

                $download_redirect_url = BASE_URL . '/index.php?view=download_file&ds_token=' . $ds_token_for_url;
                $download_redirect_url = add_session_param_to_url($download_redirect_url);

                header('Location: ' . $download_redirect_url);
                exit;
            } else {
                $error_message = $download_result['message'];
            }
        } else {
            $error_message = "Erro ao criar token para o download.";
        }
    } else {
        
        $error_message = $validity['message'] ?? 'Não foi possível validar a sua licença para download.';
    }
}

$digital_products = [];
$file_types = [];

if ($license) {
    $digital_products = get_license_digital_products($license['id']);

    foreach ($digital_products as $product) {
        $product_file_types = get_digital_product_file_types($product['id']);
        $file_types = array_merge($file_types, $product_file_types);
    }

    $unique_file_types = [];
    foreach ($file_types as $type) {
        $unique_file_types[$type['id']] = $type;
    }
    $file_types = array_values($unique_file_types);
}
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold">Verificação de Licença e Download de Produtos Digitais</h1>
    </div>

    <?php if (!empty($error_message)): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline"><?= sanitize_input($error_message) ?></span>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Sucesso!</strong>
            <span class="block sm:inline"><?= sanitize_input($success_message) ?></span>
        </div>
    <?php endif; ?>

    <!-- Step 1: License Code Input -->
    <?php if ($verification_step === 'license_check'): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Verificar Licença</h2>
            <p class="mb-4">Para verificar o status de uma licença, insira o código da mesma no campo abaixo.</p>

            <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                <?php if (function_exists('csrf_token_field')): ?>
                    <?= csrf_token_field() ?>
                <?php endif; ?>

                <div class="mb-4">
                    <label for="license_code" class="block text-sm font-medium text-gray-300 mb-1">Código de Licença</label>
                    <input type="text" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                           id="license_code" name="license_code" value="<?= sanitize_input($license_code) ?>" required
                           placeholder="Ex: XXXX-XXXX-JCS-XXXX-XXXX">
                </div>

                <div class="mt-6">
                    <button type="submit" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded font-medium">
                        <i class="ri-shield-check-line mr-2"></i> Verificar Licença
                    </button>
                </div>
            </form>
        </div>
    <?php endif; ?>

    <!-- Step 2: Email Verification -->
    <?php if ($verification_step === 'email_verification' && $license): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Detalhes da Licença</h2>

            <?php
            $decrypted_name = get_decrypted_license_name($license);
            $decrypted_email = get_decrypted_license_email($license);
            $censored_name = get_license_censored_name($decrypted_name, $license['status']);
            $censored_email = get_license_censored_email($decrypted_email, $license['status'], false);
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm mb-1">Código de Licença:</p>
                    <p class="font-medium"><?= sanitize_input($license['license_code']) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Status da Licença:</p>
                    <p class="font-medium">
                        <?php
                        $status_class = 'bg-green-900 text-green-100';
                        $status_text = 'Ativo';
                        if ($license['status'] === 'waiting_payment') {
                            $status_class = 'bg-yellow-900 text-yellow-100';
                            $status_text = 'Aguardando Pagamento';
                        } elseif ($license['status'] === 'disabled' || $license['status'] === 'canceled') {
                            $status_class = 'bg-red-900 text-red-100';
                            $status_text = ($license['status'] === 'disabled') ? 'Desativada' : 'Cancelada';
                        }
                        ?>
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $status_class ?>"><?= $status_text ?></span>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                    <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Email:</p>
                    <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                </div>
            </div>

            <div class="mt-6 bg-blue-900/30 border border-blue-800 rounded-lg p-6">
                <h3 class="text-lg font-medium mb-4 text-blue-300">Verificação de Email</h3>
                <p class="mb-4 text-blue-200">
                    Para ver os detalhes completos da licença e poder fazer download, por favor insira o email associado a esta licença.
                </p>

                <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                    <?php if (function_exists('csrf_token_field')): ?>
                        <?= csrf_token_field() ?>
                    <?php endif; ?>

                    <input type="hidden" name="license_code" value="<?= sanitize_input($license_code) ?>">

                    <div class="mb-4">
                        <label for="verification_email" class="block text-sm font-medium text-blue-300 mb-1">Email</label>
                        <input type="email" class="w-full px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                               id="verification_email" name="verification_email" value="<?= sanitize_input($verification_email) ?>" required
                               placeholder="Digite o email associado à licença">
                    </div>

                    <div class="mb-4">
                        <label for="captcha_answer" class="block text-sm font-medium text-blue-300 mb-1">Verificação de Segurança</label>
                        <div class="flex items-center gap-3">
                            <?php
                            if ($captcha === null) {
                                $captcha = generate_simple_captcha();
                            }
                            ?>
                            <div class="bg-gray-700 px-4 py-2 rounded text-white font-medium">
                                <?= $captcha['question'] ?>
                            </div>
                            <input type="number" class="w-20 px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                                   id="captcha_answer" name="captcha_answer" required>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="inline-block bg-blue-700 hover:bg-blue-600 text-white py-3 px-6 rounded font-medium">
                            <i class="ri-mail-check-line mr-2"></i> Verificar Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- Step 3: Verification Code Input -->
    <?php if ($verification_step === 'code_verification' && $license): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Detalhes da Licença</h2>

            <?php
            $decrypted_name = get_decrypted_license_name($license);
            $decrypted_email = get_decrypted_license_email($license);
            $censored_name = get_license_censored_name($decrypted_name, $license['status']);
            $censored_email = get_license_censored_email($decrypted_email, $license['status'], false);
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm mb-1">Código de Licença:</p>
                    <p class="font-medium"><?= sanitize_input($license['license_code']) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Status da Licença:</p>
                    <p class="font-medium">
                        <?php
                        $status_class = 'bg-green-900 text-green-100';
                        $status_text = 'Ativo';
                        if ($license['status'] === 'waiting_payment') {
                            $status_class = 'bg-yellow-900 text-yellow-100';
                            $status_text = 'Aguardando Pagamento';
                        } elseif ($license['status'] === 'disabled' || $license['status'] === 'canceled') {
                            $status_class = 'bg-red-900 text-red-100';
                            $status_text = ($license['status'] === 'disabled') ? 'Desativada' : 'Cancelada';
                        }
                        ?>
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $status_class ?>"><?= $status_text ?></span>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                    <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Email:</p>
                    <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                </div>
            </div>

            <div class="mt-6 bg-blue-900/30 border border-blue-800 rounded-lg p-6">
                <h3 class="text-lg font-medium mb-4 text-blue-300">Código de Verificação</h3>
                <p class="mb-4 text-blue-200">
                    Um código de verificação foi enviado para o seu email. Por favor, insira o código abaixo para continuar.
                </p>

                <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                    <?php if (function_exists('csrf_token_field')): ?>
                        <?= csrf_token_field() ?>
                    <?php endif; ?>

                    <input type="hidden" name="license_code" value="<?= sanitize_input($license_code) ?>">

                    <div class="mb-4">
                        <label for="verification_code" class="block text-sm font-medium text-blue-300 mb-1">Código de Verificação</label>
                        <input type="text" class="w-full px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                               id="verification_code" name="verification_code" value="<?= sanitize_input($verification_code) ?>" required
                               placeholder="Digite o código de 6 dígitos" maxlength="6" minlength="6">
                        <p class="text-sm text-blue-300 mt-1">
                            O código expira em 15 minutos. Se não recebeu o código, verifique sua pasta de spam.
                        </p>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="inline-block bg-blue-700 hover:bg-blue-600 text-white py-3 px-6 rounded font-medium">
                            <i class="ri-shield-check-line mr-2"></i> Verificar Código
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- Step 4: Verified - Full License Details and Download -->
    <?php if ($verification_step === 'verified' && $license && $user_verified): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Detalhes da Licença</h2>

            <?php
            $decrypted_name = get_decrypted_license_name($license);
            $decrypted_email = get_decrypted_license_email($license);
            $censored_name = get_license_censored_name($decrypted_name, $license['status']);
            $censored_email = get_license_censored_email($decrypted_email, $license['status'], true); 

            
            $download_status_class = 'bg-green-900 text-green-100';
            $download_status_text = 'Disponível';
            $is_download_expired = false;
            $is_limit_reached = false;
            $can_download = false;

            if (strtotime($license['expiry_date']) < time()) {
                $download_status_class = 'bg-red-900 text-red-100';
                $download_status_text = 'Expirado';
                $is_download_expired = true;
            } elseif ($license['downloads_used'] >= $license['download_limit']) {
                $download_status_class = 'bg-orange-900 text-orange-100';
                $download_status_text = 'Limite Atingido';
                $is_limit_reached = true;
            } else {
                $can_download = ($license['status'] === 'active');
            }
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm mb-1">Código de Licença:</p>
                    <p class="font-medium"><?= sanitize_input($license['license_code']) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Status da Licença:</p>
                    <p class="font-medium">
                        <?php
                        $status_class = 'bg-green-900 text-green-100';
                        $status_text = 'Ativo';
                        if ($license['status'] === 'waiting_payment') {
                            $status_class = 'bg-yellow-900 text-yellow-100';
                            $status_text = 'Aguardando Pagamento';
                        } elseif ($license['status'] === 'disabled' || $license['status'] === 'canceled') {
                            $status_class = 'bg-red-900 text-red-100';
                            $status_text = ($license['status'] === 'disabled') ? 'Desativada' : 'Cancelada';
                        }
                        ?>
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $status_class ?>"><?= $status_text ?></span>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                    <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Email:</p>
                    <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Validade do Download:</p>
                    <p class="font-medium">
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $download_status_class ?>"><?= $download_status_text ?></span>
                        <?php if ($is_download_expired): ?>
                            <span class="text-sm text-gray-400 ml-2">
                                (Expirou em <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>)
                            </span>
                        <?php elseif (!$is_download_expired && $license['status'] === 'active'): ?>
                            <span class="text-sm text-gray-400 ml-2">
                                (Até <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>)
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Downloads:</p>
                    <p class="font-medium"><?= $license['downloads_used'] ?> de <?= $license['download_limit'] ?> utilizados</p>
                </div>
            </div>

            <?php if (!empty($file_types)): ?>
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-2">Extensões incluídas neste download:</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($file_types as $type): ?>
                            <span class="inline-block px-3 py-1 text-sm rounded bg-gray-800 text-gray-300">
                                <?= sanitize_input($type['extension']) ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Download Button and Order Details -->
            <div class="mt-6">
                <div class="flex flex-wrap gap-3">
                    <?php if ($can_download): ?>
                        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license_code) . '&download=1') ?>"
                           class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded font-medium">
                            <i class="ri-download-line mr-2"></i> Baixar Arquivos
                        </a>
                    <?php else: ?>
                        <button class="inline-block bg-gray-700 text-gray-300 py-3 px-6 rounded font-medium cursor-not-allowed" disabled>
                            <i class="ri-download-line mr-2"></i> Baixar Arquivos
                        </button>
                    <?php endif; ?>

                    <!-- Order Details Email Button -->
                    <?php
                    $can_send_order_details = false;
                    if ($license && !empty($license['order_id'])) {
                        
                        $order_for_details = get_order_by_id($license['order_id']);

                        if ($order_for_details) {
                            $order_customer_info_json = $order_for_details['customer_info_json'] ?? '{}';
                            $order_customer_info = json_decode($order_customer_info_json, true);
                            $order_email = $order_customer_info['customer_email'] ?? null;
                            $order_status = strtolower($order_for_details['status'] ?? '');

                            
                            if (!empty($order_email) &&
                                filter_var($order_email, FILTER_VALIDATE_EMAIL) &&
                                strpos($order_email, 'anonymized') === false && 
                                strpos($order_email, '@example.com') === false && 
                                strpos($order_email, '*****') === false && 
                                !in_array($order_status, ['deleted', 'anonymized', 'apagada', 'anónima', 'cancelada']) 
                            ) {
                                $can_send_order_details = true;
                            }
                        }
                    }

                    if ($can_send_order_details):
                    ?>
                        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license_code) . '&send_order_details=1') ?>"
                           class="inline-block bg-blue-700 hover:bg-blue-600 text-white py-3 px-6 rounded font-medium">
                            <i class="ri-mail-line mr-2"></i> Enviar Detalhes da Encomenda
                        </a>
                    <?php else: ?>
                        <button class="inline-block bg-gray-700 text-gray-400 py-3 px-6 rounded font-medium cursor-not-allowed" disabled title="Os detalhes desta encomenda não podem ser reenviados neste momento.">
                            <i class="ri-mail-line mr-2"></i> Enviar Detalhes da Encomenda
                        </button>
                    <?php endif; ?>
                </div>

                <?php if (!$can_download): ?>
                    <p class="text-sm text-gray-400 mt-2">
                        <?php if ($license['status'] === 'waiting_payment'): ?>
                            Aguardando confirmação de pagamento para autorizar o download.
                        <?php elseif ($license['status'] === 'disabled' || $license['status'] === 'canceled'): ?>
                            Esta licença foi <?= ($license['status'] === 'disabled') ? 'desativada' : 'cancelada' ?>.
                            Entre em contato com o suporte para mais informações.
                        <?php elseif ($is_download_expired): ?>
                            O período de download expirou em <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>.
                            A licença continua <?= strtolower($status_text) ?>, mas não é mais possível fazer o download.
                        <?php elseif ($is_limit_reached): ?>
                            Você atingiu o limite máximo de downloads para este produto.
                        <?php endif; ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>

        <!-- License Terms -->
        <div class="bg-gray-900 rounded-lg p-6">
            <h2 class="text-xl font-medium mb-4">Termos de Licença</h2>
            <div class="prose prose-invert max-w-none">
                <?php
                $digital_files_placeholder = get_page_placeholder_by_slug('ficheiros-digitais');

                if ($digital_files_placeholder):
                    $placeholder_pages = get_pages_by_placeholder_id($digital_files_placeholder['id']);

                    if (!empty($placeholder_pages)):
                ?>
                    <h3 class="text-lg font-medium text-gray-200 mb-3"><?= htmlspecialchars($digital_files_placeholder['name']) ?></h3>
                    <ul class="space-y-2">
                        <?php foreach ($placeholder_pages as $page): ?>
                            <li>
                                <a href="<?= get_page_url($page['slug']) ?>" class="text-gray-400 hover:text-white flex items-center" target="_blank" rel="noopener noreferrer">
                                    <i class="ri-file-text-line mr-2"></i>
                                    <?= htmlspecialchars($page['title']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php
                    else:
                        echo nl2br(sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')));
                    endif;
                else:
                    echo nl2br(sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')));
                endif;
                ?>
            </div>
        </div>
    <?php endif; ?>
</div>
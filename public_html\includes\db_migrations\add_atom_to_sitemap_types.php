<?php

function migrate_add_atom_to_sitemap_types(PDO $pdo): bool
{
    try {
        
        if (!$pdo->inTransaction()) {
            $pdo->beginTransaction();
        }

        
        $pdo->exec("CREATE TABLE sitemap_configs_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL CHECK(type IN ('sitemap', 'google_merchant', 'custom', 'atom')),
            output_path TEXT NOT NULL,
            include_products INTEGER NOT NULL DEFAULT 1,
            include_blog INTEGER NOT NULL DEFAULT 1,
            include_pages INTEGER NOT NULL DEFAULT 1,
            custom_config_json TEXT,
            is_active INTEGER NOT NULL DEFAULT 1,
            include_regular_products INTEGER NOT NULL DEFAULT 1,
            include_variation_products INTEGER NOT NULL DEFAULT 1,
            include_digital_products INTEGER NOT NULL DEFAULT 1,
            last_generated TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        )");

        
        $pdo->exec("INSERT INTO sitemap_configs_new (
            id, name, type, output_path, include_products, include_blog, include_pages,
            custom_config_json, is_active, include_regular_products, include_variation_products,
            include_digital_products, last_generated, created_at, updated_at
        ) SELECT
            id, name, type, output_path, include_products, include_blog, include_pages,
            custom_config_json, is_active, include_regular_products, include_variation_products,
            include_digital_products, last_generated, created_at, updated_at
        FROM sitemap_configs");

        
        $pdo->exec("DROP TABLE sitemap_configs");

        
        $pdo->exec("ALTER TABLE sitemap_configs_new RENAME TO sitemap_configs");

        
        if ($pdo->inTransaction()) {
            $pdo->commit();
        }
        return true;
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}
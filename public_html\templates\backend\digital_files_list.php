<?php

if (!$is_admin_logged_in) {
    header('Location: admin.php?section=login&' . get_session_id_param());
    exit;
}

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../config.php'; 

$name_filter = isset($_GET['name']) ? trim($_GET['name']) : '';
$file_type_filter = isset($_GET['file_type']) ? trim($_GET['file_type']) : '';

$files_per_page = 10;
$current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;

$filters = [];
if (!empty($name_filter)) {
    $filters['name'] = $name_filter;
}
if (!empty($file_type_filter)) {
    $filters['file_type'] = $file_type_filter;
}

$total_files = count_digital_files($filters);
$total_pages = ceil($total_files / $files_per_page);

$digital_files = get_all_digital_files($filters, $current_page, $files_per_page);

$file_usage = [];
$file_types = [];
foreach ($digital_files as $file) {
    $file_usage[$file['id']] = get_digital_file_usage($file['id']);
    $file_types[$file['id']] = get_digital_file_file_types($file['id']);
}

$all_file_types = get_all_file_types();

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Gestão de Arquivos Digitais</h1>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4 collapsible-card" id="digitalFilesFilterCard">
        <div class="card-header" id="filterHeading">
            <h5 class="card-title mb-0">
                <button class="btn btn-link text-decoration-none p-0 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                    Filtrar Arquivos <i class="bi bi-chevron-down ms-1"></i>
                </button>
            </h5>
        </div>
        <div id="filterCollapse" class="collapse" aria-labelledby="filterHeading">
            <div class="card-body">
                <form method="GET" action="admin.php" class="row">
                    <input type="hidden" name="section" value="digital_files">
                    <input type="hidden" name="<?= SESSION_PARAM_NAME ?>" value="<?= session_id() ?>">

                    <div class="col-md-5 mb-3">
                        <label for="name" class="form-label">Nome do Arquivo</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= sanitize_input($name_filter) ?>" placeholder="Buscar por nome...">
                    </div>

                    <div class="col-md-5 mb-3">
                        <label for="file_type" class="form-label">Tipo de Arquivo</label>
                        <input type="text" class="form-control" id="file_type" name="file_type" value="<?= sanitize_input($file_type_filter) ?>" placeholder="Buscar por tipo...">
                    </div>

                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">Filtrar</button>
                        <a href="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" class="btn btn-outline-secondary">Limpar</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card mb-4 collapsible-card" id="digitalFilesUploadCard">
        <div class="card-header" id="uploadHeading">
            <h5 class="card-title mb-0">
                <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#uploadCollapse" aria-expanded="true" aria-controls="uploadCollapse">
                    Carregar Novo Arquivo <i class="bi bi-chevron-down ms-1"></i>
                </button>
            </h5>
        </div>
        <div id="uploadCollapse" class="collapse show" aria-labelledby="uploadHeading">
            <div class="card-body">
                <!-- Flash messages are displayed above -->

                <form method="POST" action="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" enctype="multipart/form-data" class="row">
                    <?= csrf_input_field() ?>
                    <input type="hidden" name="action" value="upload">

                    <div class="col-md-4 mb-3">
                        <label for="digital_file" class="form-label">Arquivo Digital *</label>
                        <input type="file" class="form-control" id="digital_file" name="digital_file" required>
                        <div class="form-text">Tamanho máximo: 100MB.</div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="new_file_display_name" class="form-label">Nome de Exibição</label>
                        <input type="text" class="form-control" id="new_file_display_name" name="new_file_display_name" placeholder="Opcional. Usa nome original se vazio.">
                        <div class="form-text">Nome amigável para este arquivo.</div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="short_name" class="form-label">Nome Interno</label>
                        <input type="text" class="form-control" id="short_name" name="short_name" placeholder="Nome curto para referência interna">
                        <div class="form-text">Nome curto para referência interna.</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Descrição</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        <div class="form-text">Uma descrição opcional para identificar este arquivo.</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">Tipos de Arquivo Incluídos</label>
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($all_file_types as $type): ?>
                                        <div class="col-md-3 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="file_types[]"
                                                       value="<?= $type['id'] ?>"
                                                       id="file_type_<?= $type['id'] ?>">
                                                <label class="form-check-label" for="file_type_<?= $type['id'] ?>">
                                                    <?= sanitize_input($type['name']) ?> (<?= sanitize_input($type['extension']) ?>)
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if (empty($all_file_types)): ?>
                                    <div class="alert alert-warning mb-0">
                                        Nenhum tipo de arquivo definido. <a href="admin.php?section=file_types&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" target="_blank">Adicionar tipos de arquivo</a>.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="form-text">Selecione as extensões de arquivo incluídas neste arquivo digital.</div>
                    </div>

                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-upload"></i> Carregar Arquivo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card collapsible-card" id="digitalFilesListCard">
        <div class="card-header" id="filesListHeading">
            <h5 class="card-title mb-0">
                <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#filesListCollapse" aria-expanded="true" aria-controls="filesListCollapse">
                    Arquivos Digitais Disponíveis <i class="bi bi-chevron-down ms-1"></i>
                </button>
            </h5>
        </div>
        <div id="filesListCollapse" class="collapse show" aria-labelledby="filesListHeading">
            <div class="card-body">
                <?php if (empty($digital_files)): ?>
                    <div class="alert alert-info mb-0">
                        Nenhum arquivo digital encontrado. Carregue seu primeiro arquivo usando o formulário ao lado.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Nome de Exibição</th>
                                    <th>Nome Interno</th>
                                    <th>Tamanho</th>
                                    <th>Tipos de Arquivo</th>
                                    <th>Descrição</th>
                                    <th>Data de Upload</th>
                                    <th>Em Uso</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                    <?php foreach ($digital_files as $file): ?>
                                        <tr>
                                            <td>
                                                <form method="POST" action="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" class="d-inline">
                                                    <?= csrf_input_field() ?>
                                                    <input type="hidden" name="action" value="update">
                                                    <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" class="form-control" name="display_name"
                                                               value="<?= sanitize_input(!empty($file['display_name']) ? $file['display_name'] : $file['original_filename']) ?>"
                                                               placeholder="Nome de Exibição">
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="bi bi-save"></i>
                                                        </button>
                                                    </div>
                                                </form>
                                            </td>
                                            <td>
                                                <form method="POST" action="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" class="d-inline">
                                                    <?= csrf_input_field() ?>
                                                    <input type="hidden" name="action" value="update">
                                                    <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" class="form-control" name="short_name"
                                                               value="<?= sanitize_input($file['short_name'] ?? '') ?>" placeholder="Nome Interno">
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="bi bi-save"></i>
                                                        </button>
                                                    </div>
                                                </form>
                                            </td>
                                            <td><?= format_file_size($file['file_size']) ?></td>
                                            <td>
                                                <div class="d-flex flex-wrap gap-1">
                                                    <?php if (!empty($file_types[$file['id']])): ?>
                                                        <?php foreach ($file_types[$file['id']] as $type): ?>
                                                            <span class="badge bg-info text-dark"><?= sanitize_input($type['extension']) ?></span>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">Nenhum tipo definido</span>
                                                    <?php endif; ?>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-primary mt-1"
                                                        data-bs-toggle="collapse"
                                                        data-bs-target="#file-types-row-<?= $file['id'] ?>"
                                                        aria-expanded="false">
                                                    <i class="bi bi-pencil"></i> Editar Tipos
                                                </button>
                                            </td>
                                            <td>
                                                <form method="POST" action="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" class="d-inline">
                                                    <?= csrf_input_field() ?>
                                                    <input type="hidden" name="action" value="update">
                                                    <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" class="form-control" name="description"
                                                               value="<?= sanitize_input($file['description'] ?? '') ?>" placeholder="Descrição">
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="bi bi-save"></i>
                                                        </button>
                                                    </div>
                                                </form>
                                            </td>
                                            <td><?= date('d/m/Y H:i', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <?php if (count($file_usage[$file['id']]) > 0): ?>
                                                    <span class="badge bg-success"><?= count($file_usage[$file['id']]) ?> produto(s)</span>
                                                    <button type="button" class="btn btn-sm btn-link p-0 ms-1"
                                                            data-bs-toggle="popover"
                                                            data-bs-trigger="focus"
                                                            title="Produtos usando este arquivo"
                                                            data-bs-content="<?= implode('<br>', array_map(function($p) { return sanitize_input($p['name_pt']); }, $file_usage[$file['id']])) ?>">
                                                        <i class="bi bi-info-circle"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Não</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (count($file_usage[$file['id']]) === 0): ?>
                                                    <form method="POST" action="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" class="d-inline" onsubmit="return confirm('Tem certeza que deseja excluir este arquivo?');">
                                                        <?= csrf_input_field() ?>
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <a href="<?= $file['file_path'] ?>" class="btn btn-sm btn-info" download>
                                                    <i class="bi bi-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <!-- File Types Row (Collapsible) -->
                                        <tr class="collapse" id="file-types-row-<?= $file['id'] ?>">
                                            <td colspan="10" class="bg-light p-3">
                                                <form method="POST" action="admin.php?section=digital_files&<?= SESSION_PARAM_NAME ?>=<?= session_id() ?>" class="file-types-form">
                                                    <?= csrf_input_field() ?>
                                                    <input type="hidden" name="action" value="update_file_types">
                                                    <input type="hidden" name="file_id" value="<?= $file['id'] ?>">

                                                    <div class="card">
                                                        <div class="card-header bg-primary text-white">
                                                            <h6 class="mb-0">Selecione os tipos de arquivo para: <?= sanitize_input(!empty($file['display_name']) ? $file['display_name'] : $file['original_filename']) ?></h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <?php
                                                            
                                                            $selected_type_ids = array_map(function($type_assoc) {
                                                                return $type_assoc['id'];
                                                            }, $file_types[$file['id']] ?? []);
                                                            ?>

                                                            <div class="d-flex flex-wrap gap-3 mb-3">
                                                                <?php foreach ($all_file_types as $type_option): ?>
                                                                    <div class="form-check form-check-inline">
                                                                        <input class="form-check-input"
                                                                               type="checkbox"
                                                                               name="file_types[]"
                                                                               value="<?= $type_option['id'] ?>"
                                                                               id="file_type_<?= $file['id'] ?>_<?= $type_option['id'] ?>"
                                                                               <?= in_array($type_option['id'], $selected_type_ids) ? 'checked' : '' ?>>
                                                                        <label class="form-check-label" for="file_type_<?= $file['id'] ?>_<?= $type_option['id'] ?>">
                                                                            <strong><?= sanitize_input($type_option['extension']) ?></strong>
                                                                        </label>
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            </div>

                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <button type="submit" class="btn btn-success">
                                                                    <i class="bi bi-save"></i> Guardar Tipos
                                                                </button>
                                                                <button type="button" class="btn btn-secondary"
                                                                        data-bs-toggle="collapse"
                                                                        data-bs-target="#file-types-row-<?= $file['id'] ?>">
                                                                    <i class="bi bi-x"></i> Fechar
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Page navigation" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous Button -->
                                    <li class="page-item <?= ($current_page <= 1) ? 'disabled' : '' ?>">
                                        <a class="page-link" href="admin.php?section=digital_files<?=
                                            (!empty($name_filter) ? '&name=' . urlencode($name_filter) : '') .
                                            (!empty($file_type_filter) ? '&file_type=' . urlencode($file_type_filter) : '') .
                                            '&p=' . ($current_page - 1) .
                                            '&' . SESSION_PARAM_NAME . '=' . session_id() ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>

                                    <?php
                                    $num_links_to_show = 5; 
                                    $start_page = max(1, $current_page - floor($num_links_to_show / 2));
                                    $end_page = min($total_pages, $start_page + $num_links_to_show - 1);

                                    
                                    if ($end_page == $total_pages) {
                                        $start_page = max(1, $total_pages - $num_links_to_show + 1);
                                    }
                                    ?>
                                    <?php
                                    
                                    if ($start_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?section=digital_files<?=
                                                (!empty($name_filter) ? '&name=' . urlencode($name_filter) : '') .
                                                (!empty($file_type_filter) ? '&file_type=' . urlencode($file_type_filter) : '') .
                                                '&p=1' .
                                                '&' . SESSION_PARAM_NAME . '=' . session_id() ?>">1</a>
                                        </li>
                                        <?php if ($start_page > 2): ?>
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        <?php endif;
                                    endif;

                                    
                                    for ($i = $start_page; $i <= $end_page; $i++): ?>
                                        <li class="page-item <?= ($i == $current_page) ? 'active' : '' ?>">
                                            <a class="page-link" href="admin.php?section=digital_files<?=
                                                (!empty($name_filter) ? '&name=' . urlencode($name_filter) : '') .
                                                (!empty($file_type_filter) ? '&file_type=' . urlencode($file_type_filter) : '') .
                                                '&p=' . $i .
                                                '&' . SESSION_PARAM_NAME . '=' . session_id() ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor;

                                    
                                    if ($end_page < $total_pages):
                                        if ($end_page < $total_pages - 1): ?>
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?section=digital_files<?=
                                                (!empty($name_filter) ? '&name=' . urlencode($name_filter) : '') .
                                                (!empty($file_type_filter) ? '&file_type=' . urlencode($file_type_filter) : '') .
                                                '&p=' . $total_pages .
                                                '&' . SESSION_PARAM_NAME . '=' . session_id() ?>"><?= $total_pages ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Next Button -->
                                    <li class="page-item <?= ($current_page >= $total_pages) ? 'disabled' : '' ?>">
                                        <a class="page-link" href="admin.php?section=digital_files<?=
                                            (!empty($name_filter) ? '&name=' . urlencode($name_filter) : '') .
                                            (!empty($file_type_filter) ? '&file_type=' . urlencode($file_type_filter) : '') .
                                            '&p=' . ($current_page + 1) .
                                            '&' . SESSION_PARAM_NAME . '=' . session_id() ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// This script block has been removed to use the global collapsible card logic
// from public/assets/js/admin-utils.js for consistency.
// The collapsible-card class has been added to the relevant card elements above.

document.addEventListener('DOMContentLoaded', function() {
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true // Ensure HTML content is allowed in popovers
        });
    });
});
</script>

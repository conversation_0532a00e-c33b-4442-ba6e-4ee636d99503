/* Admin Dashboard Styles */

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.dashboard-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

/* Welcome Section */
.welcome-section {
    background-color: #212529; /* Dark background */
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 5px solid #0d6efd; /* Primary color border */
    color: #e9ecef; /* Light text */
}

.welcome-section h2 {
    margin-top: 0;
    color: #0d6efd; /* Primary color for heading */
    font-weight: 600;
}

.welcome-section p {
    color: #e9ecef; /* Light text */
}

.welcome-section .date-time {
    color: #adb5bd; /* Light gray */
    font-size: 0.9rem;
}

/* Stat Cards */
.stat-card {
    flex: 1;
    min-width: 200px;
    transition: transform 0.2s, box-shadow 0.2s;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-card .card-body {
    display: flex;
    align-items: center;
    padding: 1.25rem;
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-card .stat-content {
    flex: 1;
}

.stat-card .stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.25rem;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: var(--bs-secondary-color);
    margin-bottom: 0;
}

.stat-card .stat-change {
    font-size: 0.75rem;
    position: absolute;
    bottom: 0.5rem;
    right: 0.75rem;
}

.stat-card .stat-change.positive {
    color: var(--bs-success);
}

.stat-card .stat-change.negative {
    color: var(--bs-danger);
}

/* Dashboard Widgets */
.dashboard-widget {
    background-color: #2c3034; /* Darker background */
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    margin-bottom: 1.5rem;
    overflow: hidden;
    color: #e9ecef; /* Lighter text for better contrast */
}

.widget-header {
    padding: 1rem 1.25rem;
    background-color: #212529; /* Darker header */
    border-bottom: 1px solid #495057;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #f8f9fa; /* Very light text for high contrast */
}

.widget-actions {
    display: flex;
    gap: 0.5rem;
}

.widget-actions .btn-outline-secondary {
    color: #adb5bd; /* Light gray for button text */
    border-color: #495057; /* Darker border */
}

.widget-actions .btn-outline-secondary:hover {
    background-color: #495057; /* Darker background on hover */
    color: #f8f9fa; /* Very light text on hover */
}

.widget-actions .btn-outline-primary {
    color: #0d6efd; /* Primary color for button text */
    border-color: #0d6efd; /* Primary color border */
}

.widget-actions .btn-outline-primary:hover {
    background-color: #0d6efd; /* Primary background on hover */
    color: #fff; /* White text on hover */
}

.widget-body {
    padding: 1.25rem;
    max-height: 600px;
    overflow-y: auto;
}

.widget-body.collapse:not(.show) {
    display: none;
}

.widget-body.collapsing {
    height: 0;
    overflow: hidden;
    transition: height 0.35s ease;
}

.widget-body.show {
    display: block;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Recent Items Tables */
.recent-items-table {
    width: 100%;
    color: #e9ecef; /* Light text for dark background */
}

.recent-items-table th {
    font-weight: 600;
    color: #f8f9fa; /* Very light text for headers */
}

.recent-items-table td {
    vertical-align: middle;
    color: #e9ecef; /* Light text for better contrast */
}

.recent-items-table tbody tr:hover {
    background-color: #343a40; /* Slightly lighter on hover */
}

.status-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 50rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #997404;
}

.status-badge.processing {
    background-color: rgba(13, 110, 253, 0.2);
    color: #084298;
}

.status-badge.completed {
    background-color: rgba(25, 135, 84, 0.2);
    color: #0f5132;
}

.status-badge.cancelled {
    background-color: rgba(220, 53, 69, 0.2);
    color: #842029;
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-action-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 1.5rem 1rem;
    border-radius: 0.5rem;
    background-color: #343a40; /* Darker background */
    transition: all 0.2s;
    color: #e9ecef; /* Light text */
    text-decoration: none; /* Remove underline from links */
}

.quick-action-card:hover {
    background-color: #0d6efd; /* Primary color on hover */
    transform: translateY(-5px);
    color: #fff; /* White text on hover */
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    color: #0d6efd; /* Primary color for icon */
}

.quick-action-card:hover .quick-action-icon {
    color: #fff; /* White icon on hover */
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #f8f9fa; /* Very light text */
}

.quick-action-description {
    font-size: 0.8rem;
    color: #adb5bd; /* Lighter gray for description */
}

.quick-action-card:hover .quick-action-title,
.quick-action-card:hover .quick-action-description {
    color: #fff; /* White text on hover */
}

/* System Status */
.system-status-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #495057; /* Darker border */
}

.system-status-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.system-status-icon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

.system-status-icon.healthy {
    color: #28a745; /* Brighter green */
}

.system-status-icon.warning {
    color: #ffc107; /* Brighter yellow */
}

.system-status-icon.critical {
    color: #dc3545; /* Brighter red */
}

.system-status-content {
    flex: 1;
}

.system-status-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #f8f9fa; /* Very light text */
}

.system-status-value {
    font-size: 0.875rem;
    color: #adb5bd; /* Light gray */
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .dashboard-row {
        flex-direction: column;
    }

    .stat-card {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

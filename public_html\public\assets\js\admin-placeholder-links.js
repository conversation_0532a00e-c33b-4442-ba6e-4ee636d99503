

document.addEventListener('DOMContentLoaded', function() {
    
    if (new URLSearchParams(window.location.search).get('section') === 'placeholder_links') {
        initPlaceholderLinksFeatures();
    }

    
    
    window.initPageSpecificFeatures = function() {
        const urlParams = new URLSearchParams(window.location.search);
        const section = urlParams.get('section');
        
        if (section === 'placeholder_links') {
            initPlaceholderLinksFeatures();
        }
        
    };

    function initPlaceholderLinksFeatures() {
        const urlParams = new URLSearchParams(window.location.search);
        const section = urlParams.get('section');
        const action = urlParams.get('action');

        
        if (section !== 'placeholder_links') {
            
            return;
        }

        
        const deleteButtons = document.querySelectorAll('.btn-danger[title="Eliminar"]');
        deleteButtons.forEach(button => {
            button.removeEventListener('click', handleDeleteLink); 
            button.addEventListener('click', handleDeleteLink);
        });

        
        const editButtons = document.querySelectorAll('.btn-primary[title="Editar"][href*="section=placeholder_links&action=edit"]');
        editButtons.forEach(button => {
            button.removeEventListener('click', handleAjaxNavigation); 
            button.addEventListener('click', handleAjaxNavigation);
        });

        
        const newLinkButtons = document.querySelectorAll('a[href*="section=placeholder_links&action=new"]');
        newLinkButtons.forEach(button => {
            button.removeEventListener('click', handleAjaxNavigation); 
            button.addEventListener('click', handleAjaxNavigation);
        });

        
        
        if (action === 'new' || action === 'edit') {
             
            if (window.AdminUtils && typeof window.AdminUtils.initAjaxForms === 'function') {
            }
        }
    }

    function handleDeleteLink(e) {
        e.preventDefault();
        const url = this.getAttribute('href');
        if (confirm('Tem a certeza que deseja eliminar este link?')) {
            
            
            window.location.href = url;
        }
    }

    function handleAjaxNavigation(e) {
        e.preventDefault();
        const url = this.getAttribute('href');
        if (typeof window.loadContent === 'function') {
            
            const relativeUrl = url.startsWith('admin.php') ? url : 'admin.php?' + url.split('?')[1];
            window.loadContent(relativeUrl);

            
            const historyUrl = url.includes('?') ? 'admin.php' + url.substring(url.indexOf('?')) : url;
            window.history.pushState({ url: url }, '', historyUrl);

        } else {
            window.location.href = url;
        }
    }
});

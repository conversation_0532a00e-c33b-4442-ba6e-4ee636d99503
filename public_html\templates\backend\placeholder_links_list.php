<?php

?>

<h1><PERSON><PERSON><PERSON> Links de Placeholders</h1>
<p class="text-muted">Os links permitem adicionar links externos aos placeholders no rodapé do site.</p>

<a href="admin.php?section=placeholder_links&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Criar Novo Link
</a>

<?php if (!empty($placeholders_with_links)): ?>
    <?php foreach ($placeholders_with_links as $placeholder): ?>
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= htmlspecialchars($placeholder['name']) ?></h5>
                <a href="admin.php?section=placeholder_links&action=new&placeholder_id=<?= $placeholder['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-success">
                    <i class="bi bi-plus-circle"></i> Adicionar Link
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($placeholder['links'])): ?>
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Título</th>
                                <th>URL</th>
                                <th>Destino</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($placeholder['links'] as $link): ?>
                                <tr>
                                    <td><?= htmlspecialchars($link['id']) ?></td>
                                    <td><?= htmlspecialchars($link['title']) ?></td>
                                    <td>
                                        <a href="<?= htmlspecialchars($link['url']) ?>" target="_blank" rel="noopener noreferrer" class="text-truncate d-inline-block" style="max-width: 250px;" title="<?= htmlspecialchars($link['url']) ?>">
                                            <?= htmlspecialchars($link['url']) ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($link['target'] === '_blank'): ?>
                                            <span class="badge bg-info">Nova Janela</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Mesma Janela</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="admin.php?section=placeholder_links&action=edit&id=<?= $link['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-primary me-1" title="Editar">
                                            <i class="bi bi-pencil-square"></i>
                                        </a>
                                        <a href="admin.php?section=placeholder_links&action=delete&id=<?= $link['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-danger" title="Eliminar" onclick="return confirm('Tem a certeza que deseja eliminar este link?');">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p class="text-muted">Não existem links para este placeholder.</p>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
<?php else: ?>
    <div class="alert alert-info">
        <p>Não existem placeholders criados. <a href="admin.php?section=page_placeholders&action=new&<?= get_session_id_param() ?>">Crie um placeholder</a> primeiro.</p>
    </div>
<?php endif; ?>

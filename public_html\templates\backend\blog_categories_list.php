<?php

$categories = $categories ?? []; 

?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Categorias do Blog</h3>
                    <div class="card-tools">
                        <a href="admin.php?section=blog_categories&action=new&<?php echo get_session_id_param(); ?>" class="btn btn-success btn-sm">
                            <i class="bi bi-plus-circle"></i> Adicionar Nova Categoria
                        </a>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                    <?php display_flash_messages(); ?>
                    <?php if (empty($categories)): ?>
                        <p>Não existem categorias de blog criadas.</p>
                    <?php else: ?>
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Slug</th>
                                    <th>Ativa</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($category['id']); ?></td>
                                        <td><?php echo htmlspecialchars($category['name']); ?></td>
                                        <td><?php echo htmlspecialchars($category['slug']); ?></td>
                                        <td>
                                            <?php if ($category['is_active']): ?>
                                                <span class="badge bg-success">Sim</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Não</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="admin.php?section=blog_categories&action=edit&id=<?php echo $category['id']; ?>&<?php echo get_session_id_param(); ?>" class="btn btn-primary btn-sm" title="Editar">
                                                <i class="bi bi-pencil-square"></i> Editar
                                            </a>
                                            <a href="admin.php?section=blog_categories&action=delete&id=<?php echo $category['id']; ?>&<?php echo get_session_id_param(); ?>"
                                               class="btn btn-danger btn-sm delete-confirm"
                                               data-confirm-message="Tem a certeza que deseja remover a categoria '<?php echo htmlspecialchars($category['name']); ?>'? Os posts não serão removidos."
                                               title="Remover">
                                                <i class="bi bi-trash"></i> Remover
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->
        </div>
    </div>
</div>

<!-- Add Confirmation Modal specific JS if needed, or rely on a global admin.js -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-confirm');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default link behavior
            const message = this.getAttribute('data-confirm-message') || 'Tem a certeza?';
            if (confirm(message)) {
                window.location.href = this.href; // Proceed with deletion
            }
        });
    });
});
</script>
<?php

$is_edit_mode = ($action === 'edit' && !empty($category_data));
$form_action_url = 'admin.php?' . get_session_id_param();

$name = $form_data['name'] ?? ($category_data['name'] ?? '');
$slug = $form_data['slug'] ?? ($category_data['slug'] ?? '');
$description = $form_data['description'] ?? ($category_data['description'] ?? '');
$is_active = $form_data['is_active'] ?? ($category_data['is_active'] ?? 1); 

?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $is_edit_mode ? 'Editar' : 'Adicionar Nova'; ?> Categoria de Blog</h3>
                </div>
                <!-- /.card-header -->
                <!-- form start -->
                <form action="<?php echo $form_action_url; ?>" method="post" id="blogCategoryForm">
                    <input type="hidden" name="section" value="blog_categories">
                    <input type="hidden" name="action" value="<?php echo $is_edit_mode ? 'edit' : 'new'; ?>">
                    <?php if ($is_edit_mode): ?>
                        <input type="hidden" name="id" value="<?php echo htmlspecialchars($item_id); ?>">
                    <?php endif; ?>
                    <?php echo csrf_input_field(); ?>

                    <div class="card-body">
                        <?php display_flash_messages(); ?>

                        <div class="form-group mb-3">
                            <label for="name">Nome da Categoria <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="<?php echo htmlspecialchars($name); ?>" required>
                            <small class="form-text text-muted">O nome principal da categoria.</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="slug">Slug (URL)</label>
                            <input type="text" class="form-control" id="slug" name="slug"
                                   value="<?php echo htmlspecialchars($slug); ?>"
                                   aria-describedby="slugHelp">
                            <small id="slugHelp" class="form-text text-muted">
                                Parte do URL amigável. Será gerado automaticamente a partir do nome se deixado em branco. Use apenas letras minúsculas, números e hífens.
                            </small>
                        </div>

                        <div class="form-group mb-3">
                             <label for="description">Descrição</label>
                             <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
                             <small class="form-text text-muted">Descrição opcional da categoria.</small>
                         </div>

                        <div class="form-group form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" <?php echo $is_active ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">Ativa</label>
                            <small class="form-text text-muted d-block">Desmarque para ocultar esta categoria no site.</small>
                        </div>

                    </div>
                    <!-- /.card-body -->

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">Guardar Categoria</button>
                        <a href="admin.php?section=blog_categories&<?php echo get_session_id_param(); ?>" class="btn btn-secondary">Cancelar</a>
                    </div>
                </form>
            </div>
            <!-- /.card -->
        </div>
    </div>
</div>

<script>
    // Optional: Basic slug generation/validation helper
    document.addEventListener('DOMContentLoaded', function() {
        const nameInput = document.getElementById('name');
        const slugInput = document.getElementById('slug');

        if (nameInput && slugInput) {
            // Auto-generate slug if slug field is empty when name changes
            nameInput.addEventListener('blur', function() {
                if (slugInput.value.trim() === '') {
                     // Basic slugify (replace spaces and special chars with hyphens, lowercase)
                    let slug = this.value.toLowerCase()
                                     .replace(/\s+/g, '-') // Replace spaces with -
                                     .replace(/[^\w\-]+/g, '') // Remove all non-word chars except -
                                     .replace(/\-\-+/g, '-') // Replace multiple - with single -
                                     .replace(/^-+/, '') // Trim - from start
                                     .replace(/-+$/, ''); // Trim - from end
                     slugInput.value = slug;
                }
            });

            // Basic slug validation on blur (optional)
            slugInput.addEventListener('blur', function() {
                let slug = this.value.toLowerCase()
                                  .replace(/\s+/g, '-')
                                  .replace(/[^\w\-]+/g, '')
                                  .replace(/\-\-+/g, '-')
                                  .replace(/^-+/, '')
                                  .replace(/-+$/, '');
                 if (this.value !== slug) {
                    // Maybe add a warning or just auto-correct
                    this.value = slug;
                 }
            });
        }
    });
</script>
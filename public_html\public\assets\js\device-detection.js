

(function() {
    'use strict';
    
    
    function getDeviceInfo() {
        return {
            orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait',
            screen_width: window.innerWidth || screen.width,
            screen_height: window.innerHeight || screen.height,
            device_pixel_ratio: window.devicePixelRatio || 1
        };
    }
    
    
    function sendDeviceInfo(deviceInfo) {
        
        var xhr = new XMLHttpRequest();
        xhr.open('POST', '/includes/ajax/device_info_handler.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        var params = 'orientation=' + encodeURIComponent(deviceInfo.orientation) +
                    '&screen_width=' + encodeURIComponent(deviceInfo.screen_width) +
                    '&screen_height=' + encodeURIComponent(deviceInfo.screen_height) +
                    '&device_pixel_ratio=' + encodeURIComponent(deviceInfo.device_pixel_ratio);
        
        xhr.send(params);
    }
    
    
    function updateDeviceInfo() {
        var deviceInfo = getDeviceInfo();
        sendDeviceInfo(deviceInfo);
    }
    
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', updateDeviceInfo);
    } else {
        updateDeviceInfo();
    }
    
    
    window.addEventListener('orientationchange', function() {
        
        setTimeout(updateDeviceInfo, 100);
    });
    
    
    window.addEventListener('resize', function() {
        
        clearTimeout(window.deviceDetectionTimeout);
        window.deviceDetectionTimeout = setTimeout(updateDeviceInfo, 250);
    });
    
})();
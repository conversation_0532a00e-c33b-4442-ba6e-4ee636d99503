<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/digital_order_functions.php';

if (!isset($_SESSION['customer_id'])) {
    
    header('Location: index.php?view=login&redirect=account_downloads&' . get_session_id_param());
    exit;
}

$customer_id = $_SESSION['customer_id'];
$customer = get_customer_by_id($customer_id);

if (!$customer) {
    
    unset($_SESSION['customer_id']);
    header('Location: index.php?view=login&' . get_session_id_param());
    exit;
}

$licenses = get_customer_licenses($customer['email']);

if (isset($_GET['download']) && isset($_GET['license'])) {
    $license_code = sanitize_input($_GET['license']);
    
    
    $validity = check_license_validity($license_code);
    
    if ($validity['valid']) {
        
        $result = process_download_request($license_code);
        
        if ($result['success']) {
            
            $_SESSION['download_file'] = [
                'path' => $result['file_path'],
                'name' => $result['file_name'],
                'expires' => time() + 300 
            ];
            
            
            header('Location: index.php?view=download_file&token=' . bin2hex(random_bytes(16)) . '&' . get_session_id_param());
            exit;
        } else {
            $error_message = $result['message'];
        }
    } else {
        $error_message = $validity['message'];
    }
}
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <h1 class="text-2xl font-semibold mb-6">Meus Downloads</h1>
    
    <?php if (isset($error_message)): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline"><?= sanitize_input($error_message) ?></span>
        </div>
    <?php endif; ?>
    
    <?php if (empty($licenses)): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <p class="text-center">Você ainda não possui produtos digitais para download.</p>
            <div class="mt-4 text-center">
                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=products') ?>" class="inline-block bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-button font-medium">
                    Explorar Produtos
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="grid grid-cols-1 gap-6">
            <?php foreach ($licenses as $license): ?>
                <?php
                
                $digital_products = get_license_digital_products($license['id']);
                
                
                $file_types = [];
                foreach ($digital_products as $product) {
                    $product_file_types = get_digital_product_file_types($product['id']);
                    $file_types = array_merge($file_types, $product_file_types);
                }
                
                
                $unique_file_types = [];
                foreach ($file_types as $type) {
                    $unique_file_types[$type['id']] = $type;
                }
                $file_types = array_values($unique_file_types);
                
                
                $product_info = null;
                if (isset($license['order_item_id'])) {
                    $order_item = get_order_item_by_id($license['order_item_id']);
                    if ($order_item) {
                        $product_info = get_product_by_id($order_item['product_id']);
                    }
                }
                
                
                $is_expired = false;
                $days_left = 0;
                if ($license['expiry_date']) {
                    $expiry_timestamp = strtotime($license['expiry_date']);
                    $current_timestamp = time();
                    $is_expired = $expiry_timestamp < $current_timestamp;
                    $days_left = floor(($expiry_timestamp - $current_timestamp) / (60 * 60 * 24));
                }
                
                
                $downloads_used = (int)$license['downloads_used'];
                $download_limit = (int)$license['download_limit'];
                $downloads_left = $download_limit - $downloads_used;
                $is_download_limit_reached = $downloads_left <= 0;
                
                
                $status_class = 'bg-green-900 text-green-100';
                $status_text = 'Ativo';
                
                if ($license['status'] === 'waiting_payment') {
                    $status_class = 'bg-yellow-900 text-yellow-100';
                    $status_text = 'Aguardando Pagamento';
                } elseif ($license['status'] === 'revoked') {
                    $status_class = 'bg-red-900 text-red-100';
                    $status_text = 'Revogada';
                } elseif ($is_expired) {
                    $status_class = 'bg-red-900 text-red-100';
                    $status_text = 'Expirada';
                } elseif ($is_download_limit_reached) {
                    $status_class = 'bg-orange-900 text-orange-100';
                    $status_text = 'Limite Atingido';
                }
                ?>
                <div class="bg-gray-900 rounded-lg p-6">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h2 class="text-xl font-medium">
                                <?= $product_info ? sanitize_input($product_info['name_pt']) : 'Produto Digital' ?>
                            </h2>
                            <p class="text-gray-400 text-sm">Licença: <?= sanitize_input($license['license_code']) ?></p>
                        </div>
                        <span class="inline-block px-3 py-1 text-sm rounded <?= $status_class ?> mt-2 md:mt-0">
                            <?= $status_text ?>
                        </span>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <p class="text-gray-400 text-sm mb-1">Data de Expiração:</p>
                            <p class="font-medium">
                                <?= $license['expiry_date'] ? date('d/m/Y H:i', strtotime($license['expiry_date'])) : 'Sem expiração' ?>
                                <?php if (!$is_expired && $license['expiry_date']): ?>
                                    <span class="text-sm text-gray-400">(<?= $days_left ?> dias restantes)</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-400 text-sm mb-1">Downloads:</p>
                            <p class="font-medium"><?= $downloads_used ?> de <?= $download_limit ?> utilizados</p>
                        </div>
                    </div>
                    
                    <?php if (!empty($file_types)): ?>
                        <div class="mb-4">
                            <p class="text-gray-400 text-sm mb-1">Extensões incluídas:</p>
                            <div class="flex flex-wrap gap-2">
                                <?php foreach ($file_types as $type): ?>
                                    <span class="inline-block px-2 py-1 text-xs rounded bg-gray-800 text-gray-300">
                                        <?= sanitize_input($type['extension']) ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <?php if ($license['status'] === 'active' && !$is_expired && !$is_download_limit_reached): ?>
                            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=account_downloads&download=1&license=' . urlencode($license['license_code'])) ?>" 
                               class="inline-block bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-button font-medium">
                                <i class="ri-download-line mr-2"></i> Baixar Arquivos
                            </a>
                        <?php else: ?>
                            <button class="inline-block bg-gray-700 text-gray-300 py-2 px-4 rounded-button font-medium cursor-not-allowed" disabled>
                                <i class="ri-download-line mr-2"></i> Baixar Arquivos
                            </button>
                            <p class="text-sm text-gray-400 mt-2">
                                <?php if ($license['status'] === 'waiting_payment'): ?>
                                    Aguardando confirmação de pagamento para autorizar o download.
                                <?php elseif ($license['status'] === 'revoked'): ?>
                                    Esta licença foi revogada. Entre em contato com o suporte.
                                <?php elseif ($is_expired): ?>
                                    O período de download expirou em <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>.
                                <?php elseif ($is_download_limit_reached): ?>
                                    Você atingiu o limite máximo de downloads para este produto.
                                <?php endif; ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

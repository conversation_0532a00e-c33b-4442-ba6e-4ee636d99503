<?php

if (ob_get_level()) ob_end_clean();

header('Content-Type: application/json');

require_once __DIR__ . '/includes/init.php';
require_once __DIR__ . '/includes/digital_files_functions.php';

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit;
}

if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Token CSRF inválido']);
    exit;
}

$file_id = isset($_POST['file_id']) ? (int)$_POST['file_id'] : 0;
if ($file_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID de arquivo inválido']);
    exit;
}

try {
    
    $file_type_ids = get_digital_file_file_type_ids($file_id);

    
    if (!is_array($file_type_ids)) {
        $file_type_ids = [];
    }

    
    echo json_encode([
        'success' => true,
        'message' => 'Tipos de arquivo obtidos com sucesso',
        'file_type_ids' => $file_type_ids
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao obter tipos de arquivo: ' . $e->getMessage(),
        'file_type_ids' => [] 
    ]);
}

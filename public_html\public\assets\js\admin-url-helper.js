(function() {
    'use strict';

    function getAdminBaseUrl() {
        const currentPath = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
        return window.location.origin + currentPath;
    }

    function getAbsoluteAdminUrl(url) {
        if (!url) return '';

        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }

        if (url.startsWith('admin.php') || url.startsWith('/admin.php')) {
            return getAdminBaseUrl() + (url.startsWith('/') ? url.substring(1) : url);
        }

        return url;
    }

    window.AdminUrlHelper = {
        getAdminBaseUrl,
        getAbsoluteAdminUrl
    };
})();

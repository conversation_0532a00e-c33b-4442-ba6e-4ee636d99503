(function() {
    'use strict';

    let collapsibleCards = [];

    function initCollapsibleCards() {
        collapsibleCards = document.querySelectorAll('.collapsible-card');

        collapsibleCards.forEach(card => {
            const cardId = card.id || `card-${Math.random().toString(36).substr(2, 9)}`;
            if (!card.id) card.id = cardId;

            const cardHeader = card.querySelector('.card-header');
            const cardBody = card.querySelector('.card-body');

            if (!cardHeader || !cardBody) return;

            const collapseId = `collapse-${cardId}`;
            cardBody.id = collapseId;

            cardHeader.setAttribute('data-bs-toggle', 'collapse');
            cardHeader.setAttribute('data-bs-target', `#${collapseId}`);
            cardHeader.setAttribute('aria-expanded', 'true');
            cardHeader.setAttribute('aria-controls', collapseId);

            cardBody.classList.add('collapse', 'show');

            let toggleIcon = cardHeader.querySelector('.card-collapse-icon');
            if (!toggleIcon) {
                toggleIcon = document.createElement('i');
                toggleIcon.className = 'bi bi-chevron-up card-collapse-icon';
                cardHeader.appendChild(toggleIcon);
            }

            cardHeader.classList.add('collapsible-header');

            const isCollapsed = localStorage.getItem(`card-collapsed-${cardId}`) === 'true';

            if (isCollapsed) {
                cardBody.classList.remove('show');
                toggleIcon.classList.replace('bi-chevron-up', 'bi-chevron-down');
                cardHeader.setAttribute('aria-expanded', 'false');
            }

            cardBody.addEventListener('hidden.bs.collapse', function() {
                localStorage.setItem(`card-collapsed-${cardId}`, 'true');
                toggleIcon.classList.replace('bi-chevron-up', 'bi-chevron-down');
            });

            cardBody.addEventListener('shown.bs.collapse', function() {
                localStorage.setItem(`card-collapsed-${cardId}`, 'false');
                toggleIcon.classList.replace('bi-chevron-down', 'bi-chevron-up');
            });
        });
    }

    function initAjaxForms() {
        const ajaxForms = document.querySelectorAll('form:not([data-no-ajax="true"]).ajax-form, form:not([data-no-ajax="true"]).reply-form');

        ajaxForms.forEach(form => {
            form.isSubmitting = false;

            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                if (form.isSubmitting) return;
                form.isSubmitting = true;

                const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
                const originalButtonTexts = [];

                submitButtons.forEach(button => {
                    originalButtonTexts.push(button.innerHTML);
                    button.disabled = true;
                    button.innerHTML = '<i class="bi bi-hourglass-split"></i> A processar...';
                });

                const formData = new FormData(form);
                formData.append('is_ajax', '1');

                
                
                const sessionIdentifier = window.eshopSessionId || new URLSearchParams(window.location.search).get('sid');

                
                
                if (sessionIdentifier) {
                    
                    if (formData.has('sid')) {
                        formData.delete('sid');
                    }
                    formData.append('sid', sessionIdentifier);

                    
                    const csrfToken = document.querySelector('input[name="csrf_token"]');
                    if (csrfToken && csrfToken.value) {
                        
                        if (formData.has('csrf_token')) {
                            formData.delete('csrf_token');
                        }
                        formData.append('csrf_token', csrfToken.value);
                    }
                }

                if (!formData.has('section') || !formData.get('section')) {
                    try {
                        const dataSection = form.getAttribute('data-section');
                        if (dataSection) {
                            formData.append('section', dataSection);
                        } else {
                            const formAction = form.getAttribute('action');
                            if (formAction) {
                                const actionUrl = new URL(formAction, window.location.origin);
                                const section = actionUrl.searchParams.get('section');
                                if (section) {
                                    formData.append('section', section);
                                } else {
                                    const urlParams = new URLSearchParams(window.location.search);
                                    const sectionFromUrl = urlParams.get('section');
                                    if (sectionFromUrl) {
                                        formData.append('section', sectionFromUrl);
                                    }
                                }
                            }
                        }
                    } catch (e) {  }
                }

                
                if (!formData.has('action') || !formData.get('action')) {
                    try {
                        
                        const formAction = form.getAttribute('action');
                        if (formAction) {
                            try {
                                const actionUrl = new URL(formAction, window.location.origin);
                                const action = actionUrl.searchParams.get('action');
                                if (action) {
                                    
                                    const actionPart = action.split('&')[0];
                                    formData.append('action', actionPart);
                                } else {
                                    const urlParams = new URLSearchParams(window.location.search);
                                    const actionFromUrl = urlParams.get('action');
                                    if (actionFromUrl) {
                                        formData.append('action', actionFromUrl);
                                    }
                                }
                            } catch (urlError) {
                            }
                        } else {
                            
                            const urlParams = new URLSearchParams(window.location.search);
                            const actionFromUrl = urlParams.get('action');
                            if (actionFromUrl) {
                                formData.append('action', actionFromUrl);
                            }
                        }
                    } catch (e) {
                    }
                }

                
                if (!formData.has('id') || !formData.get('id')) {
                    try {
                        
                        const idInput = form.querySelector('input[name="id"]');
                        if (idInput && idInput.value) {
                            formData.append('id', idInput.value);
                        } else {
                            
                            const dataId = form.getAttribute('data-id');
                            if (dataId) {
                                formData.append('id', dataId);
                            } else {
                                
                                const urlParams = new URLSearchParams(window.location.search);
                                const idFromUrl = urlParams.get('id');
                                if (idFromUrl) {
                                    formData.append('id', idFromUrl);
                                } else {
                                    
                                    const formAction = form.getAttribute('action');
                                    if (formAction) {
                                        try {
                                            const actionUrl = new URL(formAction, window.location.origin);
                                            const id = actionUrl.searchParams.get('id');
                                            if (id) {
                                                formData.append('id', id);
                                            }
                                        } catch (urlError) {
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                    }
                }

                try {
                    
                    let formAction;

                    
                    if (form.getAttribute('action')) {
                        formAction = form.getAttribute('action');
                    } else {
                        
                        formAction = form.action;
                        if (typeof formAction !== 'string') {
                            
                            try {
                                formAction = formAction.toString();
                            } catch (e) {
                                formAction = 'admin.php?' + new URLSearchParams(window.location.search).toString();
                            }
                        }
                    }

                    
                    if (window.AdminUrlHelper && formAction.includes('admin.php')) {
                        try {
                            const formActionUrl = new URL(formAction, window.location.origin);
                            formAction = window.AdminUrlHelper.getAdminBaseUrl() + 'admin.php' + formActionUrl.search;
                        } catch (e) {
                        }
                    } else if (formAction.includes('admin.php')) {
                        try {
                            const currentPath = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
                            const formActionUrl = new URL(formAction, window.location.origin);
                            formAction = window.location.origin + currentPath + 'admin.php' + formActionUrl.search;
                        } catch (e) {
                        }
                    }

                    const response = await fetch(formAction, {
                        method: form.method || 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    const contentType = response.headers.get('Content-Type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Resposta inválida do servidor. Verifique os logs para mais detalhes.');
                    }

                    const result = await response.json();

                    if (result.success) {
                        showNotification(result.message || 'Operação concluída com sucesso!', 'success');

                        if (result.redirect) {
                            let redirectUrl = result.redirect;

                            if (window.AdminUrlHelper) {
                                redirectUrl = window.AdminUrlHelper.getAbsoluteAdminUrl(redirectUrl);
                            } else if (redirectUrl.startsWith('admin.php') || redirectUrl.startsWith('/admin.php')) {
                                const currentPath = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
                                redirectUrl = window.location.origin + currentPath + redirectUrl;
                            }

                            setTimeout(() => {
                                window.location.href = redirectUrl;
                            }, 1000);
                            return;
                        }

                        if (result.updates) {
                            handleDomUpdates(result.updates);
                        }

                        if (result.reset_form) {
                            form.reset();
                        }
                    } else {
                        showNotification(result.message || 'Ocorreu um erro.', 'danger');

                        if (result.errors) {
                            handleValidationErrors(form, result.errors);
                        }
                    }
                } catch (error) {
                    showNotification('Erro de comunicação com o servidor: ' + error.message, 'danger');
                } finally {
                    submitButtons.forEach((button, index) => {
                        button.disabled = false;
                        button.innerHTML = originalButtonTexts[index];
                    });

                    form.isSubmitting = false;
                }
            });
        });
    }

    const recentNotifications = [];
    const NOTIFICATION_DEDUP_TIMEOUT = 2000;

    function showNotification(message, type = 'info') {
        const notificationKey = `${message}-${type}`;
        const now = Date.now();

        while (recentNotifications.length > 0 &&
               now - recentNotifications[0].timestamp > NOTIFICATION_DEDUP_TIMEOUT) {
            recentNotifications.shift();
        }

        const isDuplicate = recentNotifications.some(n => n.key === notificationKey);
        if (isDuplicate) return;

        recentNotifications.push({
            key: notificationKey,
            timestamp: now
        });

        let notificationContainer = document.getElementById('notification-container');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            notificationContainer.className = 'position-fixed top-0 end-0 p-3';
            notificationContainer.style.zIndex = '1050';
            document.body.appendChild(notificationContainer);
        }

        const notification = document.createElement('div');
        notification.className = `toast align-items-center text-bg-${type} border-0`;
        notification.role = 'alert';
        notification.setAttribute('aria-live', 'assertive');
        notification.setAttribute('aria-atomic', 'true');

        notification.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        notificationContainer.appendChild(notification);

        const toast = new bootstrap.Toast(notification, {
            autohide: true,
            delay: 5000
        });
        toast.show();

        notification.addEventListener('hidden.bs.toast', function() {
            notification.remove();
        });
    }

    function handleValidationErrors(form, errors) {
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });

        for (const field in errors) {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');

                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = errors[field];

                input.parentNode.insertBefore(errorDiv, input.nextSibling);
            }
        }
    }

    function handleDomUpdates(updates) {
        for (const selector in updates) {
            const element = document.querySelector(selector);
            if (element) {
                const update = updates[selector];

                if (update.html) {
                    element.innerHTML = update.html;
                }

                if (update.value) {
                    element.value = update.value;
                }

                if (update.attributes) {
                    for (const attr in update.attributes) {
                        element.setAttribute(attr, update.attributes[attr]);
                    }
                }

                if (update.classes) {
                    if (update.classes.add) {
                        update.classes.add.forEach(cls => {
                            element.classList.add(cls);
                        });
                    }
                    if (update.classes.remove) {
                        update.classes.remove.forEach(cls => {
                            element.classList.remove(cls);
                        });
                    }
                }
            }
        }
    }

    const style = document.createElement('style');
    style.textContent = `
        .collapsible-header {
            cursor: pointer;
            position: relative;
            padding-right: 30px;
            user-select: none;
        }

        .card-collapse-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
        }

        .card-header {
            user-select: none;
        }

        .collapse {
            transition: height 0.35s ease;
        }

        .notification-container {
            z-index: 1050;
        }
    `;

    if (document.head) {
        document.head.appendChild(style);
    } else {
        document.addEventListener('DOMContentLoaded', () => document.head.appendChild(style));
    }

    initCollapsibleCards();
    initAjaxForms();

    window.AdminUtils = {
        initCollapsibleCards,
        initAjaxForms,
        showNotification
    };
})();

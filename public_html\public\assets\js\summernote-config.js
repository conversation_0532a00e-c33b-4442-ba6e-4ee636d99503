

(function() {
    
    const initializedEditors = new Set();

    
    function applyBootstrap5Fixes() {
        
        
        if (typeof jQuery !== 'undefined') {
            const $ = jQuery;

            
            $('body').addClass('summernote-bs5-fix-applied');
        }
    }

    

    function initSummernote(selector = '.summernote-editor', customOptions = {}) {
        
        if (typeof jQuery === 'undefined' || typeof jQuery.fn.summernote === 'undefined') {
            console.warn('Summernote or jQuery not loaded yet. Will retry in 100ms.');
            setTimeout(() => initSummernote(selector, customOptions), 100);
            return;
        }

        const $ = jQuery;

        
        const defaultOptions = {
            placeholder: 'Escreva o conteúdo aqui...',
            tabsize: 2,
            height: 300,
            dialogsInBody: true, 
            dialogsFade: true,
            disableDragAndDrop: false,
            styleTags: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre'],
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                onInit: function() {

                    
                    const editor = this;
                    const $editor = $(editor);

                    
                    $editor.addClass('summernote-bs5-fixed');

                    
                    $editor.find('.note-btn-group .dropdown-toggle').each(function() {
                        const $toggle = $(this);
                        $toggle.removeAttr('data-bs-toggle');
                        $toggle.removeAttr('data-bs-auto-close');
                    });

                    
                    $(document).trigger('summernote.initialized', [editor]);
                },
                onFocus: function() {
                },
                onBlur: function() {
                }
            }
        };

        
        const options = $.extend(true, {}, defaultOptions, customOptions);

        try {
            
            $(selector).each(function() {
                const $this = $(this);
                const editorId = $this.attr('id') || $this.data('editor-id');

                
                if (initializedEditors.has(editorId)) {
                    return;
                }

                
                if ($this.hasClass('note-editor') || $this.next().hasClass('note-editor')) {
                    try {
                        $this.summernote('destroy');
                    } catch (e) {
                        console.warn(`Error destroying previous Summernote instance for ${editorId}:`, e);
                    }
                }

                
                $this.summernote(options);

                
                if (editorId) {
                    initializedEditors.add(editorId);
                }
            });
        } catch (error) {
        }
    }

    
    document.addEventListener('DOMContentLoaded', function() {
        applyBootstrap5Fixes();
        initSummernote();
    });

    
    document.addEventListener('contentLoaded', function() {
        initSummernote();
    });

    
    window.initSummernoteEditors = initSummernote;
})();

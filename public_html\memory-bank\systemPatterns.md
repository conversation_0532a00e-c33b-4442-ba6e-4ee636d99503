# Memory Bank File: systemPatterns.md

## Database Architecture

### Performance Optimization Pattern: CSV to SQLite Migration
**Context:** Large dataset lookups (IP geolocation with 136,595+ records)

**Pattern Implementation:**
- **Before:** CSV file parsing with `fopen()`, `fgetcsv()`, and line-by-line iteration
- **After:** SQLite database with PDO connections and indexed queries
- **Key Components:**
  - Separate SQLite database file (`IPs.sqlite`) for specialized data
  - Composite indexing on range columns (`Starting_IP`, `Ending_IP`)
  - String-based comparison for IP range matching
  - Proper resource management with PDO connections

**Benefits Achieved:**
- Significant performance improvement for frequent lookups
- Better scalability for large datasets
- Automatic query optimization through database engine
- Reduced memory footprint compared to file parsing

**Usage Guidelines:**
- Apply this pattern when dealing with large, frequently-accessed datasets
- Ensure proper indexing strategy based on query patterns
- Maintain backward compatibility in function signatures
- Test thoroughly with real-world data volumes

**Implementation Location:** `get_country_from_ip()` function in `includes/functions.php`

## Architecture Overview
The system follows a simplified MVC-like pattern:
*   **Models/Business Logic**: PHP files in `/includes/` directory (e.g., `product_functions.php`, `order_functions.php`, `coupon_functions.php`, `digital_product_functions.php`, `digital_files_functions.php`, `maintenance_functions.php`, `session.php`). These handle database interactions and core business rules.
*   **Views/Templates**: PHP files in `/templates/` directory, separated into `backend/` (admin) and `frontend/` (customer-facing). Partials are in `templates/backend/partials/`.
*   **Controllers/Request Handlers**: Main PHP files like `admin.php` (admin backend), `index.php` (frontend router), and specific handlers like `/includes/ajax_handler.php` and `/includes/admin_ajax_handler.php`.

## Folder Structure Patterns
*   `/includes/`: Core PHP functions, handlers, database connection (`db.php`), security (`security.php`), session management (`session.php`), maintenance (`maintenance_functions.php`), banner functions (`banner_functions.php`).
    *   `/includes/db_migrations/`: Scripts for database schema changes.
*   `/templates/backend/`: Admin interface components and views (e.g., `maintenance_simple.php`, `sessions_list.php`).
*   `/templates/frontend/`: Customer-facing views.
*   `/public/assets/`: CSS (`/css/`), JavaScript (`/js/`), Images (`/images/`).
    *   `/public/assets/images/banners/`: Banner image uploads directory.
*   `/logs/`: Error logs.
*   Database file (`dbjcs2112ew.sqlite`) is located outside the web root for security.
*   Digital product files are stored in a secure directory outside the web root (configurable, e.g., `../digital_products`).
*   Standalone maintenance scripts (e.g., `simple_cleanup.php`) for file system operations.

## Request Flow
1.  **Admin Requests**:
    *   `admin.php` acts as the main entry point, routing based on `section` and `action` parameters.
    *   Renders templates from `/templates/backend/`.
    *   AJAX requests (primarily for sidebar navigation and some specific actions) are handled by logic within `admin.php` (for GET) or by `/includes/admin_ajax_handler.php` (for POST).
    *   List views (pages, blog posts, products, coupons, licenses, **sessions**) use GET parameters for filtering and pagination, maintaining state across navigation. The `prepare_admin_view_data` function in `admin.php` often preloads data for these views.
    *   Maintenance actions (token cleanup, session deletion) are typically POST requests to `admin.php` with `section=maintenance` or `section=sessions` and a specific `action`.
2.  **Customer Requests**:
    *   `index.php` typically handles frontend routing to appropriate templates in `/templates/frontend/`.
    *   Blog posts of type 'CODE' are rendered by `templates/frontend/blog_post.php`, which uses `eval()` to execute stored code. This presents a significant security risk.
    *   AJAX requests handled by `/includes/ajax_handler.php` (e.g., coupon application).

## Security Patterns
1.  **Input Handling**: All user inputs are validated and sanitized in backend handler files or before database interaction. Security functions often reside in `/includes/security.php`.
2.  **Cookieless Session Management**: Implemented in `/includes/session.php` and utilized database-backed mechanisms (e.g., `sessions` table with IP tracking, `order_visits` table for specific tracking). No browser cookies are used. Key functions: `start_cookieless_session()`, `read_session()`, `write_session()` (captures user IP), `destroy_session()`, `get_all_sessions_data()` (includes IP data), `delete_session_and_associated_tokens()`. IP addresses are captured using `get_customer_ip()` for security monitoring and geographic analysis.
3.  **CSRF Protection**: `CSRF_SECRET` from `config.php` is used. Tokens are implemented for form submissions and validated in `admin.php` and AJAX handlers.
4.  **Data Encryption**: Sensitive data like user email/name in the license section is encrypted using `CSRF_SECRET`.
5.  **Secure File Storage**: Digital product files and database file are stored outside the web root.
6.  **Prepared Statements**: Used for all database queries to prevent SQL injection.
7.  **Confirmation Dialogs**: JavaScript confirmations are used before permanent database modifications (e.g., deletions, maintenance tasks, session deletion).
8.  **Code Execution from DB**: Blog posts of type 'CODE' execute stored content via `eval()`. This is a high-risk pattern and requires extreme caution with content input and server security.

## Database Pattern
1.  **SQLite Implementation**: Single database file (e.g., `../dbjcs2112ew.sqlite`).
2.  **Query Pattern**: Prepared statements are used exclusively, typically via functions in `db.php` or specific model files.
3.  **Database Schema (Key Tables - additions/updates noted)**:
    *   `users`: Admin user accounts.
    *   `products`: Product catalog (includes `product_type` for 'regular', 'variation', 'digital').
    *   `product_variations`: Variations of products.
    *   `product_images`, `product_videos`.
    *   `categories`: Product categories.
    *   `orders`: Customer orders (includes `has_digital_products` flag, `coupon_code`, `discount_amount`).
    *   `order_items`: Items within orders (includes `stock_reduced`, `stock_restored` flags).
    *   `sessions`: Cookieless session management (includes `user_ip TEXT` column for IP tracking and geographic analysis).
    *   `digital_products`: Links `products` to `digital_files` and stores download settings.
        ```sql
        CREATE TABLE digital_products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL UNIQUE, -- FK to products.id
            digital_file_id INTEGER,          -- FK to digital_files.id
            expiry_days INTEGER DEFAULT 5,
            download_limit INTEGER DEFAULT 3,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE SET NULL
        );
        ```
    *   `digital_files`: Stores information about individual digital files.
        ```sql
        CREATE TABLE digital_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_filename TEXT NOT NULL,
            display_name TEXT, -- User-friendly name for admin display
            file_path TEXT NOT NULL, -- Actual path on server
            file_size INTEGER NOT NULL,
            file_type TEXT,
            description TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );
        ```
    *   `digital_product_file_types`, `digital_product_file_type_associations`.
    *   `licenses`: Customer licenses for digital products (tracks status, expiry, download counts).
    *   `license_files`: Associates licenses with digital products (many-to-many between licenses and digital_products).
    *   `downloads`: Tracks download history.
    *   `pages`, `page_categories`, `page_placeholders`, `placeholder_links`.
    *   `custom_fields`, `custom_field_values`.
    *   `blog_posts`: Stores blog articles, links, and now executable code.
        ```sql
        CREATE TABLE blog_posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            post_type TEXT NOT NULL CHECK(post_type IN ('article', 'link', 'CODE')), -- Added 'CODE'
            content TEXT, -- Full content for 'article'
            link_url TEXT, -- URL for 'link' type
            link_description TEXT, -- Short description for 'link' type
            code_content TEXT, -- Raw code for 'CODE' type
            image_path TEXT NOT NULL,
            is_published INTEGER NOT NULL DEFAULT 0,
            published_at TEXT,
            seo_title TEXT,
            seo_description TEXT,
            seo_keywords TEXT,
            og_title TEXT,
            og_description TEXT,
            og_image TEXT,
            twitter_card TEXT,
            twitter_title TEXT,
            twitter_description TEXT,
            twitter_image TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            image_description TEXT
        );
        ```
    *   `blog_categories`.
    *   `contacts`, `message_replies`.
    *   `settings`: System configuration.
    *   `sessions`: Cookieless session storage. Includes `session_id`, `data`, `user_fingerprint`, `created_at`, `last_access`, `expires_at`.
    *   `order_visits`: Tracks revisits to order pages (cookieless).
    *   `coupons`: Coupon details (code, type, value, limits, validity).
        ```sql
        CREATE TABLE coupons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT NOT NULL UNIQUE,
            description TEXT,
            discount_type TEXT NOT NULL, -- 'percentage' or 'fixed'
            discount_value REAL NOT NULL,
            min_order_value REAL DEFAULT 0.0,
            usage_limit INTEGER DEFAULT NULL, -- NULL for unlimited
            usage_count INTEGER NOT NULL DEFAULT 0,
            is_active INTEGER NOT NULL DEFAULT 1,
            start_date TEXT,
            end_date TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );
        ```
    *   `sitemap_configs`: Sitemap generation settings.
    *   `download_tokens`: Stores download tokens, linked to licenses and optionally `session_id`.
        ```sql
        CREATE TABLE download_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            security_token TEXT NOT NULL,
            email_token TEXT NOT NULL UNIQUE,
            is_verified INTEGER NOT NULL DEFAULT 0,
            attempts INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            expires_at TEXT NOT NULL, session_id TEXT, is_used TEXT,
            FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
        );
        ```
    *   `order_access_tokens`: Stores tokens for accessing order details.
        ```sql
        CREATE TABLE order_access_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            access_token TEXT NOT NULL,
            created_at TEXT NOT NULL,
            expires_at TEXT NOT NULL,
            UNIQUE(order_id, access_token)
        );
        ```

## Payment Integration
*   Base implementation likely in `/includes/payment_methods.php`.
*   Specific gateways in `/includes/`.
*   Checkout flow (`templates/frontend/checkout.php`) incorporates payment processing.
*   Minimum order value and free shipping thresholds validated and applied during cart calculation.

## Product System (Physical & Digital)
1.  **Structure**: Supports 'regular' (simple), 'variation', and 'digital' product types.
    *   Variations defined by attributes (size, color), each with own SKU, stock (if physical), price.
    *   Multiple images and videos (uploaded or external URLs like YouTube) per product.
    *   Custom fields for personalization.
    *   SEO and social media metadata.
2.  **Digital Products Specifics**:
    *   `products.product_type` is 'digital'.
    *   Linked to a `digital_products` record which in turn links to a `digital_files` record (containing the actual file path and user-friendly `display_name`).
    *   Automatic stock settings (effectively unlimited), hidden stock values.
    *   Limit of 1 per cart (user preference).
    *   No product options requirement.
    *   Associated with `licenses` table.
    *   Secure download mechanism (see Digital Products System).
    *   Admin UI shows `display_name` for files, not necessarily `original_filename`.
3.  **Stock Management (Non-Digital)**: Stock reduced on order completion, can be restored for cancelled/refunded orders. Tracked via `stock_reduced`, `stock_restored` flags in `order_items`.
4.  **Media Management**: Product images/videos stored in `/public/assets/`. Thumbnails generated. Automatic deletion of media with product/order deletion.

## Coupon System
*   **Admin Management**: Full CRUD in admin panel (`templates/backend/coupons_list.php`, `coupon_form.php`).
*   **Types**: Percentage and fixed amount.
*   **Features**: Usage limits, min order value, validity period.
*   **Frontend**: Input on cart page (`templates/frontend/cart.php`). AJAX application (`includes/ajax_handler.php` action `apply_promo_code`) with subsequent page refresh as per preference. Discount descriptions italicized in parentheses.
*   **Order Integration**: `coupon_code` and `discount_amount` stored in `orders` table. Usage count incremented.
*   **Core Logic**: `/includes/coupon_functions.php` (validation, calculation, application).
*   **Session Storage**: `$_SESSION['cart_discount']`, `$_SESSION['applied_promo_code']`, `$_SESSION['applied_coupon_id']`.

## Digital Products System
1.  **File Management**:
    *   Physical files stored securely outside web root.
    *   `digital_files` table stores `original_filename`, `display_name` (user-friendly), actual `file_path`, size, type, description.
    *   `digital_products` table links a base product to a specific `digital_files` record and sets download rules (expiry, limit).
2.  **License Management**:
    *   Unique licenses generated per purchase (`xxxx-xxxx-JCS-xxxx-xxxx` format).
    *   States: `waiting_payment`, `active`, `disabled`, `canceled`.
    *   Status automatically changes with order status (e.g., 'shipped'/'completed' -> activate; 'cancelled'/'refunded' -> cancel/deactivate, remove download tokens). Email notifications sent.
    *   Admin can manage: reset download counts, edit expiration dates, delete download history, activate/deactivate licenses.
3.  **Download Security**:
    *   Three-step verification (user preference): license code, email verification (decrypting stored email for comparison), security token.
    *   Files stored outside web root. Secure download handler (`download_file.php`).
    *   Download tracking (`downloads` table), limits, IP-based restrictions, session security.
    *   Security codes invalidated after failed attempts, user notification, captcha on forms.
4.  **Checkout & Frontend**: Digital products visually identified. Shipping skipped for digital-only orders. Order success page shows download instructions/licenses. Context-aware display on order success page (initial vs. revisit with data censoring).
5.  **Encryption**: Customer email/name in license section encrypted using `CSRF_SECRET`.

## Admin Interface Patterns
1.  **List Views**: Consistent structure (products, pages, blog posts, licenses, coupons, digital_files, sessions) with:
    *   Collapsible filter panels at the top (using GET parameters, state maintained).
    *   Data tables, action buttons per item.
    *   Pagination controls.
    *   Visual indicators for active filters, "Clear Filters" button.
    *   For digital files, `display_name` is shown, with `original_filename` as secondary info.
2.  **Forms**: Direct form submissions preferred over AJAX for core CRUD, especially after issues with modals. Simple JS confirmations for destructive actions.

## Maintenance System
*   `/includes/maintenance_functions.php` and UI in `/templates/backend/maintenance_simple.php`.
*   Functions: Cleanup expired download/order access tokens, **cleanup active (non-expired) download/order access tokens**, DB optimization (VACUUM, ANALYZE), integrity checks, DB backup.
*   Uses direct form submissions and flash messages for reliability, with JS confirmation dialogs.
*   **File System Cleanup Pattern**: Standalone scripts (e.g., `simple_cleanup.php`) for file system operations that could be time-intensive or blocking. These scripts operate independently from user-facing workflows to prevent UI hanging or blank pages.

## Banner Management System
*   **File Storage**: Banner images stored in `/public/assets/images/banners/` directory.
*   **Database**: Banner metadata stored in `banners` table with image filename references.
*   **Admin Interface**: Full CRUD operations via `admin.php?section=banners` with image upload, editing, and deletion.
*   **Cleanup Strategy**: Orphaned image cleanup moved to standalone scripts to prevent UI blocking during banner operations.
*   **Functions**: Core banner operations in `/includes/banner_functions.php` including `cleanup_orphaned_banner_images()` for maintenance tasks.

## Error Handling & Logging
*   Errors logged to `/logs/error.log` (via functions in `/includes/functions.php`). PHP fatal errors also logged here.
*   User feedback via flash messages or simple on-page messages. HTML in flash messages rendered correctly.

## Sitemap and XML Generation System
*   Configurations stored in `sitemap_configs` table (type, content inclusion, output paths).
*   Generation via `/includes/sitemap_functions.php` using `DOMDocument`.
*   Automatic `robots.txt` updates. Header links `<link rel="sitemap">`.
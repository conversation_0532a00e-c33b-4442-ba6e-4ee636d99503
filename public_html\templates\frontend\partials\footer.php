<?php

$placeholders_with_pages = get_all_placeholders_with_pages();
?>
</main> <!-- End of main content area (opened in header.php) -->

<?php

require_once __DIR__ . '/../../../includes/banner_display.php';
$footer_banner = display_footer_banner('mt-12 mb-6');
if (!empty($footer_banner)) {
    echo '<div class="container mx-auto px-4 md:px-6 lg:px-8">' . $footer_banner . '</div>';
}
?>

<!-- Footer -->
<footer class="bg-gray-900 border-t border-gray-800 mt-12">
     <div class="border-t border-gray-800">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- JCS Studio Section -->
                <div class="flex flex-col items-center md:items-start text-center md:text-left">
                    <!-- Store Name -->
                    <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="text-2xl font-['Pacifico'] text-white mb-4 block">
                        <?= sanitize_input(get_setting('store_name', 'JCS Studio')) ?>
                    </a>

                    <!-- Social Icons -->
                    <div class="flex justify-center md:justify-start space-x-5 mb-4">
                        <!-- Add social links if configured in settings -->
                        <?php if ($fb_link = get_setting('social_facebook')): ?>
                        <a href="<?= sanitize_input($fb_link) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white">
                            <div class="w-7 h-7 flex items-center justify-center bg-gray-800 rounded-full">
                                <i class="ri-facebook-fill"></i>
                            </div>
                        </a>
                        <?php endif; ?>
                        <?php if ($tw_link = get_setting('social_twitter')): ?>
                        <a href="<?= sanitize_input($tw_link) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white">
                            <div class="w-7 h-7 flex items-center justify-center bg-gray-800 rounded-full">
                                <i class="ri-twitter-fill"></i>
                            </div>
                        </a>
                        <?php endif; ?>
                        <?php if ($ig_link = get_setting('social_instagram')): ?>
                        <a href="<?= sanitize_input($ig_link) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white">
                            <div class="w-7 h-7 flex items-center justify-center bg-gray-800 rounded-full">
                                <i class="ri-instagram-fill"></i>
                            </div>
                        </a>
                        <?php endif; ?>
                        <?php if ($li_link = get_setting('social_linkedin')): ?>
                        <a href="<?= sanitize_input($li_link) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white">
                            <div class="w-7 h-7 flex items-center justify-center bg-gray-800 rounded-full">
                                <i class="ri-linkedin-fill"></i>
                            </div>
                        </a>
                        <?php endif; ?>
                        <?php if ($yt_link = get_setting('social_youtube')): ?>
                        <a href="<?= sanitize_input($yt_link) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white">
                            <div class="w-7 h-7 flex items-center justify-center bg-gray-800 rounded-full">
                                <i class="ri-youtube-fill"></i>
                            </div>
                        </a>
                        <?php endif; ?>
                    </div>

                    <!-- Store Description - Smaller text with justified alignment -->
                    <p class="text-gray-400 text-sm italic text-justify"><?= sanitize_input(get_setting('store_description', 'JCS Studio é uma loja exclusivamente online dedicada à criação digital de conteúdos, alojamentos web e apoio I.T. assim como à personalização de itens a laser de fibra.')) ?></p>
                </div>

                <?php
                
                $placeholder_count = 0;

                
                foreach ($placeholders_with_pages as $placeholder) {
                    
                    if ($placeholder['slug'] === 'ficheiros-digitais') {
                        continue;
                    }

                    if (!empty($placeholder['items'])) {
                        $placeholder_count++;
                ?>
                        <!-- Dynamic Placeholder: <?= htmlspecialchars($placeholder['name']) ?> -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4"><?= htmlspecialchars($placeholder['name']) ?></h3>
                            <ul class="space-y-2">
                                <?php foreach ($placeholder['items'] as $item): ?>
                                    <li class="flex items-center">
                                        <i class="ri-arrow-right-s-line mr-2 text-gray-500"></i>
                                        <a
                                            href="<?= htmlspecialchars($item['url']) ?>"
                                            class="text-gray-400 hover:text-white"
                                            <?= $item['target'] === '_blank' ? 'target="_blank" rel="noopener noreferrer"' : '' ?>
                                        >
                                            <?php if (trim($item['title']) === 'Livro de Reclamações'): ?>
                                                <img src="<?= get_asset_url('images/livro_de_reclamacoes/logo_complaints_book.svg') ?>" alt="Livro de Reclamações" class="w-24 h-auto">
                                                <span class="block text-xs italic mt-1">(Livro de Reclamações)</span>
                                            <?php else: ?>
                                                <?= htmlspecialchars($item['title']) ?>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                <?php
                    }
                }
                ?>

                <!-- Contact column - Always show as the last column -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contacto</h3>
                    <p class="text-gray-400 mb-2 flex items-center">
                        <i class="ri-map-pin-line mr-2"></i>
                        <?= sanitize_input(get_setting('contact_address', '')) ?>
                    </p>
                    <p class="text-gray-400 mb-2 flex items-center">
                        <i class="ri-phone-line mr-2"></i>
                        <?= sanitize_input(get_setting('contact_phone', '')) ?>
                    </p>
                    <p class="text-gray-400 flex items-center">
                        <i class="ri-mail-line mr-2"></i>
                        <?= sanitize_input(get_setting('contact_email', '')) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Copyright Section -->
<div class="bg-gray-900 border-t border-gray-800">
    <div class="container mx-auto px-4 py-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Copyright -->
            <div class="text-center md:text-left">
                <p class="text-gray-400 text-sm">&copy; <?= date('Y') ?> <?= sanitize_input(get_setting('store_name', 'Minha Loja')) ?>. Todos os direitos reservados.</p>
            </div>

            <!-- Additional Info -->
            <div class="text-center md:text-right">
                <?php
                
                $footer_info_line1 = get_setting('footer_info_line1', 'Todos os produtos estão isentos de IVA ao abrigo do Regime de Isenção - Art. 53');
                $footer_info_line2 = get_setting('footer_info_line2', 'Para sua segurança e privacidade, este website não usa cookies!');
                ?>
                <?php if (!empty($footer_info_line1)): ?>
                <p class="text-gray-400 text-sm italic mb-1"><?= sanitize_input($footer_info_line1) ?></p>
                <?php endif; ?>
                <?php if (!empty($footer_info_line2)): ?>
                <p class="text-gray-400 text-sm italic"><?= sanitize_input($footer_info_line2) ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Back to Top Button -->
<button id="backToTop" class="fixed bottom-24 right-4 md:right-8 p-3 bg-primary rounded-full shadow-lg opacity-0 invisible transition-all duration-300 z-50">
    <div class="w-5 h-5 flex items-center justify-center text-white">
        <i class="ri-arrow-up-line"></i>
    </div>
</button>

<!-- Checkout Modal -->
<div id="checkoutModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[110] hidden">
    <div class="bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl border border-gray-700">
        <h3 class="text-xl font-semibold mb-4">Finalizar Compra</h3>
        <p class="text-gray-300 mb-6">Você está prestes a finalizar sua compra. Deseja continuar?</p>
        <div class="flex justify-end space-x-3">
            <button id="cancelCheckout" class="px-4 py-2 bg-gray-800 text-white rounded-button hover:bg-gray-700 whitespace-nowrap">Cancelar</button>
            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=checkout') ?>" id="confirmCheckout" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 whitespace-nowrap">Continuar</a>
        </div>
    </div>
</div>

<!-- Include Toast Notification Component -->
<?php include_once __DIR__ . '/toast.php'; ?>

<!-- Optional: Custom Frontend JS -->
<script src="<?= get_asset_url('js/main.js') ?>"></script>
<script src="<?= get_asset_url('js/custom-fields.js') ?>"></script>

<!-- Template Interaction JS (Included directly for now) -->
<script>
document.addEventListener('DOMContentLoaded', function() {

    // --- General Helper Functions ---
    // Note: showToast function is now defined in toast.php

    // --- Mobile Menu Toggle ---
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // --- Cart Preview Toggle ---
    const cartLink = document.getElementById('cartLink');
    const cartPreview = document.getElementById('cartPreview');
    const cartContainer = document.getElementById('cartContainer');

    if (cartLink && cartPreview && cartContainer) {
        // Show on hover
        cartContainer.addEventListener('mouseenter', function() {
            // Fetch updated cart preview content before showing
            fetchCartPreview();
            cartPreview.style.display = 'block';
        });

        cartContainer.addEventListener('mouseleave', function() {
            cartPreview.style.display = 'none';
        });

        // Alternative: Click to toggle
        cartLink.addEventListener('click', function(e) {
            // Only prevent default if we're showing the preview
            if (cartPreview.style.display !== 'block') {
                e.preventDefault();
                fetchCartPreview();
                cartPreview.style.display = 'block';
            }
        });
    }

    // Note: fetchCartPreview and addPreviewRemoveListeners functions have been moved to main.js

    // --- Update Cart Counter ---
    function updateCartCounter(count) {
        const cartCounter = document.getElementById('cart-item-count');
        const previewCounter = document.getElementById('cart-preview-count');
        if (cartCounter) {
            cartCounter.textContent = count;
            if (count > 0) {
                cartCounter.style.display = 'flex';
            } else {
                cartCounter.style.display = 'none';
            }
        }
        if (previewCounter) {
            previewCounter.textContent = count;
        }

        // Also update the global cart count if it exists
        if (typeof updateCartCount === 'function') {
            updateCartCount(count);
        }
    }

    // --- Add to Cart (Generic Function) ---
    // Needs product ID, quantity, and potentially variation ID
    // This function is kept for programmatic use but the event listener has been moved to main.js
    // to avoid duplicate event listeners and AJAX requests
    async function addToCart(productId, quantity, variationId = null) {

        try {
            // Helper function for AJAX requests
            const ajaxRequest = async (endpoint, options = {}) => {
                // Ensure session ID is included in the URL
                const url = `${window.eshopBaseUrl}/includes/ajax_handler.php${endpoint}`;
                const sessionParam = `${window.eshopSessionParam}=${window.eshopSessionId}`;
                const fullUrl = url + (url.includes('?') ? '&' : '?') + sessionParam;

                const response = await fetch(fullUrl, options);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return await response.json();
            };

            const response = await ajaxRequest('?action=add_to_cart', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    product_id: productId,
                    variation_id: variationId,
                    quantity: quantity
                })
            });

            if (response.success) {
                updateCartCounter(response.item_count);
                fetchCartPreview(); // Update preview content

                if (typeof showToast === 'function') {
                    showToast('Sucesso', response.message || 'Item adicionado ao carrinho!', 'ri-check-line', 'border-green-500');
                }
                return true;
            } else {
                if (typeof showToast === 'function') {
                    showToast('Erro', response.error || 'Não foi possível adicionar ao carrinho.', 'ri-error-warning-line', 'border-red-500');
                }
                return false;
            }
        } catch (error) {
            if (typeof showToast === 'function') {
                showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            }
            return false;
        }
    }

     // --- Remove From Cart (Generic Function) ---
    async function removeFromCart(productId, variationId = null, fromPreview = false) {

        try {
            // Helper function for AJAX requests
            const ajaxRequest = async (endpoint, options = {}) => {
                // Ensure session ID is included in the URL
                const url = `${window.eshopBaseUrl}/includes/ajax_handler.php${endpoint}`;
                const sessionParam = `${window.eshopSessionParam}=${window.eshopSessionId}`;
                const fullUrl = url + (url.includes('?') ? '&' : '?') + sessionParam;

                const response = await fetch(fullUrl, options);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return await response.json();
            };

            const response = await ajaxRequest('?action=remove_from_cart', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    product_id: productId,
                    variation_id: variationId
                })
            });

            if (response.success) {
                if (typeof showToast === 'function') {
                    showToast('Removido', response.message || 'Item removido do carrinho.');
                }
                updateCartCounter(response.item_count);

                if (fromPreview) {
                    fetchCartPreview(); // Refresh preview if removed from there
                } else {
                    // If on cart page, remove the item row directly
                    const cartItemRow = document.querySelector(`.cart-item-row[data-product-id="${productId}"][data-variation-id="${variationId || ''}"]`);
                    if (cartItemRow) {
                        cartItemRow.remove();
                    }

                    // Update cart page totals (assuming function exists in cart.php context)
                    if (typeof updateCartTotals === 'function') {
                        updateCartTotals();
                    }

                    // Check if cart page is now empty
                    const cartItemsContainer = document.getElementById('cartItemsContainer');
                    const emptyCartMessage = document.getElementById('emptyCartMessage');
                    const orderSummary = document.getElementById('orderSummary');

                    if (cartItemsContainer && emptyCartMessage) {
                        if (document.querySelectorAll('#cartItemsContainer .cart-item-row').length === 0) {
                            cartItemsContainer.classList.add('hidden');
                            emptyCartMessage.classList.remove('hidden');

                            // Hide summary/checkout button if needed
                            if (orderSummary) {
                                orderSummary.classList.add('hidden');
                            }
                        }
                    }
                }
                return true;
            } else {
                if (typeof showToast === 'function') {
                    showToast('Erro', response.error || 'Não foi possível remover o item.', 'ri-error-warning-line', 'border-red-500');
                }
                return false;
            }
        } catch (error) {
            if (typeof showToast === 'function') {
                showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            }
            return false;
        }
    }

    // --- Handle checkout button clicks ---
    // Note: Add to cart functionality is now handled in main.js to avoid duplicate event listeners
    document.body.addEventListener('click', function(e) {
        // Handle checkout button clicks
        const checkoutBtn = e.target.closest('a[href*="page=checkout"]');
        if (checkoutBtn && !checkoutBtn.id.includes('confirmCheckout')) {
            e.preventDefault();
            const checkoutModal = document.getElementById('checkoutModal');
            if (checkoutModal) {
                checkoutModal.classList.remove('hidden');
            }
        }
    });

    // --- Checkout Modal Handling ---
    const checkoutModal = document.getElementById('checkoutModal');
    const cancelCheckoutBtn = document.getElementById('cancelCheckout');

    if (cancelCheckoutBtn && checkoutModal) {
        cancelCheckoutBtn.addEventListener('click', function() {
            checkoutModal.classList.add('hidden');
        });
    }

    // --- Back to Top Button ---
    const backToTopButton = document.getElementById('backToTop');
    if (backToTopButton) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.add('opacity-0', 'invisible');
                backToTopButton.classList.remove('opacity-100', 'visible');
            }
        });
        backToTopButton.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // --- Contact Form Toggle (If using the template's contact section) ---
    const contactToggle = document.getElementById('contactToggle'); // Assuming this ID exists if contact section is used
    const contactContent = document.getElementById('contactContent');
    const toggleIcon = document.getElementById('toggleIcon');
    let isContactOpen = false;
    if (contactToggle && contactContent && toggleIcon) {
        contactToggle.addEventListener('click', function() {
            isContactOpen = !isContactOpen;
            if (isContactOpen) {
                contactContent.style.maxHeight = contactContent.scrollHeight + "px";
                toggleIcon.style.transform = 'rotate(180deg)';
            } else {
                contactContent.style.maxHeight = "0";
                toggleIcon.style.transform = 'rotate(0)';
            }
        });
    }

    // --- Reset Cart Function ---
    // This function can be called to reset the cart if there are issues
    async function resetCart() {
        try {
            const response = await fetch(`${window.eshopBaseUrl}/includes/ajax_handler.php?action=clear_cart&${window.eshopSessionParam}=${window.eshopSessionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Update UI
                const cartCounter = document.getElementById('cart-item-count');
                const previewCounter = document.getElementById('cart-preview-count');
                const previewSubtotal = document.getElementById('cart-preview-subtotal');
                const previewItemsContainer = document.getElementById('cart-preview-items');

                if (cartCounter) cartCounter.textContent = '0';
                if (previewCounter) previewCounter.textContent = '0';
                if (previewSubtotal) previewSubtotal.textContent = data.subtotal_formatted || '€0,00';
                if (previewItemsContainer) {
                    previewItemsContainer.innerHTML = '<p class="text-sm text-gray-400 text-center py-4">Seu carrinho está vazio.</p>';
                }

                // Show success message
                if (typeof showToast === 'function') {
                    showToast('Sucesso', 'Carrinho limpo com sucesso.');
                }

                return true;
            } else {
                return false;
            }
        } catch (error) {
            return false;
        }
    }

    // --- Global AJAX Request Helper ---
    // This function ensures all AJAX requests include the session ID in both URL and headers
    window.ajaxRequest = async (endpoint, options = {}) => {
        // Ensure session ID is included in the URL
        const url = `${window.eshopBaseUrl}/includes/ajax_handler.php${endpoint}`;
        const sessionParam = `${window.eshopSessionParam}=${window.eshopSessionId}`;
        const fullUrl = url + (url.includes('?') ? '&' : '?') + sessionParam;

        // Ensure headers exist in options
        if (!options.headers) {
            options.headers = {};
        }

        // Always include session ID in headers
        options.headers['X-Session-UUID'] = window.eshopSessionId;

        // Add X-Requested-With header to identify as AJAX request
        options.headers['X-Requested-With'] = 'XMLHttpRequest';

        const response = await fetch(fullUrl, options);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return await response.json();
    };

    // --- Fetch Cart Count Only ---
    // This is a lightweight alternative to fetchCartPreview that only updates the count
    async function fetchCartCount() {
        try {
            // Use the global ajaxRequest function with the dedicated cart count endpoint
            const data = await window.ajaxRequest('?action=get_cart_count', {
                headers: {
                    'X-Session-UUID': window.eshopSessionId
                }
            });

            // Update cart count badge
            const cartCounter = document.getElementById('cart-item-count');
            if (cartCounter) {
                const count = data.item_count || 0;
                cartCounter.textContent = count;
                cartCounter.style.display = count > 0 ? 'flex' : 'none';

                // Also update the preview count if it exists
                const previewCounter = document.getElementById('cart-preview-count');
                if (previewCounter) {
                    previewCounter.textContent = count;
                }
            }

            return data.item_count || 0;
        } catch (error) {
            return 0;
        }
    }

    // Make fetchCartCount available globally
    window.fetchCartCount = fetchCartCount;

    // Note: fetchCartPreview is now defined and exposed globally in main.js

    // --- Initial Cart Preview Fetch ---
    // Fetch preview on load to ensure it's up-to-date if user navigates back/forth
    if (typeof window.fetchCartPreview === 'function') {
        window.fetchCartPreview();
    }

    // Fallback: If preview fails, at least try to get the count
    setTimeout(() => {
        const cartCounter = document.getElementById('cart-item-count');
        if (cartCounter && cartCounter.textContent === '0' && cartCounter.style.display === 'none') {
            fetchCartCount();
        }
    }, 2000);

});
</script>

</body>
</html>
<?php

if (ob_get_level() > 0) {
    ob_end_flush();
}
?>

<?php

function migrate_remove_digital_product_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_product_file_type_associations';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            return true;
        }

        
        $pdo->beginTransaction();

        
        $sql = "INSERT OR IGNORE INTO digital_file_type_associations (digital_file_id, file_type_id, created_at)
                SELECT dp.digital_file_id, dpfta.file_type_id, dpfta.created_at
                FROM digital_product_file_type_associations dpfta
                JOIN digital_products dp ON dpfta.digital_product_id = dp.id
                WHERE EXISTS (SELECT 1 FROM digital_products WHERE id = dpfta.digital_product_id)
                AND EXISTS (SELECT 1 FROM digital_files WHERE id = dp.digital_file_id)";
        
        $pdo->exec($sql);
        
        
        $pdo->exec("DROP TABLE IF EXISTS digital_product_file_type_associations;");

        
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_product_id;");
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_type_id;");

        
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => 'remove_digital_product_file_type_associations']);

        
        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

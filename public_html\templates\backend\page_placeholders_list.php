<?php

?>

<h1>Gerir Placeholders de Páginas</h1>
<p class="text-muted">Os placeholders permitem agrupar páginas em seções no rodapé do site.</p>

<a href="admin.php?section=page_placeholders&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Criar Novo Placeholder
</a>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Lista de Placeholders</h5>
        <?php if (!empty($placeholders)): ?>
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome</th>
                        <th>Slug</th>
                        <th>Páginas</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($placeholders as $placeholder): ?>
                        <tr>
                            <td><?= htmlspecialchars($placeholder['id']) ?></td>
                            <td><?= htmlspecialchars($placeholder['name']) ?></td>
                            <td><?= htmlspecialchars($placeholder['slug']) ?></td>
                            <td>
                                <?php
                                
                                $placeholder_pages = get_pages_by_placeholder_id($placeholder['id']);
                                echo count($placeholder_pages);
                                ?>
                            </td>
                            <td>
                                <a href="admin.php?section=page_placeholders&action=edit&id=<?= $placeholder['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-primary me-1" title="Editar">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <a href="admin.php?section=page_placeholders&action=delete&id=<?= $placeholder['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-danger" title="Eliminar" onclick="return confirm('Tem a certeza que deseja eliminar este placeholder? As páginas associadas não serão eliminadas, apenas desassociadas.');">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>Não existem placeholders criados.</p>
        <?php endif; ?>
    </div>
</div>

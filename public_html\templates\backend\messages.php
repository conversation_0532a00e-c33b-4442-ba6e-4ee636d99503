<?php

function get_message_status_badge(string $status): string {
    return match ($status) {
        'new' => '<span class="badge bg-info">Nova</span>',
        'read' => '<span class="badge bg-secondary">Lida</span>',
        'replied' => '<span class="badge bg-success">Respondida</span>',
        'archived' => '<span class="badge bg-warning">Arquivada</span>',
        default => '<span class="badge bg-light text-dark">' . sanitize_input($status) . '</span>',
    };
}

?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Histórico de Mensagens</h1>
</div>

<?php display_flash_messages(); ?>

<div class="accordion" id="messageHistoryAccordion">
    <?php if (empty($message_history)): ?>
        <div class="alert alert-info">Nenhuma mensagem encontrada.</div>
    <?php else: ?>
        <?php foreach ($message_history as $message_id => $thread): ?>
            <?php
                $message = $thread['message_data'];
                $replies = $thread['replies'];
                $accordion_id = "message_" . $message_id;
                $header_id = "header_" . $message_id;
                $collapse_id = "collapse_" . $message_id;
            ?>
            <div class="accordion-item mb-3 shadow-sm border" id="<?= $accordion_id ?>">
                <h2 class="accordion-header" id="<?= $header_id ?>">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $collapse_id ?>" aria-expanded="false" aria-controls="<?= $collapse_id ?>">
                        <div class="d-flex align-items-center w-100">
                            <div class="me-3"><?= get_message_status_badge($message['status']) ?></div>
                            <div class="fw-bold me-3 text-truncate" style="max-width: 300px;"><?= sanitize_input($message['subject']) ?></div>
                            <div class="text-muted small me-auto">
                                <i class="bi bi-person"></i> <?= sanitize_input($message['name']) ?>
                                <span class="ms-2"><i class="bi bi-envelope"></i> <?= sanitize_input($message['email']) ?></span>
                                <span class="ms-2"><i class="bi bi-calendar"></i> <?= date('d/m/Y H:i', strtotime($message['created_at'])) ?></span>
                            </div>
                            <?php if ($message['replied_at']): ?>
                                <div class="text-muted small me-3">
                                    <i class="bi bi-reply"></i> <?= date('d/m/Y H:i', strtotime($message['replied_at'])) ?>
                                </div>
                            <?php endif; ?>
                            <div class="badge bg-secondary rounded-pill ms-2"><?= count($replies) ?> Resposta(s)</div>
                        </div>
                    </button>
                </h2>
                <div id="<?= $collapse_id ?>" class="accordion-collapse collapse" aria-labelledby="<?= $header_id ?>" data-bs-parent="#messageHistoryAccordion">
                    <div class="accordion-body">
                        <!-- Original Message -->
                        <div class="card mb-4 border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <div class="d-flex align-items-center">
                                    <div class="me-auto">
                                        <strong><i class="bi bi-chat-left-text"></i> Mensagem Original</strong>
                                    </div>
                                    <div class="text-muted small">
                                        <i class="bi bi-calendar"></i> <?= date('d/m/Y H:i', strtotime($message['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex flex-wrap mb-3">
                                        <div class="me-4 mb-2">
                                            <strong><i class="bi bi-person"></i> Nome:</strong> <?= sanitize_input($message['name']) ?>
                                        </div>
                                        <div class="me-4 mb-2">
                                            <strong><i class="bi bi-envelope"></i> Email:</strong> <?= sanitize_input($message['email']) ?>
                                        </div>
                                        <?php if ($message['phone']): ?>
                                        <div class="me-4 mb-2">
                                            <strong><i class="bi bi-telephone"></i> Telefone:</strong> <?= sanitize_input($message['phone']) ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if ($message['product_ref']): ?>
                                        <div class="mb-2">
                                            <strong><i class="bi bi-box"></i> Ref. Produto:</strong> <?= sanitize_input($message['product_ref']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="border-top pt-3">
                                        <p style="white-space: pre-wrap;"><?= sanitize_input($message['message']) ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Replies -->
                        <?php if (!empty($replies)): ?>
                            <div class="replies-container mb-4">
                                <h5 class="mb-3"><i class="bi bi-reply-all"></i> Respostas</h5>
                                <?php foreach ($replies as $reply): ?>
                                    <div class="card mb-3 border-0 shadow-sm">
                                        <div class="card-body">
                                            <p style="white-space: pre-wrap;"><?= sanitize_input($reply['reply_body']) ?></p>
                                            <div class="text-end text-muted small">
                                                <i class="bi bi-clock"></i> <?= date('d/m/Y H:i', strtotime($reply['sent_at'])) ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-light mb-4">
                                <i class="bi bi-info-circle"></i> Nenhuma resposta enviada ainda.
                            </div>
                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end mb-4">
                            <a href="admin.php?section=messages&action=download_message&id=<?= $message_id ?>&<?= get_session_id_param() ?>&csrf_token=<?= sanitize_input($_SESSION['csrf_token'] ?? '') ?>" class="btn btn-outline-primary me-2 download-message-btn" title="Download Histórico" onclick="this.classList.add('disabled'); this.innerHTML = '<i class=\'bi bi-hourglass-split\'></i> Gerando...';">
                                <i class="bi bi-download"></i> Download
                            </a>
                            <form method="post" action="admin.php?section=messages&action=delete&<?= get_session_id_param() ?>" class="d-inline" onsubmit="return confirm('Tem a certeza que deseja remover permanentemente esta mensagem e todas as suas respostas? Esta ação não pode ser desfeita.');">
                                <?= csrf_input_field() ?>
                                <input type="hidden" name="message_id" value="<?= $message_id ?>">
                                <button type="submit" class="btn btn-outline-danger" title="Remover Mensagem">
                                    <i class="bi bi-trash"></i> Remover
                                </button>
                            </form>
                        </div>

                        <!-- Reply Form -->
                        <div class="card border-0 shadow-sm mb-3">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="bi bi-reply"></i> Enviar Nova Resposta</h5>
                            </div>
                            <div class="card-body">
                                <form class="reply-message-form" data-message-id="<?= $message_id ?>">
                                    <?= csrf_input_field() ?>
                                    <div class="mb-3">
                                        <textarea class="form-control reply-body-textarea" id="reply_message_body_<?= $message_id ?>" name="reply_message" rows="4" placeholder="Digite sua resposta aqui..." required></textarea>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-send"></i> Enviar Resposta
                                        </button>
                                    </div>
                                    <div class="reply-status mt-3" style="display: none;"></div>
                                </form>
                            </div>
                        </div>

                    </div> <!-- /.accordion-body -->
                </div> <!-- /.accordion-collapse -->
            </div> <!-- /.accordion-item -->
        <?php endforeach; ?>
    <?php endif; ?>
</div> <!-- /#messageHistoryAccordion -->

<!-- Add JS for handling AJAX reply submission -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- Reply Message Logic ---
    document.querySelectorAll('.reply-message-form').forEach(form => {
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            const formElement = this;
            const messageId = formElement.dataset.messageId;
            const replyBody = formElement.querySelector('.reply-body-textarea').value;
            const csrfTokenInput = formElement.querySelector('input[name="csrf_token"]');
            const statusDiv = formElement.querySelector('.reply-status');
            const submitButton = formElement.querySelector('button[type="submit"]');

            if (!csrfTokenInput) {
                 statusDiv.textContent = 'Erro: Token de segurança não encontrado.';
                 statusDiv.className = 'reply-status mt-2 alert alert-danger';
                 statusDiv.style.display = 'block';
                 return;
            }
            const csrfToken = csrfTokenInput.value;

            statusDiv.style.display = 'none';
            statusDiv.textContent = '';
            statusDiv.className = 'reply-status mt-2';
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Enviando...';

            const formData = new FormData();
            formData.append('action', 'reply_message');
            formData.append('message_id', messageId);
            formData.append('reply_message', replyBody);
            formData.append('csrf_token', csrfToken);

            fetch('admin.php?section=messages&action=reply_message&<?= get_session_id_param() ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                 statusDiv.style.display = 'block';
                if (data.success) {
                    statusDiv.classList.add('alert', 'alert-success');
                    statusDiv.textContent = data.message || 'Resposta enviada com sucesso!';
                    formElement.querySelector('.reply-body-textarea').value = '';
                    // Consider reloading or adding reply dynamically
                    // setTimeout(() => { location.reload(); }, 1500); // Optional reload
                } else {
                    statusDiv.classList.add('alert', 'alert-danger');
                    statusDiv.textContent = 'Erro: ' + (data.message || 'Falha ao enviar resposta.');
                }
                 if (data.warning) {
                     const warningDiv = document.createElement('div');
                     warningDiv.className = 'alert alert-warning mt-2';
                     warningDiv.textContent = data.warning;
                     statusDiv.parentNode.insertBefore(warningDiv, statusDiv.nextSibling);
                 }
            })
            .catch(error => {
                statusDiv.style.display = 'block';
                statusDiv.classList.add('alert', 'alert-danger');
                statusDiv.textContent = 'Erro de comunicação ao enviar resposta.';
            })
            .finally(() => {
                 submitButton.disabled = false;
                 submitButton.innerHTML = 'Enviar Resposta';
            });
        });
    });

    // Delete functionality now uses a simple form submission with a JavaScript confirmation dialog
});
</script>


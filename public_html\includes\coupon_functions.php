<?php

function check_and_create_coupons_table(PDO $pdo): void
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='coupons';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            
            $pdo->exec("CREATE TABLE coupons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT NOT NULL UNIQUE,
                description TEXT,
                discount_type TEXT NOT NULL, -- 'percentage' or 'fixed'
                discount_value REAL NOT NULL,
                min_order_value REAL DEFAULT 0.0,
                usage_limit INTEGER DEFAULT NULL, -- NULL for unlimited
                usage_count INTEGER NOT NULL DEFAULT 0,
                is_active INTEGER NOT NULL DEFAULT 1,
                start_date TEXT,
                end_date TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");

            $pdo->exec("CREATE INDEX idx_coupons_code ON coupons (code);");
            $pdo->exec("CREATE INDEX idx_coupons_active ON coupons (is_active);");
        }
    } catch (PDOException $e) {
        
    }
}

function create_coupon(array $coupon_data): int|false
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $sql = "INSERT INTO coupons (
                    code,
                    description,
                    discount_type,
                    discount_value,
                    min_order_value,
                    usage_limit,
                    is_active,
                    start_date,
                    end_date
                ) VALUES (
                    :code,
                    :description,
                    :discount_type,
                    :discount_value,
                    :min_order_value,
                    :usage_limit,
                    :is_active,
                    :start_date,
                    :end_date
                )";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':code' => strtoupper($coupon_data['code']),
            ':description' => $coupon_data['description'] ?? '',
            ':discount_type' => $coupon_data['discount_type'],
            ':discount_value' => $coupon_data['discount_value'],
            ':min_order_value' => $coupon_data['min_order_value'] ?? 0.0,
            ':usage_limit' => $coupon_data['usage_limit'] !== '' ? $coupon_data['usage_limit'] : null,
            ':is_active' => $coupon_data['is_active'] ?? 1,
            ':start_date' => $coupon_data['start_date'] ?? null,
            ':end_date' => $coupon_data['end_date'] ?? null
        ]);

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return false;
    }
}

function update_coupon(int $coupon_id, array $coupon_data): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $sql = "UPDATE coupons SET
                    code = :code,
                    description = :description,
                    discount_type = :discount_type,
                    discount_value = :discount_value,
                    min_order_value = :min_order_value,
                    usage_limit = :usage_limit,
                    is_active = :is_active,
                    start_date = :start_date,
                    end_date = :end_date,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            ':id' => $coupon_id,
            ':code' => strtoupper($coupon_data['code']),
            ':description' => $coupon_data['description'] ?? '',
            ':discount_type' => $coupon_data['discount_type'],
            ':discount_value' => $coupon_data['discount_value'],
            ':min_order_value' => $coupon_data['min_order_value'] ?? 0.0,
            ':usage_limit' => $coupon_data['usage_limit'] !== '' ? $coupon_data['usage_limit'] : null,
            ':is_active' => $coupon_data['is_active'] ?? 1,
            ':start_date' => $coupon_data['start_date'] ?? null,
            ':end_date' => $coupon_data['end_date'] ?? null
        ]);

        return $result !== false;
    } catch (PDOException $e) {
        return false;
    }
}

function get_coupon_by_id(int $coupon_id): array|false
{
    return db_query("SELECT * FROM coupons WHERE id = ?", [$coupon_id], true);
}

function get_coupon_by_code(string $code): array|false
{
    return db_query("SELECT * FROM coupons WHERE code = ?", [strtoupper($code)], true);
}

function get_all_coupons(bool $active_only = false): array
{
    $sql = "SELECT * FROM coupons";
    $params = [];

    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }

    $sql .= " ORDER BY created_at DESC";

    $result = db_query($sql, $params, false, true);
    return is_array($result) ? $result : [];
}

function delete_coupon(int $coupon_id): bool
{
    return db_query("DELETE FROM coupons WHERE id = ?", [$coupon_id]) !== false;
}

function validate_coupon(string $code, float $cart_total = 0.0): array
{
    $coupon = get_coupon_by_code($code);

    if (!$coupon) {
        return [
            'valid' => false,
            'message' => 'Código promocional inválido.',
            'coupon' => null
        ];
    }

    
    if (!$coupon['is_active']) {
        return [
            'valid' => false,
            'message' => 'Este código promocional não está ativo.',
            'coupon' => null
        ];
    }

    
    if (!empty($coupon['start_date']) && strtotime($coupon['start_date']) > time()) {
        return [
            'valid' => false,
            'message' => 'Este código promocional ainda não está válido.',
            'coupon' => null
        ];
    }

    
    if (!empty($coupon['end_date']) && strtotime($coupon['end_date']) < time()) {
        return [
            'valid' => false,
            'message' => 'Este código promocional expirou.',
            'coupon' => null
        ];
    }

    
    if ($coupon['usage_limit'] !== null && $coupon['usage_count'] >= $coupon['usage_limit']) {
        return [
            'valid' => false,
            'message' => 'Este código promocional atingiu o limite de uso.',
            'coupon' => null
        ];
    }

    
    if ($coupon['min_order_value'] > 0 && $cart_total < $coupon['min_order_value']) {
        return [
            'valid' => false,
            'message' => 'O valor mínimo de encomenda para este código é ' . format_price($coupon['min_order_value']) . '.',
            'coupon' => null
        ];
    }

    return [
        'valid' => true,
        'message' => 'Código promocional válido.',
        'coupon' => $coupon
    ];
}

function calculate_coupon_discount(array $coupon, float $cart_subtotal): float
{
    if ($coupon['discount_type'] === 'percentage') {
        
        $discount = $cart_subtotal * ($coupon['discount_value'] / 100);
    } else {
        
        $discount = $coupon['discount_value'];

        
        if ($discount > $cart_subtotal) {
            $discount = $cart_subtotal;
        }
    }

    return $discount;
}

function apply_promo_code(string $code): array
{
    
    if (empty($code)) {
        unset($_SESSION['cart_discount']);
        unset($_SESSION['applied_promo_code']);
        unset($_SESSION['applied_coupon_id']);
        unset($_SESSION['applied_coupon_description']);
        return [
            'success' => true,
            'message' => 'Código promocional removido.',
            'discount_amount' => 0
        ];
    }

    
    $cart_totals = calculate_cart_totals();
    $cart_subtotal = $cart_totals['subtotal'];

    
    $validation = validate_coupon($code, $cart_subtotal);

    if (!$validation['valid']) {
        return [
            'success' => false,
            'error' => $validation['message'],
            'discount_amount' => $_SESSION['cart_discount'] ?? 0
        ];
    }

    $coupon = $validation['coupon'];
    $discount_amount = calculate_coupon_discount($coupon, $cart_subtotal);

    
    $_SESSION['cart_discount'] = $discount_amount;
    $_SESSION['applied_promo_code'] = $code;
    $_SESSION['applied_coupon_id'] = $coupon['id'];
    $_SESSION['applied_coupon_description'] = $coupon['description'] ?? '';

    
    if ($coupon['discount_type'] === 'percentage') {
        $message = 'Desconto de ' . number_format($coupon['discount_value'], 0) . '% aplicado.';
    } else {
        $message = 'Desconto de ' . format_price($discount_amount) . ' aplicado.';
    }

    return [
        'success' => true,
        'message' => $message,
        'discount_amount' => $discount_amount
    ];
}

function increment_coupon_usage(int $coupon_id): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $sql = "UPDATE coupons SET
                    usage_count = usage_count + 1,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([':id' => $coupon_id]);

        return $result !== false;
    } catch (PDOException $e) {
        return false;
    }
}

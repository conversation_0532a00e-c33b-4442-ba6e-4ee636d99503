<?php
require_once __DIR__ . '/../../includes/banner_functions.php';
require_once __DIR__ . '/../../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        $redirect_tab = $_POST['redirect_tab'] ?? 'manage';
        header('Location: admin.php?section=banners&tab=' . $redirect_tab . '&' . get_session_id_param());
        exit;
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        $link_type = $_POST['link_type'] ?? 'none';
        $banner_type = $_POST['banner_type'] ?? 'wide';
        
        $link_value_to_save = '';
        if ($link_type === 'internal') {
            $link_value_to_save = $_POST['internal_product_slug'] ?? '';
        } elseif ($link_type === 'external') {
            $link_value_to_save = $_POST['external_url_value'] ?? '';
        }

        
        $banner_type_mapping = [
            'wide' => 'homepage_wide',
            'content' => 'homepage_content',
            'small' => 'product_small',
            'footer' => 'footer'
        ];
        
        $data = [
            'title' => $_POST['title'] ?? '',
            'link_type' => $link_type,
            'link_value' => $link_value_to_save,
            'link_target' => $_POST['link_target'] ?? '_self',
            'banner_type' => $banner_type_mapping[$banner_type] ?? 'homepage_wide',
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'display_order' => (int)($_POST['display_order'] ?? 0)
        ];
        
        
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = handle_banner_image_upload($_FILES['image']);
            if ($upload_result['success']) {
                $data['image_filename'] = $upload_result['filename'];
                $banner_id = create_banner($data);
                if ($banner_id) {
                    add_flash_message('Banner criado com sucesso!', 'success');
                    header('Location: admin.php?section=banners&tab=list&' . get_session_id_param());
                    exit;
                } else {
                    $error_message = "Erro ao criar banner.";
                }
            } else {
                $error_message = $upload_result['error'];
            }
        } else {
            $error_message = "Por favor, selecione uma imagem.";
        }
    } elseif ($action === 'update') {
        $id = (int)($_POST['id'] ?? 0);
        $link_type = $_POST['link_type'] ?? 'none';
        $banner_type = $_POST['banner_type'] ?? 'wide';
        
        $link_value_to_save = '';
        if ($link_type === 'internal') {
            $link_value_to_save = $_POST['internal_product_slug'] ?? '';
        } elseif ($link_type === 'external') {
            $link_value_to_save = $_POST['external_url_value'] ?? '';
        }

        
        $banner_type_mapping = [
            'wide' => 'homepage_wide',
            'content' => 'homepage_content',
            'small' => 'product_small',
            'footer' => 'footer'
        ];
        
        $data = [
            'title' => $_POST['title'] ?? '',
            'link_type' => $link_type,
            'link_value' => $link_value_to_save,
            'link_target' => $_POST['link_target'] ?? '_self',
            'banner_type' => $banner_type_mapping[$banner_type] ?? 'homepage_wide',
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'display_order' => (int)($_POST['display_order'] ?? 0)
        ];
        
        
        $existing_banner = get_banner_by_id($id);
        if ($existing_banner) {
            $data['image_filename'] = $existing_banner['image_filename'];
            
            
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = handle_banner_image_upload($_FILES['image']);
                if ($upload_result['success']) {
                    
                    $old_image_path = __DIR__ . '/../../public/uploads/banners/' . $existing_banner['image_filename'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }
                    $data['image_filename'] = $upload_result['filename'];
                } else {
                    $error_message = $upload_result['error'];
                }
            }
            
            if (!isset($error_message)) {
                if (update_banner($id, $data)) {
                    add_flash_message('Banner atualizado com sucesso!', 'success');
                    $redirect_tab = $_POST['redirect_tab'] ?? 'list';
                    header('Location: admin.php?section=banners&tab=' . $redirect_tab . '&' . get_session_id_param());
                    exit;
                } else {
                    $error_message = "Erro ao atualizar banner.";
                }
            }
        } else {
            $error_message = "Banner não encontrado.";
        }
    }
}

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $id = (int)($_GET['id'] ?? 0);
    
    if ($action === 'delete' && $id > 0) {
        if (delete_banner($id)) {
            $success_message = "Banner excluído com sucesso!";
        } else {
            $error_message = "Erro ao excluir banner.";
        }
    } elseif ($action === 'toggle_status' && $id > 0) {
        if (toggle_banner_status($id)) {
            $success_message = "Status do banner alterado com sucesso!";
        } else {
            $error_message = "Erro ao alterar status do banner.";
        }
    } elseif ($action === 'reset_views') {
        if (reset_all_banner_visitor_counts()) {
            $success_message = "Visualizações de todos os banners foram resetadas com sucesso!";
        } else {
            $error_message = "Erro ao resetar visualizações dos banners.";
        }
    }
}

$banners = get_banners(['order_by' => 'banner_type ASC, display_order ASC, created_at DESC']);

$products = get_products_for_banner_dropdown();

$editing_banner = null;
if (isset($_GET['edit']) && $_GET['edit'] > 0) {
    $editing_banner = get_banner_by_id((int)$_GET['edit']);
}

$active_tab = $_GET['tab'] ?? 'manage';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-3">Gerenciar Banners</h1>
            
            <!-- Flash Messages -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($success_message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($error_message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Tabs Navigation -->
            <ul class="nav nav-tabs mb-4" id="bannerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?= $active_tab === 'manage' ? 'active' : '' ?>" 
                       href="admin.php?section=banners&tab=manage&<?= get_session_id_param() ?>">
                        <i class="fas fa-plus-circle"></i>
                        Adicionar novo banner / Preview
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?= $active_tab === 'list' ? 'active' : '' ?>" 
                       href="admin.php?section=banners&tab=list&<?= get_session_id_param() ?>">
                        <i class="fas fa-list"></i>
                        Banners Existentes
                        <?php if (!empty($banners)): ?>
                            <span class="badge bg-primary ms-1"><?= count($banners) ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="bannerTabContent">
                <!-- Manage Tab (Add/Edit + Preview) -->
                <div class="tab-pane fade <?= $active_tab === 'manage' ? 'show active' : '' ?>" id="manage" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><?= $editing_banner ? 'Editar Banner' : 'Adicionar Novo Banner' ?></h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" enctype="multipart/form-data">
                                        <input type="hidden" name="action" value="<?= $editing_banner ? 'update' : 'create' ?>">
                                        <input type="hidden" name="redirect_tab" value="<?= $active_tab ?>">
                                        <?php if ($editing_banner): ?>
                                            <input type="hidden" name="id" value="<?= $editing_banner['id'] ?>">
                                        <?php endif; ?>
                                        <?= csrf_input_field() ?>
                                        
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Título *</label>
                                            <input type="text" class="form-control" id="title" name="title" 
                                                   value="<?= htmlspecialchars($editing_banner['title'] ?? '') ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="image" class="form-label">Imagem <?= $editing_banner ? '' : '*' ?></label>
                                            <input type="file" class="form-control" id="image" name="image" 
                                                   accept="image/*,video/webm" <?= $editing_banner ? '' : 'required' ?>>
                                            <?php if ($editing_banner && $editing_banner['image_filename']): ?>
                                                <small class="text-muted">Imagem atual: <?= htmlspecialchars($editing_banner['image_filename']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="banner_type" class="form-label">Tipo de Banner *</label>
                                            <select class="form-select" id="banner_type" name="banner_type" required>
                                                <option value="wide" <?= ($editing_banner['banner_type'] ?? '') === 'homepage_wide' ? 'selected' : '' ?>>
                                                    Banner Largo (Homepage Topo)
                                                </option>
                                                <option value="content" <?= ($editing_banner['banner_type'] ?? '') === 'homepage_content' ? 'selected' : '' ?>>
                                                    Banner Largo (Homepage Conteúdo)
                                                </option>
                                                <option value="small" <?= ($editing_banner['banner_type'] ?? '') === 'product_small' ? 'selected' : '' ?>>
                                                    Banner Pequeno (Páginas de Produto)
                                                </option>
                                                <option value="footer" <?= ($editing_banner['banner_type'] ?? '') === 'footer' ? 'selected' : '' ?>>
                                                    Banner Largo (Rodapé)
                                                </option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="link_type" class="form-label">Tipo de Link</label>
                                            <select class="form-select" id="link_type" name="link_type" onchange="toggleLinkFields()">
                                                <?php 
                                                $current_link_type = 'none';
                                                if ($editing_banner) {
                                                    if ($editing_banner['link_type'] === 'internal' && !empty($editing_banner['link_value'])) {
                                                        $current_link_type = 'internal';
                                                    } elseif ($editing_banner['link_type'] === 'external') {
                                                        $current_link_type = 'external';
                                                    }
                                                }
                                                ?>
                                                <option value="none" <?= $current_link_type === 'none' ? 'selected' : '' ?>>
                                                    Sem Link
                                                </option>
                                                <option value="internal" <?= $current_link_type === 'internal' ? 'selected' : '' ?>>
                                                    Link Interno (Produto)
                                                </option>
                                                <option value="external" <?= $current_link_type === 'external' ? 'selected' : '' ?>>
                                                    Link Externo
                                                </option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3" id="internal_link_field" style="display: none;">
                                            <label for="internal_product" class="form-label">Produto</label>
                                            <select class="form-select" id="internal_product" name="internal_product_slug">
                                                <option value="">Selecione um produto</option>
                                                <?php foreach ($products as $product): ?>
                                                    <option value="<?= htmlspecialchars($product['slug']) ?>" 
                                                            <?= ($editing_banner && $editing_banner['link_type'] === 'internal' && $editing_banner['link_value'] === $product['slug']) ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($product['name_pt']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3" id="external_link_field" style="display: none;">
                                            <label for="external_url" class="form-label">URL Externa</label>
                                            <input type="url" class="form-control" id="external_url" name="external_url_value" 
                                                   value="<?= ($editing_banner && $editing_banner['link_type'] === 'external') ? htmlspecialchars($editing_banner['link_value'] ?? '') : '' ?>" 
                                                   placeholder="https://exemplo.com">
                                        </div>
                                        
                                        <div class="mb-3" id="link_target_field" style="display: none;">
                                            <label for="link_target" class="form-label">Abrir Link</label>
                                            <select class="form-select" id="link_target" name="link_target">
                                                <option value="_self" <?= ($editing_banner['link_target'] ?? '') === '_self' ? 'selected' : '' ?>>
                                                    Na mesma janela
                                                </option>
                                                <option value="_blank" <?= ($editing_banner['link_target'] ?? '') === '_blank' ? 'selected' : '' ?>>
                                                    Em nova janela
                                                </option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="display_order" class="form-label">Ordem de Exibição</label>
                                            <input type="number" class="form-control" id="display_order" name="display_order" 
                                                   value="<?= htmlspecialchars($editing_banner['display_order'] ?? '0') ?>" min="0">
                                            <small class="text-muted">Menor número = maior prioridade</small>
                                        </div>
                                        
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                                   <?= ($editing_banner['is_active'] ?? 1) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_active">
                                                Banner Ativo
                                            </label>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i>
                                                <?= $editing_banner ? 'Atualizar Banner' : 'Criar Banner' ?>
                                            </button>
                                            <?php if ($editing_banner): ?>
                                                <a href="admin.php?section=banners&tab=manage&<?= get_session_id_param() ?>" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i>
                                                    Cancelar Edição
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Banner Preview -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Preview</h5>
                                </div>
                                <div class="card-body">
                                    <?php if ($editing_banner && $editing_banner['image_filename']): ?>
                                        <div class="text-center">
                                            <?php 
                                            $file_extension = strtolower(pathinfo($editing_banner['image_filename'], PATHINFO_EXTENSION));
                                            if ($file_extension === 'webm'): 
                                            ?>
                                                <video src="../public/uploads/banners/<?= htmlspecialchars($editing_banner['image_filename']) ?>" 
                                                       alt="<?= htmlspecialchars($editing_banner['title']) ?>" 
                                                       class="img-fluid" style="max-height: 200px;" controls autoplay loop muted playsinline></video>
                                            <?php else: ?>
                                                <img src="../public/uploads/banners/<?= htmlspecialchars($editing_banner['image_filename']) ?>" 
                                                     alt="<?= htmlspecialchars($editing_banner['title']) ?>" 
                                                     class="img-fluid" style="max-height: 200px;">
                                            <?php endif; ?>
                                            <p class="mt-2 text-muted"><?= htmlspecialchars($editing_banner['title']) ?></p>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center text-muted">
                                            <i class="fas fa-image fa-3x mb-3"></i>
                                            <p>Selecione uma imagem para ver o preview</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    
                <!-- List Tab (Existing Banners) -->
                <div class="tab-pane fade <?= $active_tab === 'list' ? 'show active' : '' ?>" id="list" role="tabpanel">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Banners Existentes</h5>
                            <?php if (!empty($banners)): ?>
                                <a href="admin.php?section=banners&action=reset_views&tab=list&<?= get_session_id_param() ?>" 
                                   class="btn btn-warning btn-sm" 
                                   onclick="return confirm('Tem certeza que deseja resetar as visualizações de todos os banners? Esta ação não pode ser desfeita.')" 
                                   title="Resetar Visualizações">
                                    <i class="fas fa-undo"></i>
                                    Resetar Visualizações
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <?php if (empty($banners)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-images fa-3x mb-3"></i>
                                    <p>Nenhum banner encontrado.</p>
                                    <a href="admin.php?section=banners&tab=manage&<?= get_session_id_param() ?>" class="btn btn-primary">
                                        <i class="fas fa-plus"></i>
                                        Criar Primeiro Banner
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Preview</th>
                                                <th>Título</th>
                                                <th>Tipo</th>
                                                <th>Link</th>
                                                <th>Status</th>
                                                <th>Ordem</th>
                                                <th>Visualizações</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($banners as $banner): ?>
                                                <tr>
                                                    <td>
                                                        <?php 
                                                        $file_extension_list = strtolower(pathinfo($banner['image_filename'], PATHINFO_EXTENSION));
                                                        if ($file_extension_list === 'webm'): 
                                                        ?>
                                                            <video src="../public/uploads/banners/<?= htmlspecialchars($banner['image_filename']) ?>" 
                                                                   alt="<?= htmlspecialchars($banner['title']) ?>" 
                                                                   style="width: 60px; height: 40px; object-fit: cover;" muted playsinline></video>
                                                        <?php else: ?>
                                                            <img src="../public/uploads/banners/<?= htmlspecialchars($banner['image_filename']) ?>" 
                                                                 alt="<?= htmlspecialchars($banner['title']) ?>" 
                                                                 style="width: 60px; height: 40px; object-fit: cover;">
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($banner['title']) ?></td>
                                                    <td>
                                                        <?php
                                                        $type_labels = [
                                                            'homepage_wide' => ['label' => 'Largo (Topo)', 'class' => 'primary'],
                                                            'homepage_content' => ['label' => 'Largo (Conteúdo)', 'class' => 'info'],
                                                            'product_small' => ['label' => 'Pequeno', 'class' => 'secondary'],
                                                            'footer' => ['label' => 'Largo (Rodapé)', 'class' => 'dark']
                                                        ];
                                                        $type_info = $type_labels[$banner['banner_type']] ?? ['label' => 'Desconhecido', 'class' => 'secondary'];
                                                        ?>
                                                        <span class="badge bg-<?= $type_info['class'] ?>">
                                                            <?= $type_info['label'] ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($banner['link_type'] === 'internal' && !empty($banner['link_value'])): ?>
                                                            <span class="badge bg-info">Produto: <?= htmlspecialchars($banner['link_value']) ?></span>
                                                        <?php elseif ($banner['link_type'] === 'external'): ?>
                                                            <span class="badge bg-warning">Externo</span>
                                                        <?php else: ?>
                                                            <span class="text-muted">Sem link</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?= $banner['is_active'] ? 'success' : 'danger' ?>">
                                                            <?= $banner['is_active'] ? 'Ativo' : 'Inativo' ?>
                                                        </span>
                                                    </td>
                                                    <td><?= $banner['display_order'] ?></td>
                                                    <td><?= number_format($banner['visitor_count']) ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm" role="group">
                                                            <a href="admin.php?section=banners&tab=manage&edit=<?= $banner['id'] ?>&<?= get_session_id_param() ?>" 
                                                               class="btn btn-outline-primary" title="Editar Banner">
                                                                <i class="fas fa-pencil-alt"></i>
                                                            </a>
                                                            <a href="admin.php?section=banners&action=toggle_status&id=<?= $banner['id'] ?>&tab=list&<?= get_session_id_param() ?>" 
                                                               class="btn btn-outline-<?= $banner['is_active'] ? 'warning' : 'success' ?>" 
                                                               title="<?= $banner['is_active'] ? 'Desativar Banner' : 'Ativar Banner' ?>">
                                                                <i class="fas fa-<?= $banner['is_active'] ? 'toggle-off' : 'toggle-on' ?>"></i>
                                                            </a>
                                                            <a href="admin.php?section=banners&action=delete&id=<?= $banner['id'] ?>&tab=list&<?= get_session_id_param() ?>" 
                                                               class="btn btn-outline-danger" title="Excluir Banner"
                                                               onclick="return confirm('Tem certeza que deseja excluir este banner?')">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleLinkFields() {
    const linkType = document.getElementById('link_type').value;
    const internalField = document.getElementById('internal_link_field');
    const externalField = document.getElementById('external_link_field');
    const targetField = document.getElementById('link_target_field');
    const internalSelect = document.getElementById('internal_product');
    const externalInput = document.getElementById('external_url');
    
    // Hide all fields first and clear their values
    internalField.style.display = 'none';
    externalField.style.display = 'none';
    targetField.style.display = 'none';
    
    // Clear values of hidden fields
    if (linkType !== 'internal') {
        internalSelect.value = '';
    }
    if (linkType !== 'external') {
        externalInput.value = '';
    }
    
    // Show relevant fields
    if (linkType === 'internal') {
        internalField.style.display = 'block';
        targetField.style.display = 'block';
    } else if (linkType === 'external') {
        externalField.style.display = 'block';
        targetField.style.display = 'block';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleLinkFields();
});
</script>
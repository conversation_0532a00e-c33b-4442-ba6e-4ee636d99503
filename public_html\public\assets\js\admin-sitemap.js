document.addEventListener('DOMContentLoaded', function() {
    
    const generateSitemapButtons = document.querySelectorAll('.generate-sitemap-btn, .btn-generate-sitemap-id'); 
    const generateAllSitemapsButton = document.querySelector('a[href*="action=generate_all"]'); 

    function handleSitemapGeneration(event, sitemapId = null, all = false) {
        event.preventDefault();
        const button = event.currentTarget;
        const originalButtonText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Gerando...';
        
        const sid = getAdminSessionId(); 
        
        const formData = new FormData();
        formData.append('action', all ? 'generate_all_sitemaps' : 'generate_sitemap');
        formData.append('section', 'sitemaps'); 

        if (!all) {
            if (sitemapId) {
                formData.append('id', sitemapId);
            } else {
                alert('Erro Interno: ID do sitemap não foi fornecido para uma geração individual.');
                button.disabled = false;
                button.innerHTML = originalButtonText;
                return;
            }
        }
        
        if (sid) {
            formData.append('sid', sid);
        }
        const csrfToken = getCsrfToken(); 
        if (csrfToken) {
            formData.append('csrf_token', csrfToken);
        } else {
            console.warn('[DEBUG] CSRF token NOT FOUND for sitemap generation. FormData will not include it.');
        }
        
        
        
        
        
        

        
        const fetchUrlBase = 'admin_ajax.php';

        fetch(fetchUrlBase, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(data.message || 'Sitemap gerado com sucesso!');
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    window.location.reload(); 
                }
            } else {
                alert('Erro ao gerar sitemap: ' + (data.message || 'Erro desconhecido.'));
            }
        })
        .catch(error => {
            alert('Erro de comunicação ao gerar sitemap: ' + error.message);
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalButtonText;
        });
    }

    
    document.body.addEventListener('click', function(event) {
        const target = event.target.closest('a'); 

        if (!target) {
            return; 
        }

        const href = target.getAttribute('href');
        if (!href) {
            return; 
        }

        
        if (href.includes('action=generate_all')) {
            
            if (target.matches('a[href*="action=generate_all"].btn-success')) { 
                 handleSitemapGeneration(event, null, true);
            }
        }
        
        else if (href.includes('action=generate') && href.includes('id=')) {
             
            if (target.matches('a[href*="action=generate"][href*="id="].btn-outline-success') || target.closest('a[href*="action=generate"][href*="id="].btn-outline-success')) {
                const urlParams = new URLSearchParams(href.split('?')[1]);
                const sitemapId = urlParams.get('id');
                if (sitemapId) {
                    handleSitemapGeneration(event, sitemapId, false);
                } else {
                }
            }
        }
    });

    
    const currentUrl = new URL(window.location.href);
    const section = currentUrl.searchParams.get('section');
    const action = currentUrl.searchParams.get('action');
    const sitemapIdForEdit = currentUrl.searchParams.get('id');

    if (section === 'sitemaps' && action === 'edit' && sitemapIdForEdit) {
        const sid = getAdminSessionId();
        const csrfTokenDetails = getCsrfToken();

        const formDataDetails = new FormData();
        formDataDetails.append('action', 'get_sitemap_details');
        formDataDetails.append('id', sitemapIdForEdit);
        if (sid) {
            formDataDetails.append('sid', sid);
        } 
        if (csrfTokenDetails) {
            formDataDetails.append('csrf_token', csrfTokenDetails);
        } else {
            console.warn('[DEBUG] CSRF token NOT FOUND for get_sitemap_details. FormData will not include it.');
        }
        
        
        
        
        
        
        
        const fetchUrlDetailsBase = 'admin_ajax.php';

        fetch(fetchUrlDetailsBase, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formDataDetails
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.sitemapData) {
                const sitemapData = data.sitemapData;
                
                const nameEl = document.getElementById('name');
                const typeEl = document.getElementById('type');
                const outputPathEl = document.getElementById('output_path');
                const includeProductsEl = document.getElementById('include_products');
                const includeBlogEl = document.getElementById('include_blog');
                const includePagesEl = document.getElementById('include_pages');
                const customConfigJsonEl = document.getElementById('custom_config_json');
                const isActiveEl = document.getElementById('is_active');

                if (nameEl) nameEl.value = sitemapData.name || '';
                if (typeEl) typeEl.value = sitemapData.type || 'sitemap';
                if (outputPathEl) outputPathEl.value = sitemapData.output_path || '';
                if (includeProductsEl) includeProductsEl.checked = !!parseInt(sitemapData.include_products);
                if (includeBlogEl) includeBlogEl.checked = !!parseInt(sitemapData.include_blog);
                if (includePagesEl) includePagesEl.checked = !!parseInt(sitemapData.include_pages);
                if (customConfigJsonEl) customConfigJsonEl.value = sitemapData.custom_config_json || '';
                if (isActiveEl) isActiveEl.checked = !!parseInt(sitemapData.is_active);

                
                if (typeEl) typeEl.dispatchEvent(new Event('change'));
            } else {
            }
        })
        .catch(error => {
        });
    }
    
    
    function getAdminSessionId() {
        const urlParams = new URLSearchParams(window.location.search);
        const sidParam = urlParams.get('sid');
        if (sidParam) {
            return sidParam;
        }
        if (typeof window.eshopSessionId !== 'undefined') {
            return window.eshopSessionId;
        }
        const hiddenSidInput = document.querySelector('input[name="debug_session_id"]');
        if (hiddenSidInput && hiddenSidInput.value) {
            return hiddenSidInput.value;
        }
        return null;
    }

    
    function getCsrfToken() {
        
        if (window.csrfToken) { 
            return window.csrfToken;
        } else {
            console.warn('[DEBUG] window.csrfToken is not defined or empty.'); 
        } 
        const csrfInput = document.querySelector('input[name="csrf_token"]');
        if (csrfInput && csrfInput.value) { 
            return csrfInput.value;
        } else {
            console.warn('[DEBUG] Fallback CSRF input field not found or has empty value.'); 
        } 
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta && csrfMeta.content) { 
            return csrfMeta.content;
        } else {
             console.warn('[DEBUG] Fallback CSRF meta tag not found.'); 
        } 
        return null;
    }
});
<?php

$title_filter = isset($_GET['title']) ? trim($_GET['title']) : '';
$category_filter = isset($_GET['category_id']) ? $_GET['category_id'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$location_filter = isset($_GET['location']) ? $_GET['location'] : '';

$current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
$per_page = 20; 

$filters = [];
if (!empty($title_filter)) {
    $filters['title'] = $title_filter;
}
if (!empty($category_filter)) {
    $filters['category_id'] = $category_filter;
}
if ($status_filter !== '') {
    $filters['is_active'] = $status_filter;
}
if (!empty($location_filter)) {
    $filters['location'] = $location_filter;
}

$filters['page'] = $current_page;
$filters['per_page'] = $per_page;

$total_pages = ceil(count_pages($filters) / $per_page);

$pages = get_all_pages($filters);

$categories = get_all_page_categories();
?>

<h1>Gerir Páginas</h1>
<div class="d-flex justify-content-between mb-3">
    <a href="admin.php?section=pages&action=new&<?= get_session_id_param() ?>" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Criar Nova Página
    </a>

    <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <i class="bi bi-funnel"></i> Filtros
    </button>
</div>

<div class="collapse mb-4" id="filterCollapse">
    <div class="card card-body">
        <form method="get" action="admin.php" class="row g-3">
            <input type="hidden" name="section" value="pages">
            <?php
            
            $session_param = get_session_id_param();
            if (!empty($session_param)) {
                list($name, $value) = explode('=', $session_param);
                echo '<input type="hidden" name="' . $name . '" value="' . $value . '">';
            }
            ?>

            <div class="col-md-3">
                <label for="title" class="form-label">Título</label>
                <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($title_filter) ?>">
            </div>

            <div class="col-md-3">
                <label for="category_id" class="form-label">Categoria</label>
                <select class="form-select" id="category_id" name="category_id">
                    <option value="" <?= $category_filter === '' ? 'selected' : '' ?>>Todas</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" <?= $category_filter == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="col-md-2">
                <label for="status" class="form-label">Estado</label>
                <select class="form-select" id="status" name="status">
                    <option value="" <?= $status_filter === '' ? 'selected' : '' ?>>Todos</option>
                    <option value="1" <?= $status_filter === '1' ? 'selected' : '' ?>>Ativo</option>
                    <option value="0" <?= $status_filter === '0' ? 'selected' : '' ?>>Inativo</option>
                </select>
            </div>

            <div class="col-md-2">
                <label for="location" class="form-label">Localização</label>
                <select class="form-select" id="location" name="location">
                    <option value="" <?= $location_filter === '' ? 'selected' : '' ?>>Todas</option>
                    <option value="header" <?= $location_filter === 'header' ? 'selected' : '' ?>>Cabeçalho</option>
                    <option value="footer" <?= $location_filter === 'footer' ? 'selected' : '' ?>>Rodapé</option>
                    <option value="checkout" <?= $location_filter === 'checkout' ? 'selected' : '' ?>>Checkout</option>
                    <option value="digital_checkout" <?= $location_filter === 'digital_checkout' ? 'selected' : '' ?>>Checkout Digital</option>
                </select>
            </div>

            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">Filtrar</button>
                <a href="admin.php?section=pages&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">Limpar</a>
            </div>
        </form>
    </div>
</div>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title mb-0">Lista de Páginas</h5>
            <?php if (!empty($title_filter) || $category_filter !== '' || $status_filter !== '' || $location_filter !== ''): ?>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2">Filtros ativos</span>
                    <a href="admin.php?section=pages&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Limpar filtros
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($title_filter) || $category_filter !== '' || $status_filter !== '' || $location_filter !== ''): ?>
        <script>
            // Auto-expand filter panel when filters are active
            document.addEventListener('DOMContentLoaded', function() {
                const filterCollapse = document.getElementById('filterCollapse');
                if (filterCollapse) {
                    const bsCollapse = new bootstrap.Collapse(filterCollapse, {
                        toggle: true
                    });
                }
            });
        </script>
        <?php endif; ?>

        <?php if (empty($pages)): ?>
            <div class="alert alert-info">Nenhuma página encontrada.</div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Título</th>
                            <th>Categoria</th>
                            <th>Ativa</th>
                            <th>Cabeçalho</th>
                            <th>Rodapé</th>
                            <th>Checkout</th>
                            <th>Checkout Digital</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        
                        if (!function_exists('display_checkmark')) { 
                            function display_checkmark($value) {
                                return $value ? '<i class="bi bi-check-circle-fill text-success" title="Sim"></i>' : '<i class="bi bi-x-circle-fill text-secondary" title="Não"></i>';
                            }
                        }
                        ?>
                        <?php foreach ($pages as $page): ?>
                            <tr>
                                <td><?= htmlspecialchars($page['id']) ?></td>
                                <td><?= htmlspecialchars($page['title_pt']) ?></td>
                                <td><?= htmlspecialchars($page['category_name'] ?? 'N/A') ?></td>
                                <td class="text-center"><?= display_checkmark($page['is_active']) ?></td>
                                <td class="text-center"><?= display_checkmark($page['show_in_header']) ?></td>
                                <td class="text-center"><?= display_checkmark($page['show_in_footer']) ?></td>
                                <td class="text-center"><?= display_checkmark($page['require_agreement_checkout']) ?></td>
                                <td class="text-center"><?= display_checkmark($page['require_agreement_digital_checkout']) ?></td>
                                <td>
                                    <a href="admin.php?section=pages&action=edit&id=<?= $page['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-primary me-1" title="Editar">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <?php
                                    
                                    
                                    
                                    ?>
                                    <a href="admin.php?section=pages&action=delete&id=<?= $page['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-danger" title="Eliminar" onclick="return confirm('Tem a certeza que deseja eliminar esta página?');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                    <?php ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-3">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Button -->
                        <li class="page-item <?= ($current_page <= 1) ? 'disabled' : '' ?>">
                            <a class="page-link" href="admin.php?section=pages<?=
                                (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                ($status_filter !== '' ? '&status=' . $status_filter : '') .
                                ($location_filter !== '' ? '&location=' . $location_filter : '') .
                                '&p=' . ($current_page - 1) .
                                '&' . $session_param ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>

                        <?php
                        
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        if ($start_page > 1) {
                            echo '<li class="page-item"><a class="page-link" href="admin.php?section=pages' .
                                (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                ($status_filter !== '' ? '&status=' . $status_filter : '') .
                                ($location_filter !== '' ? '&location=' . $location_filter : '') .
                                '&p=1&' . $session_param . '">1</a></li>';
                            if ($start_page > 2) {
                                echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                            }
                        }

                        for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?= ($i == $current_page) ? 'active' : '' ?>">
                                <a class="page-link" href="admin.php?section=pages<?=
                                    (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                    ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                    ($status_filter !== '' ? '&status=' . $status_filter : '') .
                                    ($location_filter !== '' ? '&location=' . $location_filter : '') .
                                    '&p=' . $i .
                                    '&' . $session_param ?>"><?= $i ?></a>
                            </li>
                        <?php endfor;

                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="admin.php?section=pages' .
                                (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                ($status_filter !== '' ? '&status=' . $status_filter : '') .
                                ($location_filter !== '' ? '&location=' . $location_filter : '') .
                                '&p=' . $total_pages . '&' . $session_param . '">' . $total_pages . '</a></li>';
                        }
                        ?>

                        <!-- Next Button -->
                        <li class="page-item <?= ($current_page >= $total_pages) ? 'disabled' : '' ?>">
                            <a class="page-link" href="admin.php?section=pages<?=
                                (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                ($status_filter !== '' ? '&status=' . $status_filter : '') .
                                ($location_filter !== '' ? '&location=' . $location_filter : '') .
                                '&p=' . ($current_page + 1) .
                                '&' . $session_param ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php

require_once __DIR__ . '/../digital_product_functions.php';

function handle_license_extension_request(array $data): array
{
    
    if (empty($data['license_id']) || !is_numeric($data['license_id'])) {
        return [
            'success' => false,
            'message' => 'ID de licença inválido.'
        ];
    }
    
    $license_id = (int)$data['license_id'];
    $days_to_add = isset($data['days_to_add']) && is_numeric($data['days_to_add']) ? (int)$data['days_to_add'] : 5;
    $reason = $data['reason'] ?? '';
    
    
    return request_license_extension($license_id, $days_to_add, $reason);
}

function handle_additional_downloads_request(array $data): array
{
    
    if (empty($data['license_id']) || !is_numeric($data['license_id'])) {
        return [
            'success' => false,
            'message' => 'ID de licença inválido.'
        ];
    }
    
    $license_id = (int)$data['license_id'];
    $additional_downloads = isset($data['additional_downloads']) && is_numeric($data['additional_downloads']) ? (int)$data['additional_downloads'] : 3;
    $reason = $data['reason'] ?? '';
    
    
    return request_additional_downloads($license_id, $additional_downloads, $reason);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = [
        'success' => false,
        'message' => 'Ação não especificada.'
    ];
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'request_extension':
            $response = handle_license_extension_request($_POST);
            break;
            
        case 'request_additional_downloads':
            $response = handle_additional_downloads_request($_POST);
            break;
    }
    
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

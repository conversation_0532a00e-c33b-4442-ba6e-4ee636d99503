<?php

function migrate_file_types_tables()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return [
            'success' => false,
            'message' => 'Erro ao conectar à base de dados.'
        ];
    }

    try {
        
        $pdo->beginTransaction();

        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_product_file_types'");
        $old_table_exists = $stmt->fetch();

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files_file_types'");
        $new_table_exists = $stmt->fetch();

        if (!$old_table_exists) {
            
            $pdo->commit();
            return [
                'success' => true,
                'message' => 'A tabela digital_product_file_types não existe, nada para migrar.'
            ];
        }

        if (!$new_table_exists) {
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS digital_files_file_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                extension TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )");
            
            
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_digital_files_file_types_name ON digital_files_file_types (name)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_digital_files_file_types_extension ON digital_files_file_types (extension)");
        }

        
        $pdo->exec("INSERT OR IGNORE INTO digital_files_file_types (id, name, extension, created_at, updated_at)
                    SELECT id, name, extension, created_at, updated_at
                    FROM digital_product_file_types");

        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_product_file_type_associations'");
        $assoc_table_exists = $stmt->fetch();

        if ($assoc_table_exists) {
            
            $pdo->exec("DROP TABLE IF EXISTS digital_product_file_type_associations");
            $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_product_id");
            $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_type_id");
        }

        
        $pdo->exec("DROP TABLE IF EXISTS digital_product_file_types");
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_types_name");
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_types_extension");

        
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => 'migrate_file_types_tables']);

        
        $pdo->commit();
        
        return [
            'success' => true,
            'message' => 'Migração das tabelas de tipos de arquivo concluída com sucesso.'
        ];
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return [
            'success' => false,
            'message' => 'Erro ao migrar tabelas de tipos de arquivo: ' . $e->getMessage()
        ];
    }
}

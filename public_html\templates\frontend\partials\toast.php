<?php

?>

<!-- Toast Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-xs w-full"></div>

<!-- Toast JavaScript -->
<script>
/**
 * Show a toast notification
 * @param {string} title - Toast title
 * @param {string} message - Toast message
 * @param {string} icon - Remix icon class (e.g., 'ri-check-line')
 * @param {string} borderColor - Tailwind border color class (e.g., 'border-green-500')
 * @param {number} duration - Duration in milliseconds (default: 3000)
 */
function showToast(title, message, icon = 'ri-information-line', borderColor = 'border-blue-500', duration = 3000) {
    const container = document.getElementById('toast-container');
    if (!container) return;
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `bg-gray-800 border-l-4 ${borderColor} rounded shadow-lg p-4 mb-3 flex items-start transform transition-all duration-300 translate-x-full opacity-0`;
    
    // Toast content
    toast.innerHTML = `
        <div class="flex-shrink-0 mr-3">
            <i class="${icon} text-xl"></i>
        </div>
        <div class="flex-grow">
            <h4 class="font-medium text-white">${title}</h4>
            <p class="text-sm text-gray-300">${message}</p>
        </div>
        <button class="ml-4 text-gray-400 hover:text-white focus:outline-none flex-shrink-0" onclick="this.parentElement.remove()">
            <i class="ri-close-line"></i>
        </button>
    `;
    
    // Add to container
    container.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 10);
    
    // Auto-remove after duration
    setTimeout(() => {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            toast.remove();
        }, 300); // Wait for transition to complete
    }, duration);
}

// Global function to update cart count
function updateCartCount(count) {
    const badge = document.getElementById('cart-count');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline-flex' : 'none';
    }
}
</script>

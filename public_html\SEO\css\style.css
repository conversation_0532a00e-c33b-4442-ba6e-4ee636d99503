/* General Styles */

/* Stats Cards */
.stats-card {
    background-color: #f8f9fa; /* Light grey background */
    border-radius: 10px; /* Rounded corners */
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none; /* Remove default card border if any */
}

.stats-card:hover {
    transform: translateY(-5px); /* Slight lift on hover */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* Enhanced shadow on hover */
}

.stats-label {
    font-size: 0.9rem;
    color: #6c757d; /* Muted text color */
    margin-bottom: 8px;
    text-transform: uppercase;
    font-weight: 500;
}

.stats-value {
    font-size: 2.2rem;
    font-weight: bold;
    color: #343a40; /* Darker color for the value */
}

.stats-value.text-success {
    color: #28a745 !important; /* Ensure Bootstrap success color overrides */
}

.stats-value.text-danger {
    color: #dc3545 !important; /* Ensure Bootstrap danger color overrides */
}

/* Ensure the row containing stats cards has some margin */
.row.mb-4 > .col-md-3 > .stats-card {
    height: 100%; /* Make cards in the same row equal height */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f2f5;
    color: #333;
    margin: 0;
    padding: 20px;
    font-size: 14px;
}

.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

.card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    padding: 10px 15px;
    font-weight: 600;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.card-body {
    padding: 15px;
}

.form-range {
    padding-left: 0;
    padding-right: 0;
}

#urlLimitValue {
    font-weight: bold;
    color: #007bff;
}

.url-limit-label {
    display: block;
    margin-bottom: .5rem;
    font-weight: 500;
}

.spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: .2em;
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}

.btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.form-control {
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.list-group-item {
    border: 1px solid rgba(0,0,0,.125);
    padding: .75rem 1.25rem;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-link {
    border: 1px solid transparent;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
    padding: .5rem 1rem;
    color: #007bff;
}

.nav-link:hover, .nav-link:focus {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #0056b3;
}

.nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content > .tab-pane {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-top: 0;
    background-color: #fff;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
}

.modal-content {
    border-radius: .3rem;
    border: 1px solid rgba(0,0,0,.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: .75rem;
}

.close {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.close:hover {
    color: #000;
    text-decoration: none;
    opacity: .75;
}

.ping-site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: .5rem .75rem;
    border-bottom: 1px solid #eee;
}

.ping-site-item:last-child {
    border-bottom: none;
}

.ping-site-actions .btn {
    margin-left: .25rem;
    padding: .25rem .5rem;
    font-size: .75rem;
}

.ping-site-name {
    font-weight: 500;
}

.ping-site-url {
    font-size: .8em;
    color: #6c757d;
    margin-left: 10px;
}

.tech-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background: linear-gradient(135deg, #6e8efb, #a777e3);
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tech-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
}

.tech-header p {
    margin: 5px 0 0;
    font-size: 1rem;
    font-weight: 300;
    opacity: 0.9;
}

.tech-circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1; /* Ensure circles are behind content */
    border-radius: 8px; /* Match parent's border-radius */
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.tech-circle.c1 { width: 60px; height: 60px; left: 10%; bottom: 10%; animation-duration: 15s; }
.tech-circle.c2 { width: 80px; height: 80px; left: 30%; bottom: 40%; animation-duration: 20s; animation-delay: 2s; }
.tech-circle.c3 { width: 40px; height: 40px; left: 50%; bottom: 60%; animation-duration: 25s; }
.tech-circle.c4 { width: 100px; height: 100px; left: 70%; bottom: 20%; animation-duration: 18s; animation-delay: 1s; }
.tech-circle.c5 { width: 50px; height: 50px; left: 90%; bottom: 50%; animation-duration: 22s; }
.tech-circle.c6 { width: 70px; height: 70px; left: 5%; bottom: 70%; animation-duration: 17s; animation-delay: 3s; }
.tech-circle.c7 { width: 90px; height: 90px; left: 25%; bottom: 5%; animation-duration: 19s; }
.tech-circle.c8 { width: 30px; height: 30px; left: 45%; bottom: 80%; animation-duration: 23s; animation-delay: 2.5s; }
.tech-circle.c9 { width: 110px; height: 110px; left: 65%; bottom: 30%; animation-duration: 16s; }
.tech-circle.c10 { width: 55px; height: 55px; left: 85%; bottom: 75%; animation-duration: 21s; animation-delay: 1.5s; }


.tech-circle.small {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.05);
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    25% { transform: translateY(-10px) translateX(5px) rotate(5deg); }
    50% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    75% { transform: translateY(10px) translateX(-5px) rotate(-5deg); }
    100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
}

.glow-effect {
    animation: glow 1.5s infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px #a777e3, 0 0 10px #a777e3, 0 0 15px #6e8efb, 0 0 20px #6e8efb;
    }
    to {
        box-shadow: 0 0 10px #a777e3, 0 0 20px #a777e3, 0 0 30px #6e8efb, 0 0 40px #6e8efb;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f2f5;
    color: #333;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

.container-fluid {
    max-width: 1400px;
    margin: auto;
    background-color: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Header */
.header-title {
    color: #1a73e8; /* Google Blue */
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5rem;
    font-weight: 600;
    letter-spacing: -0.5px;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.12);
}

.card-header {
    background-color: #e8f0fe; /* Light Google Blue */
    color: #1a73e8;
    font-weight: 500;
    padding: 15px 20px;
    border-bottom: 1px solid #d1e0ff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.card-body {
    padding: 20px;
}

/* Form Elements */
.form-label {
    font-weight: 500;
    color: #5f6368; /* Google Grey */
    margin-bottom: 8px;
}

.form-control,
.form-select {
    border-radius: 6px;
    border: 1px solid #dadce0;
    padding: 10px 15px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #1a73e8;
    box-shadow: 0 0 0 0.2rem rgba(26, 115, 232, 0.25);
}

.form-range {
    padding: 0;
}

.form-range::-webkit-slider-thumb {
    background-color: #1a73e8;
}

.form-range::-moz-range-thumb {
    background-color: #1a73e8;
}

.form-range::-ms-thumb {
    background-color: #1a73e8;
}

/* Buttons */
.btn {
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease;
    border: none;
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background-color: #1765c2;
}

.btn-secondary {
    background-color: #dadce0;
    color: #3c4043;
}

.btn-secondary:hover {
    background-color: #c8cdd1;
}

.btn-success {
    background-color: #34a853; /* Google Green */
    color: white;
}

.btn-success:hover {
    background-color: #2c8f46;
}

.btn-danger {
    background-color: #ea4335; /* Google Red */
    color: white;
}

.btn-danger:hover {
    background-color: #d0382b;
}

.btn-warning {
    background-color: #fbbc05; /* Google Yellow */
    color: #202124;
}

.btn-warning:hover {
    background-color: #e6ac04;
}

.btn-info {
    background-color: #4285f4; /* Lighter Google Blue */
    color: white;
}

.btn-info:hover {
    background-color: #357ae8;
}

.btn-light {
    background-color: #f8f9fa;
    color: #202124;
    border: 1px solid #dadce0;
}

.btn-light:hover {
    background-color: #e9ecef;
}

.btn-sm {
    padding: 8px 15px;
    font-size: 0.875rem;
}

/* Progress Bar */
.progress {
    height: 25px;
    border-radius: 6px;
    background-color: #e9ecef;
}

.progress-bar {
    background-color: #1a73e8;
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Tabs */
.nav-tabs .nav-link {
    color: #5f6368;
    border-radius: 6px 6px 0 0;
    border-color: #dadce0 #dadce0 #fff;
    padding: 12px 20px;
    font-weight: 500;
}

.spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: .2em;
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}

.btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.form-control {
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.list-group-item {
    border: 1px solid rgba(0,0,0,.125);
    padding: .75rem 1.25rem;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-link {
    border: 1px solid transparent;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
    padding: .5rem 1rem;
    color: #007bff;
}

.nav-link:hover, .nav-link:focus {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #0056b3;
}

.nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content > .tab-pane {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-top: 0;
    background-color: #fff;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
}

.modal-content {
    border-radius: .3rem;
    border: 1px solid rgba(0,0,0,.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: .75rem;
}

.close {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.close:hover {
    color: #000;
    text-decoration: none;
    opacity: .75;
}

.ping-site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: .5rem .75rem;
    border-bottom: 1px solid #eee;
}

.ping-site-item:last-child {
    border-bottom: none;
}

.ping-site-actions .btn {
    margin-left: .25rem;
    padding: .25rem .5rem;
    font-size: .75rem;
}

.ping-site-name {
    font-weight: 500;
}

.ping-site-url {
    font-size: .8em;
    color: #6c757d;
    margin-left: 10px;
}

.tech-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background: linear-gradient(135deg, #6e8efb, #a777e3);
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tech-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
}

.tech-header p {
    margin: 5px 0 0;
    font-size: 1rem;
    font-weight: 300;
    opacity: 0.9;
}

.tech-circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1; /* Ensure circles are behind content */
    border-radius: 8px; /* Match parent's border-radius */
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.tech-circle.c1 { width: 60px; height: 60px; left: 10%; bottom: 10%; animation-duration: 15s; }
.tech-circle.c2 { width: 80px; height: 80px; left: 30%; bottom: 40%; animation-duration: 20s; animation-delay: 2s; }
.tech-circle.c3 { width: 40px; height: 40px; left: 50%; bottom: 60%; animation-duration: 25s; }
.tech-circle.c4 { width: 100px; height: 100px; left: 70%; bottom: 20%; animation-duration: 18s; animation-delay: 1s; }
.tech-circle.c5 { width: 50px; height: 50px; left: 90%; bottom: 50%; animation-duration: 22s; }
.tech-circle.c6 { width: 70px; height: 70px; left: 5%; bottom: 70%; animation-duration: 17s; animation-delay: 3s; }
.tech-circle.c7 { width: 90px; height: 90px; left: 25%; bottom: 5%; animation-duration: 19s; }
.tech-circle.c8 { width: 30px; height: 30px; left: 45%; bottom: 80%; animation-duration: 23s; animation-delay: 2.5s; }
.tech-circle.c9 { width: 110px; height: 110px; left: 65%; bottom: 30%; animation-duration: 16s; }
.tech-circle.c10 { width: 55px; height: 55px; left: 85%; bottom: 75%; animation-duration: 21s; animation-delay: 1.5s; }


.tech-circle.small {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.05);
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    25% { transform: translateY(-10px) translateX(5px) rotate(5deg); }
    50% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    75% { transform: translateY(10px) translateX(-5px) rotate(-5deg); }
    100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
}

.glow-effect {
    animation: glow 1.5s infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px #a777e3, 0 0 10px #a777e3, 0 0 15px #6e8efb, 0 0 20px #6e8efb;
    }
    to {
        box-shadow: 0 0 10px #a777e3, 0 0 20px #a777e3, 0 0 30px #6e8efb, 0 0 40px #6e8efb;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}

.nav-tabs .nav-link.active {
    color: #1a73e8;
    background-color: #fff;
    border-color: #1a73e8 #1a73e8 #fff;
    border-bottom: 3px solid #1a73e8;
    margin-bottom: -2px; /* Align with tab content border */
}

.tab-content {
    border: 1px solid #dadce0;
    border-top: none;
    padding: 20px;
    background-color: #fff;
    border-radius: 0 0 10px 10px;
}

/* Log and Result Containers */
.log-container,
.results-container,
.failed-links-container,
.non-responsive-sites-container,
.processed-urls-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    background-color: #f8f9fa;
}

.log-entry,
.result-item,
.failed-link-item,
.non-responsive-site-item,
.processed-url-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
    word-break: break-all;
}

.log-entry:last-child,
.result-item:last-child,
.failed-link-item:last-child,
.non-responsive-site-item:last-child,
.processed-url-item:last-child {
    border-bottom: none;
}

.log-entry.info {
    color: #0d6efd;
}

.log-entry.success {
    color: #198754;
}

.log-entry.warning {
    color: #ffc107;
}

.log-entry.error {
    color: #dc3545;
}

/* Modals */
.modal-content {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.modal-header {
    background-color: #e8f0fe;
    color: #1a73e8;
    border-bottom: 1px solid #d1e0ff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.modal-title {
    font-weight: 500;
}

.spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: .2em;
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}

.btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.form-control {
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.list-group-item {
    border: 1px solid rgba(0,0,0,.125);
    padding: .75rem 1.25rem;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-link {
    border: 1px solid transparent;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
    padding: .5rem 1rem;
    color: #007bff;
}

.nav-link:hover, .nav-link:focus {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #0056b3;
}

.nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content > .tab-pane {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-top: 0;
    background-color: #fff;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
}

.modal-content {
    border-radius: .3rem;
    border: 1px solid rgba(0,0,0,.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: .75rem;
}

.close {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.close:hover {
    color: #000;
    text-decoration: none;
    opacity: .75;
}

.ping-site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: .5rem .75rem;
    border-bottom: 1px solid #eee;
}

.ping-site-item:last-child {
    border-bottom: none;
}

.ping-site-actions .btn {
    margin-left: .25rem;
    padding: .25rem .5rem;
    font-size: .75rem;
}

.ping-site-name {
    font-weight: 500;
}

.ping-site-url {
    font-size: .8em;
    color: #6c757d;
    margin-left: 10px;
}

.tech-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background: linear-gradient(135deg, #6e8efb, #a777e3);
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tech-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
}

.tech-header p {
    margin: 5px 0 0;
    font-size: 1rem;
    font-weight: 300;
    opacity: 0.9;
}

.tech-circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1; /* Ensure circles are behind content */
    border-radius: 8px; /* Match parent's border-radius */
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.tech-circle.c1 { width: 60px; height: 60px; left: 10%; bottom: 10%; animation-duration: 15s; }
.tech-circle.c2 { width: 80px; height: 80px; left: 30%; bottom: 40%; animation-duration: 20s; animation-delay: 2s; }
.tech-circle.c3 { width: 40px; height: 40px; left: 50%; bottom: 60%; animation-duration: 25s; }
.tech-circle.c4 { width: 100px; height: 100px; left: 70%; bottom: 20%; animation-duration: 18s; animation-delay: 1s; }
.tech-circle.c5 { width: 50px; height: 50px; left: 90%; bottom: 50%; animation-duration: 22s; }
.tech-circle.c6 { width: 70px; height: 70px; left: 5%; bottom: 70%; animation-duration: 17s; animation-delay: 3s; }
.tech-circle.c7 { width: 90px; height: 90px; left: 25%; bottom: 5%; animation-duration: 19s; }
.tech-circle.c8 { width: 30px; height: 30px; left: 45%; bottom: 80%; animation-duration: 23s; animation-delay: 2.5s; }
.tech-circle.c9 { width: 110px; height: 110px; left: 65%; bottom: 30%; animation-duration: 16s; }
.tech-circle.c10 { width: 55px; height: 55px; left: 85%; bottom: 75%; animation-duration: 21s; animation-delay: 1.5s; }


.tech-circle.small {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.05);
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    25% { transform: translateY(-10px) translateX(5px) rotate(5deg); }
    50% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    75% { transform: translateY(10px) translateX(-5px) rotate(-5deg); }
    100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
}

.glow-effect {
    animation: glow 1.5s infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px #a777e3, 0 0 10px #a777e3, 0 0 15px #6e8efb, 0 0 20px #6e8efb;
    }
    to {
        box-shadow: 0 0 10px #a777e3, 0 0 20px #a777e3, 0 0 30px #6e8efb, 0 0 40px #6e8efb;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
}

/* Specific List Styles */
.ping-sites-list {
    list-style-type: none;
    padding-left: 0;
}

.ping-site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
    background-color: #fff;
    transition: background-color 0.2s ease;
}

.ping-site-item:hover {
    background-color: #f8f9fa;
}

.ping-site-item span {
    flex-grow: 1;
    margin-right: 10px;
    word-break: break-all;
}

.ping-site-item .btn-group .btn {
    margin-left: 5px;
}

/* Tech Header (Animated Gradient) */
.tech-header {
    padding: 40px 20px;
    text-align: center;
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    color: white;
    border-radius: 10px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.tech-header h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
}

.tech-header p {
    font-size: 1.1rem;
    margin-bottom: 0;
    opacity: 0.9;
}

/* Floating Tech Circles */
.tech-circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none; /* Ensure circles don't interfere with text */
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.tech-circle:nth-child(1) { width: 80px; height: 80px; left: 15%; bottom: 10%; animation-duration: 18s; }
.tech-circle:nth-child(2) { width: 50px; height: 50px; left: 30%; bottom: 60%; animation-duration: 22s; animation-delay: -5s; }
.tech-circle:nth-child(3) { width: 120px; height: 120px; left: 70%; bottom: 20%; animation-duration: 25s; animation-delay: -10s; }
.tech-circle:nth-child(4) { width: 30px; height: 30px; left: 85%; bottom: 75%; animation-duration: 15s; animation-delay: -2s; }
.tech-circle:nth-child(5) { width: 60px; height: 60px; left: 5%; bottom: 40%; animation-duration: 20s; animation-delay: -8s; }

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-30px) rotate(180deg); opacity: 0.3; }
    100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
}

/* Glow Effect for Cards (Subtle) */
.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -2px; left: -2px; right: -2px; bottom: -2px;
    background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
    background-size: 400% 400%;
    z-index: -1;
    filter: blur(10px);
    opacity: 0;
    transition: opacity 0.5s ease;
    border-radius: 12px; /* Match card's border-radius */
    animation: gradientBG 10s ease infinite alternate;
}

.card:hover .glow-effect::before, /* Apply to card on hover */
.glow-effect.active::before { /* Or when .active class is present */
    opacity: 0.3;
}

/* Failed Links List Specifics */
.failed-links-list .failed-link-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.failed-links-list .failed-link-item .error-message {
    font-size: 0.85em;
    color: #777;
    margin-left: 10px;
}

/* Ping Sites List in Modal */
.ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
}

/* Action Icons */
.action-icon {
    cursor: pointer;
    color: #5f6368;
    transition: color 0.2s ease;
    font-size: 1.1rem;
}

.action-icon:hover {
    color: #1a73e8;
}

.action-icon.delete:hover {
    color: #ea4335;
}

/* Ping Site Status Indicator */
.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    background-color: #ccc; /* Default: unknown */
}

.ping-site-status.success {
    background-color: #34a853; /* Green */
}

.ping-site-status.failure {
    background-color: #ea4335; /* Red */
}

/* URL Selection Modal */
#urlSelectionList {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.url-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.url-item:last-child {
    border-bottom: none;
}

.url-item input[type="checkbox"] {
    margin-right: 10px;
}

.url-item label {
    flex-grow: 1;
    font-size: 0.9rem;
    word-break: break-all;
    cursor: pointer;
}

.url-item .skip-indicator {
    font-size: 0.8rem;
    color: #e67e22; /* Orange for skipped */
    margin-left: 10px;
    font-style: italic;
}

/* Utility classes */
.text-muted-light {
    color: #888 !important;
}

.font-monospace-sm {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.85em;
}

.badge.bg-warning-subtle {
    background-color: #fff3cd !important;
    color: #664d03 !important;
}

.badge.bg-danger-subtle {
    background-color: #f8d7da !important;
    color: #58151c !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-title {
        font-size: 2rem;
    }
    .card-columns-custom {
        column-count: 1;
    }
    .btn-group-responsive {
        flex-direction: column;
    }
    .btn-group-responsive .btn {
        margin-bottom: 5px;
        width: 100%;
    }
    .btn-group-responsive .btn:last-child {
        margin-bottom: 0;
    }
    .tech-header h1 {
        font-size: 2.2rem;
    }
    .tech-header p {
        font-size: 1rem;
    }
}

.failed-links-list, .ping-sites-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-icon {
    cursor: pointer;
    margin-left: 8px;
    font-size: 1.1em;
}

.action-icon.edit {
    color: #ffc107; /* Warning color for edit */
}

.action-icon.edit:hover {
    color: #e0a800;
}

.action-icon.delete {
    color: #dc3545; /* Danger color for delete */
}

.action-icon.delete:hover {
    color: #c82333;
}

.ping-site-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.ping-site-status.active {
    background-color: #28a745; /* Green for active */
}

.ping-site-status.inactive {
    background-color: #dc3545; /* Red for inactive */
}

.ping-site-status.unknown {
    background-color: #ffc107; /* Yellow for unknown */
}

/* Ensure interactive elements are clearly clickable */
.btn, .nav-link, .action-icon, .close, [role="button"] {
    cursor: pointer;
}

/* Improve focus visibility for accessibility */
.btn:focus, .form-control:focus, .nav-link:focus, .action-icon:focus, .close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); /* Bootstrap's default focus shadow */
}

/* Style for the list of URLs in the modal */
#urlSelectionList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#urlSelectionList .form-check-input {
    margin-right: 10px;
}

#urlSelectionList .url-text {
    flex-grow: 1;
    word-break: break-all; /* Prevent long URLs from breaking layout */
}

#urlSelectionList .badge {
    margin-left: 10px;
    font-size: 0.75em;
}

/* Products Modal Styles */
.products-modal .modal-dialog {
    max-width: 90%;
}

.products-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.products-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.products-card .card-img-top {
    transition: transform 0.2s ease;
    border-radius: 0;
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.products-card:hover .card-img-top {
    transform: scale(1.02);
}

.products-card .card-body {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 120px;
}

.products-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    color: #333;
    flex-grow: 1;
}

.pinterest-btn {
    background-color: #bd081c;
    border-color: #bd081c;
    color: white;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
    white-space: nowrap;
}

.pinterest-btn:hover {
    background-color: #8c0613;
    border-color: #8c0613;
    color: white;
    transform: scale(1.05);
}

.pinterest-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(189, 8, 28, 0.25);
    color: white;
}

.product-price {
    font-weight: 600;
    color: #28a745;
    font-size: 1.1rem;
    margin: 0;
}

.products-card .d-flex.justify-content-between {
    margin-top: auto;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive adjustments for products */
@media (max-width: 768px) {
    .products-modal .modal-dialog {
        max-width: 95%;
    }

    .products-card .card-title {
        font-size: 0.9rem;
        line-height: 1.2;
    }

    .products-card .card-body {
        padding: 0.75rem;
        min-height: 100px;
    }

    .pinterest-btn {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    .product-price {
        font-size: 1rem;
    }

    .products-card .card-img-top {
        height: 150px;
    }
}
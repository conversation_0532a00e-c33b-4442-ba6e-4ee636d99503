<?php

if (!isset($attribute_id) || !filter_var($attribute_id, FILTER_VALIDATE_INT)) {
    echo '<div class="alert alert-danger">ID do Atributo inválido ou em falta.</div>';
    echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Atributos</a>';
    return;
}

$attribute = db_query("SELECT * FROM attributes WHERE id = :id", [':id' => $attribute_id], true);
if (!$attribute) {
    echo '<div class="alert alert-danger">Atributo pai não encontrado (ID: ' . $attribute_id . ').</div>';
     echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Atributos</a>';
    return;
}

$attribute_values = db_query(
    "SELECT * FROM attribute_values WHERE attribute_id = :aid ORDER BY value_pt ASC",
    [':aid' => $attribute_id],
    false, true
);

?>

<h1>Gerir Valores para: <?= sanitize_input($attribute['name_pt']) ?></h1>
<hr>

<a href="admin.php?section=attributes&<?= get_session_id_param() ?>" class="btn btn-outline-secondary mb-3">
    <i class="bi bi-arrow-left"></i> Voltar aos Atributos
</a>

<?php display_flash_messages(); ?>

<form method="POST" action="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&<?= get_session_id_param() ?>" id="attribute-values-form">
    <?= csrf_input_field() ?>
    <input type="hidden" name="attribute_id" value="<?= $attribute_id ?>">

    <div class="card">
        <div class="card-header">Valores</div>
        <div class="card-body">
            <div id="values-list">
                <?php if (!empty($attribute_values)): ?>
                    <?php foreach ($attribute_values as $value): ?>
                        <div class="row g-3 mb-2 align-items-center value-row" data-value-id="<?= $value['id'] ?>">
                            <div class="col-md-5">
                                <label for="value_pt_<?= $value['id'] ?>" class="visually-hidden">Valor (PT)</label>
                                <input type="text" class="form-control" id="value_pt_<?= $value['id'] ?>" name="values[<?= $value['id'] ?>][value_pt]" value="<?= sanitize_input($value['value_pt']) ?>" required>
                            </div>
                            <div class="col-md-4">
                                <label for="price_modifier_<?= $value['id'] ?>" class="visually-hidden">Modificador Preço</label>
                                <div class="input-group">
                                     <span class="input-group-text"><?= get_setting('currency_symbol', '€') ?></span>
                                     <input type="number" step="0.01" class="form-control" id="price_modifier_<?= $value['id'] ?>" name="values[<?= $value['id'] ?>][price_modifier]" value="<?= sanitize_input($value['price_modifier']) ?>" placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-1 text-end">
                                <button type="button" class="btn btn-sm btn-outline-danger remove-value-btn" title="Remover Valor">
                                    <i class="bi bi-trash"></i>
                                </button>
                                <input type="hidden" name="values[<?= $value['id'] ?>][delete]" class="delete-flag" value="0">
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p id="no-values-msg">Nenhum valor definido para este atributo ainda.</p>
                <?php endif; ?>
            </div>

            <hr>

            <button type="button" id="add-value-btn" class="btn btn-secondary btn-sm">
                <i class="bi bi-plus-lg"></i> Adicionar Novo Valor
            </button>
        </div>
        <div class="card-footer text-end">
            <button type="submit" class="btn btn-primary">Guardar Alterações</button>
        </div>
    </div>
</form>

<!-- Template for new value row -->
<template id="new-value-template">
    <div class="row g-3 mb-2 align-items-center value-row new-value-row">
        <div class="col-md-5">
            <label for="value_pt_new" class="visually-hidden">Valor (PT)</label>
            <input type="text" class="form-control" id="value_pt_new" name="values[new][value_pt]" value="" placeholder="Novo Valor (ex: Vermelho)" required>
        </div>
        <div class="col-md-4">
            <label for="price_modifier_new" class="visually-hidden">Modificador Preço</label>
             <div class="input-group">
                 <span class="input-group-text"><?= get_setting('currency_symbol', '€') ?></span>
                 <input type="number" step="0.01" class="form-control" id="price_modifier_new" name="values[new][price_modifier]" value="0.00" placeholder="0.00">
            </div>
        </div>
        <div class="col-md-1 text-end">
            <button type="button" class="btn btn-sm btn-outline-danger remove-new-value-btn" title="Remover Novo Valor">
                <i class="bi bi-x-lg"></i>
            </button>
            <input type="hidden" name="values[new][delete]" class="delete-flag" value="0"> <!-- Not used for new, but keeps structure -->
        </div>
    </div>
</template>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const valuesList = document.getElementById('values-list');
    const addBtn = document.getElementById('add-value-btn');
    const template = document.getElementById('new-value-template');
    const noValuesMsg = document.getElementById('no-values-msg');

    // Add new value row
    addBtn.addEventListener('click', function() {
        const clone = template.content.cloneNode(true);
        const tempKey = `new_${Date.now()}`;
        clone.querySelector('input[name="values[new][value_pt]"]').name = `values[${tempKey}][value_pt]`;
        clone.querySelector('input[name="values[new][price_modifier]"]').name = `values[${tempKey}][price_modifier]`;
        clone.querySelector('input[name="values[new][delete]"]').name = `values[${tempKey}][delete]`;

        valuesList.appendChild(clone);
        if (noValuesMsg) noValuesMsg.style.display = 'none';
        updateRemoveButtons();
    });

    // Handle removal of existing rows (mark for deletion)
    function handleRemoveExisting(event) {
        const button = event.target.closest('.remove-value-btn');
        if (!button) return;
        const row = button.closest('.value-row');
        const deleteFlagInput = row.querySelector('.delete-flag');
        if (row && deleteFlagInput) {
            if (deleteFlagInput.value === '1') {
                row.style.opacity = '1';
                row.querySelectorAll('input').forEach(input => input.disabled = false);
                deleteFlagInput.value = '0';
                button.classList.remove('btn-warning');
                button.classList.add('btn-outline-danger');
                button.innerHTML = '<i class="bi bi-trash"></i>';
            } else {
                row.style.opacity = '0.5';
                row.querySelectorAll('input').forEach(input => input.disabled = true);
                deleteFlagInput.disabled = false;
                deleteFlagInput.value = '1';
                button.classList.remove('btn-outline-danger');
                button.classList.add('btn-warning');
                 button.innerHTML = '<i class="bi bi-arrow-counterclockwise"></i>';
            }
        }
    }

     // Handle removal of newly added rows (remove from DOM)
     function handleRemoveNew(event) {
         const button = event.target.closest('.remove-new-value-btn');
         if (!button) return;
         const row = button.closest('.new-value-row');
         if (row) {
             row.remove();
             if (valuesList.querySelectorAll('.value-row').length === 0 && noValuesMsg) {
                 noValuesMsg.style.display = 'block';
             }
         }
     }

    // Update event listeners for all remove buttons
    function updateRemoveButtons() {
        valuesList.querySelectorAll('.remove-value-btn').forEach(btn => {
            btn.removeEventListener('click', handleRemoveExisting);
            btn.addEventListener('click', handleRemoveExisting);
        });
         valuesList.querySelectorAll('.remove-new-value-btn').forEach(btn => {
             btn.removeEventListener('click', handleRemoveNew);
             btn.addEventListener('click', handleRemoveNew);
         });
    }

    // Initial setup
    updateRemoveButtons();

});
</script>

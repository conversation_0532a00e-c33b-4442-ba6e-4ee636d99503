<?php

ob_start();

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/session.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/blog_functions.php';
require_once __DIR__ . '/includes/product_functions.php';
require_once __DIR__ . '/includes/sitemap_functions.php';

$current_session_id = start_cookieless_session();

$settings = load_settings(true);

$view = $_GET['view'] ?? 'home';
$product_slug = $_GET['product'] ?? null;
$action = $_GET['action'] ?? null;

$page_title = $settings['store_name'] ?? 'eShop';
$view_data = [
    'settings' => $settings,
    'page_title' => $page_title,
    'current_session_id' => $current_session_id,
];

if ($action && isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {

    require_once __DIR__ . '/includes/ajax_handler.php';
    exit;
}

if ($action === 'submit_contact' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once __DIR__ . '/includes/functions.php';
    $success = handle_contact_form_submission($_POST);

    $result_url = BASE_URL . '/index.php?view=contact_result&' . get_session_id_param();
    header('Location: ' . $result_url);
    exit;
}

if ($view === 'checkout') {

    $cart_items = $_SESSION['cart'] ?? [];
    if (empty($cart_items)) {

        header('Location: ' . add_session_param_to_url(BASE_URL . '/index.php?view=cart'));
        exit;
    }
}

$is_download_request = ($view === 'download_file' || ($view === 'download'));
if (session_status() === PHP_SESSION_ACTIVE && !$is_download_request) {
    session_write_close();
}

if ($product_slug) {
    $view_data['product_slug'] = sanitize_input($product_slug);

    $product = get_product_by_slug($product_slug);
    if ($product) {
        $view_data['page_title'] = $product['seo_title'] ?: $product['name_pt'] . " - " . $page_title;
        $view_data['page_description'] = $product['seo_description'] ?: mb_substr(strip_tags($product['description_pt']), 0, 160);
        $view_data['seo_keywords'] = $product['seo_keywords'] ?: '';

        $view_data['og_title'] = $product['og_title'] ?: $product['name_pt'];
        $view_data['og_description'] = $product['og_description'] ?: $view_data['page_description'];

        $default_image = get_product_default_image($product['id']);
        $default_image_url = $default_image ? get_product_image_url($default_image['filename']) : '';

        
        if (empty($default_image_url)) {
            
            $default_image_url = BASE_URL . '/public/assets/images/default-product.jpg';
        }

        
        $view_data['og_image'] = !empty($product['og_image']) ? (filter_var($product['og_image'], FILTER_VALIDATE_URL) ? $product['og_image'] : BASE_URL . '/' . ltrim($product['og_image'], '/')) : $default_image_url;
        $view_data['og_type'] = 'product';

        $view_data['twitter_card'] = $product['twitter_card'] ?: 'summary_large_image';
        $view_data['twitter_title'] = $product['twitter_title'] ?: $product['name_pt'];
        $view_data['twitter_description'] = $product['twitter_description'] ?: $view_data['page_description'];
        
        $view_data['twitter_image'] = !empty($product['twitter_image']) ? (filter_var($product['twitter_image'], FILTER_VALIDATE_URL) ? $product['twitter_image'] : BASE_URL . '/' . ltrim($product['twitter_image'], '/')) : $default_image_url;
    } else {
        $view_data['page_title'] = "Produto - " . $page_title;
    }
}

if ($view === 'blog_post' && isset($_GET['slug'])) {
    $post_slug = sanitize_input($_GET['slug']);
    $post = get_blog_post($post_slug);

    if ($post && $post['is_published']) {
        $view_data['post'] = $post;
        $view_data['page_title'] = $post['seo_title'] ?: $post['title'];
        $view_data['page_description'] = $post['seo_description'] ?: mb_substr(strip_tags(($post['post_type'] === 'link' ? ($post['link_description'] ?? '') : ($post['content'] ?? ''))), 0, 160);
        $view_data['seo_keywords'] = $post['seo_keywords'] ?: '';
        $view_data['og_title'] = $post['og_title'] ?: $view_data['page_title'];
        $view_data['og_description'] = $post['og_description'] ?: $view_data['page_description'];

        $view_data['active_categories'] = get_active_categories_with_posts();
        $view_data['og_image'] = !empty($post['og_image']) ? BASE_URL . '/' . $post['og_image'] : (!empty($post['image_path']) ? BASE_URL . '/' . $post['image_path'] : null);
        $view_data['twitter_card'] = $post['twitter_card'] ?: 'summary_large_image';
        $view_data['twitter_title'] = $post['twitter_title'] ?: $view_data['page_title'];
        $view_data['twitter_description'] = $post['twitter_description'] ?: $view_data['page_description'];
        $view_data['twitter_image'] = !empty($post['twitter_image']) ? BASE_URL . '/' . $post['twitter_image'] : (!empty($post['image_path']) ? BASE_URL . '/' . $post['image_path'] : null);
    }
}

if ($view !== 'download_file') {
    include_template('frontend/partials/header.php', $view_data);

    if (($view === 'home' && !isset($_GET['page']) && !$product_slug) || (isset($_GET['page']) && $_GET['page'] === 'search')) {
        include_template('frontend/partials/collapsible_filters.php', $view_data);
    }
    echo '<main class="container mt-4 mb-5">';
}

if ($product_slug) {
    include_template('frontend/product_detail.php', $view_data);

} elseif (isset($_GET['page']) && $_GET['page'] === 'search') {

    $search_query = isset($_GET['q']) ? sanitize_input(trim($_GET['q'])) : '';
    $view_data['search_query'] = $search_query;
    $view_data['search_results'] = [];
    $view_data['total_results'] = 0;
    $view_data['current_page'] = 1;
    $view_data['per_page'] = 20;
    $view_data['total_pages'] = 0;

    if (!empty($search_query)) {
        // Get pagination parameters
        $current_page = filter_input(INPUT_GET, 'p', FILTER_VALIDATE_INT);
        $current_page = ($current_page !== false && $current_page > 0) ? $current_page : 1;
        $per_page = 20; // Fixed at 20 results per page as requested

        // Get paginated search results
        $search_result = search_site_content_paginated($search_query, $current_page, $per_page);

        $view_data['search_results'] = $search_result['results'];
        $view_data['total_results'] = $search_result['total_count'];
        $view_data['current_page'] = $current_page;
        $view_data['per_page'] = $per_page;
        $view_data['total_pages'] = ceil($search_result['total_count'] / $per_page);
        $view_data['page_title'] = 'Resultados para "' . htmlspecialchars($search_query) . '" - ' . $page_title;
    } else {
        $view_data['page_title'] = 'Pesquisa - ' . $page_title;
    }
    include_template('frontend/search_results.php', $view_data);

} elseif (isset($_GET['page']) && !empty($_GET['page'])) {

    $page_slug = sanitize_input($_GET['page']);
    $page_content = get_page_by_slug($page_slug);

    if ($page_content) {
        $view_data['page_content'] = $page_content;

        $view_data['page_title'] = !empty($page_content['seo_title']) ? $page_content['seo_title'] : $page_content['title_pt'] . " - " . $page_title;
        $view_data['page_description'] = !empty($page_content['seo_description']) ? $page_content['seo_description'] : mb_substr(strip_tags($page_content['content_pt']), 0, 160);
        $view_data['seo_keywords'] = $page_content['seo_keywords'] ?? '';

        $view_data['og_title'] = !empty($page_content['og_title']) ? $page_content['og_title'] : $page_content['title_pt'];
        $view_data['og_description'] = !empty($page_content['og_description']) ? $page_content['og_description'] : $view_data['page_description'];
        $view_data['og_image'] = !empty($page_content['og_image']) ? (filter_var($page_content['og_image'], FILTER_VALIDATE_URL) ? $page_content['og_image'] : BASE_URL . '/' . ltrim($page_content['og_image'], '/')) : '';
        $view_data['og_type'] = 'article';

        $view_data['twitter_card'] = !empty($page_content['twitter_card']) ? $page_content['twitter_card'] : 'summary';
        $view_data['twitter_title'] = !empty($page_content['twitter_title']) ? $page_content['twitter_title'] : $page_content['title_pt'];
        $view_data['twitter_description'] = !empty($page_content['twitter_description']) ? $page_content['twitter_description'] : $view_data['page_description'];
        $view_data['twitter_image'] = !empty($page_content['twitter_image']) ? (filter_var($page_content['twitter_image'], FILTER_VALIDATE_URL) ? $page_content['twitter_image'] : BASE_URL . '/' . ltrim($page_content['twitter_image'], '/')) : $view_data['og_image'];

        include_template('frontend/page.php', $view_data);
    } else {

        http_response_code(404);
        $view_data['page_title'] = "Página Não Encontrada - " . $page_title;
        echo "<h1>404 - Página Não Encontrada</h1>";
        echo "<p>Lamentamos, mas a página solicitada ('" . htmlspecialchars($page_slug) . "') não existe ou não está ativa.</p>";
    }

} elseif ($view === 'home') {

    $slider_count = get_blog_slider_count_setting();
    $slider_delay = get_blog_slider_delay_setting();
    $latest_posts_result = get_blog_posts([
        'is_published' => 1,
        'per_page' => $slider_count,
        'order_by' => 'published_at',
        'order_dir' => 'DESC'
    ]);
    $view_data['latest_blog_posts'] = $latest_posts_result['posts'] ?? [];
    $view_data['blog_slider_delay'] = $slider_delay;
    $view_data['page_title'] = "Início - " . $page_title;
    include_template('frontend/home.php', $view_data);

} elseif ($view === 'blog') {

    $category_slug = $_GET['category'] ?? null;
    $page_num = (int)($_GET['p'] ?? 1);
    $options = [
        'is_published' => 1,
        'page' => $page_num,
        'per_page' => get_setting('blog_posts_per_page', 5),
        'order_by' => 'published_at',
        'order_dir' => 'DESC'
    ];
    $category_name = "Blog";

    if ($category_slug) {
        $category_data = get_blog_category($category_slug);
        if ($category_data && $category_data['is_active']) {
            $options['category_id'] = $category_data['id'];
            $category_name = $category_data['name'];
            $view_data['current_blog_category'] = $category_data;
        } else {

        }
    }

    $posts_result = get_blog_posts($options);
    $view_data['blog_posts'] = $posts_result['posts'];
    $view_data['total_posts'] = $posts_result['total_count'];
    $view_data['current_page'] = $options['page'];
    $view_data['per_page'] = $options['per_page'];
    $view_data['page_title'] = $category_name . " - " . $page_title;
    include_template('frontend/blog_list.php', $view_data);

} elseif ($view === 'blog_post' && isset($_GET['slug'])) {

    
    if (isset($view_data['post']) && $view_data['post'] && $view_data['post']['is_published']) {
        include_template('frontend/blog_post.php', $view_data);
    } else {
        http_response_code(404);
        $view_data['page_title'] = "Post Não Encontrado - " . $page_title;
        echo "<h1>404 - Post Não Encontrado</h1>";
        echo "<p>Lamentamos, mas o post solicitado não existe ou não está publicado.</p>";
    }

} elseif ($view === 'blog_search') {

    $search_term = sanitize_input($_GET['q'] ?? '');
    $page_num = (int)($_GET['p'] ?? 1);
    $options = [
        'is_published' => 1,
        'page' => $page_num,
        'per_page' => get_setting('blog_posts_per_page', 5),
        'order_by' => 'published_at',
        'order_dir' => 'DESC',
        'search_term' => $search_term
    ];

    $posts_result = get_blog_posts($options);
    $view_data['blog_posts'] = $posts_result['posts'];
    $view_data['total_posts'] = $posts_result['total_count'];
    $view_data['current_page'] = $options['page'];
    $view_data['per_page'] = $options['per_page'];
    $view_data['search_term'] = $search_term;
    $view_data['page_title'] = 'Pesquisa no Blog: "' . htmlspecialchars($search_term) . '" - ' . $page_title;
    include_template('frontend/blog_search_results.php', $view_data);

} elseif ($view === 'cart') {

    $view_data['page_title'] = "Carrinho - " . $page_title;
    include_template('frontend/cart.php', $view_data);

} elseif ($view === 'checkout') {

     $view_data['page_title'] = "Checkout - " . $page_title;
     include_template('frontend/checkout.php', $view_data);

} elseif ($view === 'contact') {

     $view_data['page_title'] = "Contacto - " . $page_title;
     include_template('frontend/contact.php', $view_data);

} elseif ($view === 'contact_result') {

     $view_data['page_title'] = "Resultado do Contacto - " . $page_title;
     include_template('frontend/contact_result.php', $view_data);

} elseif ($view === 'order_success') {

     $view_data['page_title'] = "Encomenda Confirmada - " . $page_title;
     include_template('frontend/order_success.php', $view_data);

} elseif ($view === 'download') {

     $view_data['page_title'] = "Download de Produtos Digitais - " . $page_title;
     include_template('frontend/download_fixed.php', $view_data);

} elseif ($view === 'license_extension') {

    $view_data['page_title'] = "Solicitar Extensão de Licença - " . $page_title;
    include_template('frontend/license_extension_form.php', $view_data);

} elseif ($view === 'additional_downloads') {

    $view_data['page_title'] = "Solicitar Downloads Adicionais - " . $page_title;
    include_template('frontend/additional_downloads_form.php', $view_data);

} elseif ($view === 'download_file') {

    include_template('frontend/download_file.php', $view_data);

    exit;

} elseif ($view === 'account_downloads') {

     $view_data['page_title'] = "Meus Downloads - " . $page_title;
     include_template('frontOffice/account_downloads.php', $view_data);

} elseif ($action) {

     http_response_code(404);
     $view_data['page_title'] = "Ação não encontrada - " . $page_title;
     echo "<h1>404 - Ação não encontrada</h1>";
     echo "<p>A ação solicitada ('" . htmlspecialchars($action) . "') não é válida ou já foi processada.</p>";

} else {

    http_response_code(404);
    $view_data['page_title'] = "Página Não Encontrada - " . $page_title;
    echo "<h1>404 - Página Não Encontrada</h1>";
    echo "<p>Lamentamos, mas a página que procura ('" . htmlspecialchars($view) . "') não existe.</p>";
}

if ($view !== 'download_file') {
    echo '</main>';
    include_template('frontend/partials/footer.php', $view_data);
}

?>

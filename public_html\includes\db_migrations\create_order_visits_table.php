<?php

function create_order_visits_table(PDO $pdo): bool
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_visits';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            
            $pdo->exec("CREATE TABLE order_visits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                access_token TEXT NOT NULL,
                session_id TEXT NOT NULL,
                visit_time TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                is_first_visit INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
            );");

            
            $pdo->exec("CREATE INDEX idx_order_visits_order_id ON order_visits(order_id);");
            $pdo->exec("CREATE INDEX idx_order_visits_access_token ON order_visits(access_token);");
            $pdo->exec("CREATE INDEX idx_order_visits_session_id ON order_visits(session_id);");
        }

        return true;
    } catch (PDOException $e) {
        return false;
    }
}

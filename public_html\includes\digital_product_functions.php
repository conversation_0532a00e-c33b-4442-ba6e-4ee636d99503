<?php

require_once __DIR__ . '/license_encryption_functions.php';

function get_digital_product_by_product_id(int $product_id): array|false
{
    if ($product_id <= 0) return false;

    $sql = "SELECT dp.*, df.file_path, df.original_filename AS df_original_filename, df.display_name AS df_display_name
            FROM digital_products dp
            LEFT JOIN digital_files df ON dp.digital_file_id = df.id
            WHERE dp.product_id = :product_id";

    return db_query($sql, [':product_id' => $product_id], true);
}

function get_digital_product_file_types(int $digital_product_id): array
{
    if ($digital_product_id <= 0) return [];

    
    $digital_product = db_query(
        "SELECT digital_file_id FROM digital_products WHERE id = :id",
        [':id' => $digital_product_id],
        true
    );

    if (!$digital_product || empty($digital_product['digital_file_id'])) {
        return [];
    }

    $digital_file_id = (int)$digital_product['digital_file_id'];

    
    $sql = "SELECT dfft.* FROM digital_files_file_types dfft
            JOIN digital_file_type_associations dfta ON dfft.id = dfta.file_type_id
            WHERE dfta.digital_file_id = :digital_file_id";

    $result = db_query($sql, [':digital_file_id' => $digital_file_id], false, true);
    return is_array($result) ? $result : [];
}

function get_digital_product_file_type_ids(int $digital_product_id): array
{
    if ($digital_product_id <= 0) return [];

    
    $digital_product = db_query(
        "SELECT digital_file_id FROM digital_products WHERE id = :id",
        [':id' => $digital_product_id],
        true
    );

    if (!$digital_product || empty($digital_product['digital_file_id'])) {
        return [];
    }

    $digital_file_id = (int)$digital_product['digital_file_id'];

    
    require_once __DIR__ . '/digital_files_functions.php';
    return get_digital_file_file_type_ids($digital_file_id);
}

function get_all_file_types(): array
{
    $sql = "SELECT * FROM digital_files_file_types ORDER BY name ASC";
    $result = db_query($sql, [], false, true);
    return is_array($result) ? $result : [];
}

function create_digital_product(array $data): int|false
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        

        $sql = "INSERT INTO digital_products (
                    product_id, digital_file_id, expiry_days, download_limit,
                    created_at, updated_at
                ) VALUES (
                    :product_id, :digital_file_id, :expiry_days, :download_limit,
                    datetime('now', 'localtime'), datetime('now', 'localtime')
                )";

        $params = [
            ':product_id' => $data['product_id'] ?? null,
            ':digital_file_id' => $data['digital_file_id'] ?? null,
            ':expiry_days' => $data['expiry_days'] ?? get_setting('digital_download_expiry_days', 5),
            ':download_limit' => $data['download_limit'] ?? get_setting('digital_download_limit', 3)
        ];

        
        if (empty($params[':product_id'])) {
            throw new Exception("Product ID is missing or invalid for digital_products insert.");
        }
        if (empty($params[':digital_file_id'])) {
            throw new Exception("Digital File ID is missing or invalid for digital_products insert.");
        }

        $stmt_dp = $pdo->prepare($sql);
        if (!$stmt_dp->execute($params)) {
            $errorInfo = $stmt_dp->errorInfo();
            throw new Exception("Failed to insert digital product record. DB Error: " . ($errorInfo[2] ?? 'Unknown error'));
        }

        $digital_product_id = $pdo->lastInsertId();

        if (!$digital_product_id) {
            throw new Exception("Failed to get last insert ID for digital product");
        }

        if (!empty($data['file_types']) && is_array($data['file_types'])) {

            
            require_once __DIR__ . '/digital_files_functions.php';
            update_digital_file_file_types((int)$params[':digital_file_id'], $data['file_types'], false);
        } else {
        }
        return (int)$digital_product_id;
    } catch (Exception $e) {
        
        
        
        
        return false;
    }
}

function update_digital_product(int $digital_product_id, array $data): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }
    if ($digital_product_id <= 0) {
        return false;
    }

    try {
        

        $sql = "UPDATE digital_products SET
                    expiry_days = :expiry_days,
                    download_limit = :download_limit,
                    updated_at = datetime('now', 'localtime')";

        $params = [
            ':id' => $digital_product_id,
            ':expiry_days' => $data['expiry_days'],
            ':download_limit' => $data['download_limit']
        ];

        
        $current_digital_product = db_query(
            "SELECT digital_file_id FROM digital_products WHERE id = :id",
            [':id' => $digital_product_id],
            true
        );

        $digital_file_id = $current_digital_product['digital_file_id'] ?? null;

        if (isset($data['digital_file_id']) && filter_var($data['digital_file_id'], FILTER_VALIDATE_INT) && $data['digital_file_id'] > 0) {
            $sql .= ", digital_file_id = :digital_file_id";
            $params[':digital_file_id'] = $data['digital_file_id'];
            $digital_file_id = $data['digital_file_id']; 
        }

        $sql .= " WHERE id = :id";

        $result = db_query($sql, $params);
        if ($result === false) {
            throw new Exception("Falha ao atualizar o registo do produto digital principal.");
        }

        
        if (isset($data['file_types']) && is_array($data['file_types']) && $digital_file_id) {
            require_once __DIR__ . '/digital_files_functions.php';
            update_digital_file_file_types((int)$digital_file_id, $data['file_types'], false);
        }

        return true;
    } catch (Exception $e) {
        
        throw $e;
    }
}

function delete_digital_product(int $digital_product_id): bool
{
    if ($digital_product_id <= 0) return false;

    $digital_product_info = db_query(
        "SELECT digital_file_id FROM digital_products WHERE id = :id",
        [':id' => $digital_product_id],
        true
    );

    if (!$digital_product_info) {
        return false;
    }

    $digital_file_id_to_check = $digital_product_info['digital_file_id'];

    $result = db_query(
        "DELETE FROM digital_products WHERE id = :id",
        [':id' => $digital_product_id]
    );

    if ($result) {

        if ($digital_file_id_to_check) {

            $usage_count = db_query(
                "SELECT COUNT(*) as count FROM digital_products WHERE digital_file_id = :dfid",
                [':dfid' => $digital_file_id_to_check],
                true
            );

            if ($usage_count && (int)$usage_count['count'] === 0) {

                require_once __DIR__ . '/digital_files_functions.php';
                delete_digital_file($digital_file_id_to_check);
            }
        }
        return true;
    }
    return false;
}

function generate_license_code(): string
{
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';

    for ($i = 0; $i < 4; $i++) {
        $code .= $chars[rand(0, strlen($chars) - 1)];
    }

    $code .= '-';

    for ($i = 0; $i < 4; $i++) {
        $code .= $chars[rand(0, strlen($chars) - 1)];
    }

    $code .= '-JCS-';

    for ($i = 0; $i < 4; $i++) {
        $code .= $chars[rand(0, strlen($chars) - 1)];
    }

    $code .= '-';

    for ($i = 0; $i < 4; $i++) {
        $code .= $chars[rand(0, strlen($chars) - 1)];
    }

    return $code;
}

function create_license(array $data): int|false
{
    $pdo = get_db_connection();
    if (!$pdo) return false;

    require_once __DIR__ . '/../includes/security.php';

    try {
        $pdo->beginTransaction();

        if (empty($data['license_code'])) {
            $data['license_code'] = generate_license_code();

            $exists = db_query(
                "SELECT 1 FROM licenses WHERE license_code = :code",
                [':code' => $data['license_code']],
                true
            );

            while ($exists) {
                $data['license_code'] = generate_license_code();
                $exists = db_query(
                    "SELECT 1 FROM licenses WHERE license_code = :code",
                    [':code' => $data['license_code']],
                    true
                );
            }
        }

        if (empty($data['expiry_date'])) {
            $expiry_days = $data['expiry_days'] ?? get_setting('digital_download_expiry_days', 5);
            $data['expiry_date'] = date('Y-m-d H:i:s', strtotime("+{$expiry_days} days"));
        }

        $customer_name = encrypt_sensitive_data($data['customer_name']);
        $customer_email = encrypt_sensitive_data($data['customer_email']);

        if ($customer_name === false || $customer_email === false) {
            throw new Exception("Failed to encrypt sensitive data");
        }

        $sql = "INSERT INTO licenses (
                    license_code, order_id, order_item_id, customer_name, customer_email,
                    status, expiry_date, download_limit, downloads_used, is_encrypted,
                    created_at, updated_at
                ) VALUES (
                    :license_code, :order_id, :order_item_id, :customer_name, :customer_email,
                    :status, :expiry_date, :download_limit, :downloads_used, 1,
                    datetime('now', 'localtime'), datetime('now', 'localtime')
                )";

        $params = [
            ':license_code' => $data['license_code'],
            ':order_id' => $data['order_id'] ?? null,
            ':order_item_id' => $data['order_item_id'] ?? null,
            ':customer_name' => $customer_name,
            ':customer_email' => $customer_email,
            ':status' => $data['status'] ?? 'active',
            ':expiry_date' => $data['expiry_date'],
            ':download_limit' => $data['download_limit'] ?? get_setting('digital_download_limit', 3),
            ':downloads_used' => $data['downloads_used'] ?? 0
        ];

        db_query($sql, $params);
        $license_id = $pdo->lastInsertId();

        if (!empty($data['digital_product_ids']) && is_array($data['digital_product_ids'])) {
            foreach ($data['digital_product_ids'] as $digital_product_id) {
                $sql = "INSERT INTO license_files (
                            license_id, digital_product_id, created_at
                        ) VALUES (
                            :license_id, :digital_product_id, datetime('now', 'localtime')
                        )";

                db_query($sql, [
                    ':license_id' => $license_id,
                    ':digital_product_id' => $digital_product_id
                ]);
            }
        }

        $pdo->commit();
        return $license_id;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_license_by_code(string $license_code): array|false
{
    if (empty($license_code)) return false;

    $sql = "SELECT * FROM licenses WHERE license_code = :code";
    return db_query($sql, [':code' => $license_code], true);
}

function get_license_by_id(int $license_id): array|false
{
    if ($license_id <= 0) return false;

    $sql = "SELECT * FROM licenses WHERE id = :id";
    return db_query($sql, [':id' => $license_id], true);
}

function get_all_licenses(?string $status = null): array
{
    $sql = "SELECT * FROM licenses";
    $params = [];

    if ($status !== null) {
        $sql .= " WHERE status = :status";
        $params[':status'] = $status;
    }

    $sql .= " ORDER BY created_at DESC";

    $result = db_query($sql, $params, false, true);
    return is_array($result) ? $result : [];
}

function update_license(int $license_id, array $data): bool
{
    $pdo = get_db_connection();
    if (!$pdo || $license_id <= 0) return false;

    require_once __DIR__ . '/../includes/security.php';

    try {
        $pdo->beginTransaction();

        $customer_name = encrypt_sensitive_data($data['customer_name']);
        $customer_email = encrypt_sensitive_data($data['customer_email']);

        if ($customer_name === false || $customer_email === false) {
            throw new Exception("Failed to encrypt sensitive data");
        }

        $sql = "UPDATE licenses SET
                    customer_name = :customer_name,
                    customer_email = :customer_email,
                    status = :status,
                    expiry_date = :expiry_date,
                    download_limit = :download_limit,
                    is_encrypted = 1,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $params = [
            ':id' => $license_id,
            ':customer_name' => $customer_name,
            ':customer_email' => $customer_email,
            ':status' => $data['status'],
            ':expiry_date' => $data['expiry_date'],
            ':download_limit' => $data['download_limit']
        ];

        db_query($sql, $params);

        if (isset($data['digital_product_ids']) && is_array($data['digital_product_ids'])) {

            $sql = "DELETE FROM license_files WHERE license_id = :license_id";
            db_query($sql, [':license_id' => $license_id]);

            foreach ($data['digital_product_ids'] as $digital_product_id) {
                $sql = "INSERT INTO license_files (
                            license_id, digital_product_id, created_at
                        ) VALUES (
                            :license_id, :digital_product_id, datetime('now', 'localtime')
                        )";

                db_query($sql, [
                    ':license_id' => $license_id,
                    ':digital_product_id' => $digital_product_id
                ]);
            }
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function delete_license(int $license_id): bool
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $pdo->beginTransaction();

        
        $stmt_downloads = $pdo->prepare("DELETE FROM downloads WHERE license_id = :license_id");
        $stmt_downloads->execute([':license_id' => $license_id]);

        
        $stmt_license_files = $pdo->prepare("DELETE FROM license_files WHERE license_id = :license_id");
        $stmt_license_files->execute([':license_id' => $license_id]);

        
        $stmt_tokens = $pdo->prepare("DELETE FROM download_tokens WHERE license_id = :license_id");
        $stmt_tokens->execute([':license_id' => $license_id]);

        
        $stmt_licenses = $pdo->prepare("DELETE FROM licenses WHERE id = :id");
        $stmt_licenses->execute([':id' => $license_id]);
        $deletedRows = $stmt_licenses->rowCount();

        if ($deletedRows === 0) {
        }

        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    } catch (Exception $e) { 
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function record_download_attempt(int $license_id): bool
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $sql = "INSERT INTO downloads (
                    license_id, ip_address, user_agent, download_date
                ) VALUES (
                    :license_id, :ip_address, :user_agent, datetime('now', 'localtime')
                )";

        $params = [
            ':license_id' => $license_id,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        db_query($sql, $params);

        $sql = "UPDATE licenses SET
                    downloads_used = downloads_used + 1,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        db_query($sql, [':id' => $license_id]);

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function verify_license_email(int $license_id, string $email): array
{
    if ($license_id <= 0 || empty($email)) {
        return [
            'valid' => false,
            'message' => 'Parâmetros inválidos para verificação de email.'
        ];
    }

    $license = get_license_by_id($license_id);
    if (!$license) {
        return [
            'valid' => false,
            'message' => 'Licença não encontrada.'
        ];
    }

    $decrypted_email = get_decrypted_license_email($license);

    if (strtolower(trim($email)) === strtolower(trim($decrypted_email))) {
        return [
            'valid' => true,
            'message' => 'Email verificado com sucesso.',
            'license' => $license
        ];
    } else {
        return [
            'valid' => false,
            'message' => 'O email fornecido não corresponde ao email associado a esta licença.',
            'license' => $license
        ];
    }
}

function check_license_validity(string $license_code, bool $check_token = true, ?string $email = null): array
{
    $license = get_license_by_code($license_code);

    if (!$license) {
        return [
            'valid' => false,
            'message' => 'Licença não encontrada.'
        ];
    }

    if ($license['status'] !== 'active') {
        return [
            'valid' => false,
            'message' => 'Esta licença não está ativa.',
            'license' => $license
        ];
    }

    if (strtotime($license['expiry_date']) < time()) {
        return [
            'valid' => false,
            'message' => 'O período de download dos ficheiros associados a esta licença expirou.',
            'license' => $license
        ];
    }

    if ($license['downloads_used'] >= $license['download_limit']) {
        return [
            'valid' => false,
            'message' => 'Limite de downloads atingido para esta licença.',
            'license' => $license
        ];
    }

    if ($check_token) {

        $active_token = get_active_download_token($license['id']);

        if (!$active_token || !$active_token['is_verified']) {

            if ($email !== null) {
                $email_verification = verify_license_email($license['id'], $email);

                if (!$email_verification['valid']) {
                    return [
                        'valid' => true,
                        'requires_email_verification' => true,
                        'message' => 'Por favor, forneça o email associado a esta licença para receber o código de segurança.',
                        'license' => $license
                    ];
                }

                $token_data = create_download_token($license['id']);

                if ($token_data) {

                    send_security_token_email($license, $token_data);

                    return [
                        'valid' => true,
                        'requires_token' => true,
                        'message' => 'Um código de segurança foi enviado para o seu email. Por favor, verifique sua caixa de entrada e insira o código para continuar.',
                        'license' => $license,
                        'token_data' => $token_data
                    ];
                } else {
                    return [
                        'valid' => false,
                        'message' => 'Erro ao gerar código de segurança. Por favor, tente novamente.'
                    ];
                }
            } else {

                return [
                    'valid' => true,
                    'requires_email_verification' => true,
                    'message' => 'Por favor, forneça o email associado a esta licença para receber o código de segurança.',
                    'license' => $license
                ];
            }
        }
    }

    return [
        'valid' => true,
        'message' => 'Licença válida.',
        'license' => $license
    ];
}

function get_license_digital_products(int $license_id): array
{
    if ($license_id <= 0) return [];

    
    
    $sql = "SELECT dp.*, df.file_path, df.original_filename AS df_original_filename, df.display_name AS df_display_name
            FROM digital_products dp
            JOIN license_files lf ON dp.id = lf.digital_product_id
            LEFT JOIN digital_files df ON dp.digital_file_id = df.id
            WHERE lf.license_id = :license_id";

    $result = db_query($sql, [':license_id' => $license_id], false, true);
    return is_array($result) ? $result : [];
}

function create_license_text_content(array $license): string
{
    $license_text = get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.');

    $customer_name = get_decrypted_license_name($license);
    $customer_email = get_decrypted_license_email($license);

    $content = "LICENÇA DE USO\n";
    $content .= "=============\n\n";
    $content .= $license_text . "\n\n";
    $content .= "Estes ficheiros foram licenciados ao utilizador {$customer_name} com o email {$customer_email} em " . date('Y-m-d H:i:s') . "\n\n";
    $content .= "Código de Licença: {$license['license_code']}\n";
    $content .= "Data limite de download: {$license['expiry_date']}\n";
    $content .= "Limite de Downloads: {$license['download_limit']}\n";

    return $content;
}

function create_license_zip_file(array $license): string|false
{
    if (empty($license['id'])) return false;

    $digital_products = get_license_digital_products($license['id']);
    if (empty($digital_products)) return false;

    $temp_dir = sys_get_temp_dir() . '/license_' . $license['id'] . '_' . time();
    if (!is_dir($temp_dir) && !mkdir($temp_dir, 0755, true)) {
        return false;
    }

    $license_text_content = create_license_text_content($license);
    $license_text_file = $temp_dir . '/licença.txt';
    if (file_put_contents($license_text_file, $license_text_content) === false) {
        return false;
    }

    if (class_exists('ZipArchive')) {

        $zip_file = $temp_dir . '/license_' . $license['license_code'] . '.zip';
        $zip = new ZipArchive();

        if ($zip->open($zip_file, ZipArchive::CREATE) !== true) {
            return false;
        }

        $zip->addFile($license_text_file, 'licença.txt');

        foreach ($digital_products as $product) {
            $file_path = $product['file_path'];
            if (file_exists($file_path)) {
                $file_name = basename($file_path);
                $zip->addFile($file_path, $file_name);
            } else {
            }
        }

        $zip->close();

        @unlink($license_text_file);

        return $zip_file;
    } else {

        if (count($digital_products) === 1) {
            $product = $digital_products[0];
            $file_path = $product['file_path'];

            if (file_exists($file_path)) {

                $license_text_dest = dirname($file_path) . '/LICENSE-' . $license['license_code'] . '.txt';
                copy($license_text_file, $license_text_dest);

                @unlink($license_text_file);

                return $file_path;
            }
        }

        return false;
    }
}

function send_license_download_email(array $license): bool
{
    if (empty($license['id'])) return false;

    $customer_name = get_decrypted_license_name($license);
    $customer_email = get_decrypted_license_email($license);

    if (empty($customer_email)) return false;

    
    $token_data = create_download_token($license['id'], 1440); 
    if (!$token_data) {
        return false;
    }

    
    $download_url = BASE_URL . '/index.php?view=download&email_token=' . urlencode($token_data['email_token']);
    $download_url = add_session_param_to_url($download_url);

    $from_email = get_setting('from_email', '<EMAIL>');
    $from_name = get_setting('from_name', 'My Store');
    $store_name = get_setting('store_name', 'My Store');

    $subject = "Seu download está pronto - {$store_name}";

    $message = "<html><body>";
    $message .= "<h1>Seu download está pronto!</h1>";
    $message .= "<p>Olá {$customer_name},</p>";
    $message .= "<p>Seu download está pronto para ser baixado. Clique no link abaixo para baixar seus arquivos:</p>";
    $message .= "<p><a href=\"{$download_url}\">{$download_url}</a></p>";
    $message .= "<p>Detalhes da licença:</p>";
    $message .= "<ul>";
    $message .= "<li>Código de Licença: {$license['license_code']}</li>";
    $message .= "<li>Data de Expiração: {$license['expiry_date']}</li>";
    $message .= "<li>Limite de Downloads: {$license['download_limit']}</li>";
    $message .= "</ul>";
    $message .= "<p>Obrigado por sua compra!</p>";
    $message .= "<p>Atenciosamente,<br>{$store_name}</p>";
    $message .= "</body></html>";

    require_once __DIR__ . '/email_functions.php';
    return send_email($customer_email, $customer_name, $subject, $message);
}

function process_download_request(string $license_code, ?string $security_token = null, ?string $email_token = null, ?string $verification_email = null): array
{

    if (!empty($email_token)) {
        $token_verification = verify_email_download_token($email_token);

        if (!$token_verification['valid']) {
            return [
                'success' => false,
                'message' => $token_verification['message']
            ];
        }

        $license_code = $token_verification['license_code'];

        $validity = check_license_validity($license_code, false);
    } else {

        $validity = check_license_validity($license_code, false);

        if ($validity['valid'] && empty($security_token)) {

            if (!empty($verification_email)) {
                $email_verification = verify_license_email($validity['license']['id'], $verification_email);

                if (!$email_verification['valid']) {
                    return [
                        'success' => false,
                        'requires_email_verification' => true,
                        'message' => $email_verification['message'],
                        'license' => $validity['license']
                    ];
                }

                if (empty($security_token)) {

                    $token_data = create_download_token($validity['license']['id']);

                    if ($token_data) {

                        send_security_token_email($validity['license'], $token_data);

                        return [
                            'success' => false,
                            'requires_token' => true,
                            'message' => 'Um código de segurança foi enviado para o seu email. Por favor, verifique sua caixa de entrada e insira o código para continuar.',
                            'license' => $validity['license'],
                            'token_data' => $token_data
                        ];
                    }
                }
            } else {

                return [
                    'success' => false,
                    'requires_email_verification' => true,
                    'message' => 'Por favor, forneça o email associado a esta licença.',
                    'license' => $validity['license']
                ];
            }
        }

        if (isset($validity['requires_token']) && $validity['requires_token'] && empty($security_token)) {
            return [
                'success' => false,
                'requires_token' => true,
                'message' => $validity['message'],
                'license' => $validity['license']
            ];
        }

        if (!empty($security_token) && isset($validity['license'])) {
            $token_verification = verify_security_token($validity['license']['id'], $security_token);

            if (!$token_verification['valid']) {
                return [
                    'success' => false,
                    'requires_token' => true,
                    'message' => $token_verification['message'],
                    'license' => $validity['license']
                ];
            }
        }
    }

    if (!$validity['valid']) {
        return [
            'success' => false,
            'message' => $validity['message']
        ];
    }

    $license = $validity['license'];

    
    $token_id_to_increment = null;
    $current_token_attempts = 0;
    $token_details_for_check = null;

    if (!empty($email_token)) {
        
        $token_details_for_check = db_query("SELECT id, attempts, license_id FROM download_tokens WHERE email_token = :etoken AND is_verified = 1 ORDER BY created_at DESC LIMIT 1", [':etoken' => $email_token], true);
    } elseif (!empty($security_token) && isset($validity['license']['id'])) {
        
        $token_details_for_check = db_query("SELECT id, attempts, license_id FROM download_tokens WHERE license_id = :lid AND security_token = :stoken AND is_verified = 1 ORDER BY created_at DESC LIMIT 1", [':lid' => $validity['license']['id'], ':stoken' => $security_token], true);
    }

    if ($token_details_for_check) {
        $token_id_to_increment = $token_details_for_check['id'];
        $current_token_attempts = (int)$token_details_for_check['attempts'];
        
        
        
        if ($license['id'] != $token_details_for_check['license_id']) {
            
            $refreshed_license = get_license_by_id($token_details_for_check['license_id']);
            if (!$refreshed_license) {
                 return ['success' => false, 'message' => 'Erro ao validar dados da licença associada ao token.'];
            }
            $license = $refreshed_license; 
        }

    } else {
        return [
            'success' => false,
            'message' => 'Erro interno ao processar o token de download (não foi possível encontrar o registo do token).'
        ];
    }

    
    
    if ($current_token_attempts >= $license['download_limit']) {
        db_query("UPDATE download_tokens SET is_used = 1 WHERE id = :id", [':id' => $token_id_to_increment]); 
        return [
            'success' => false,
            'message' => 'O limite de downloads para este link/token (' . $license['download_limit'] . ' downloads) foi atingido.'
        ];
    }

    
    if (!record_download_attempt($license['id'])) { 
        return [
            'success' => false,
            'message' => 'Erro ao registrar tentativa de download na licença.'
        ];
    }

    
    $update_token_attempts_sql = "UPDATE download_tokens SET attempts = attempts + 1 WHERE id = :id";
    db_query($update_token_attempts_sql, [':id' => $token_id_to_increment]);
    $new_token_attempts = $current_token_attempts + 1;

    
    if ($new_token_attempts >= $license['download_limit']) {
        db_query("UPDATE download_tokens SET is_used = 1 WHERE id = :id", [':id' => $token_id_to_increment]);
    }

    $file_path = create_license_zip_file($license);

    if (!$file_path) {
        
        
        return [
            'success' => false,
            'message' => 'Erro ao criar arquivo de download.'
        ];
    }

    $is_zip = (pathinfo($file_path, PATHINFO_EXTENSION) === 'zip');
    $file_name = $is_zip
        ? 'license_' . $license['license_code'] . '.zip'
        : basename($file_path);

    return [
        'success' => true,
        'message' => 'Download pronto.',
        'file_path' => $file_path,
        'file_name' => $file_name,
        'is_zip' => $is_zip
    ];
}

function get_censored_string(string $string, int $show_first = 2, int $show_last = 2): string
{
    $length = mb_strlen($string);

    if ($length <= $show_first + $show_last) {
        return str_repeat('*', $length);
    }

    $first = mb_substr($string, 0, $show_first);
    $last = mb_substr($string, -$show_last);
    $middle = str_repeat('*', $length - $show_first - $show_last);

    return $first . $middle . $last;
}

function get_fully_censored_string(string $string): string
{
    $length = mb_strlen($string);

    if ($length <= 2) {
        return str_repeat('*', $length);
    }

    $first = mb_substr($string, 0, 1);
    $last = mb_substr($string, -1);
    $middle = str_repeat('*', $length - 2);

    return $first . $middle . $last;
}

function get_censored_email(string $email): string
{
    $parts = explode('@', $email);

    if (count($parts) !== 2) {
        return get_censored_string($email);
    }

    $username = $parts[0];
    $domain = $parts[1];

    $censored_username = get_censored_string($username, 2, 1);
    $censored_domain = get_censored_string($domain, 1, 2);

    return $censored_username . '@' . $censored_domain;
}

function get_fully_censored_email(string $email): string
{
    $parts = explode('@', $email);

    if (count($parts) !== 2) {
        return get_fully_censored_string($email);
    }

    $username = $parts[0];
    $domain = $parts[1];

    $censored_username = get_fully_censored_string($username);
    $censored_domain = get_fully_censored_string($domain);

    return $censored_username . '@' . $censored_domain;
}

function get_license_download_count(int $license_id): int
{
    if ($license_id <= 0) return 0;

    $sql = "SELECT COUNT(*) as count FROM downloads WHERE license_id = :license_id";
    $result = db_query($sql, [':license_id' => $license_id], true);

    return $result ? (int)$result['count'] : 0;
}

function generate_security_token(): string
{

    return str_pad((string)mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
}

function generate_email_download_token(): string
{
    return bin2hex(random_bytes(16));
}

function create_download_token(int $license_id, int $expiry_minutes = 60, bool $admin_override_session = false): array|false
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {

        $security_token = generate_security_token();
        $email_token = generate_email_download_token();

        $expires_at = date('Y-m-d H:i:s', strtotime("+{$expiry_minutes} minutes"));

        $session_id = $admin_override_session ? null : session_id();

        
        
        
        if (!$admin_override_session && $session_id) {
            $sql_invalidate_previous = "UPDATE download_tokens
                                      SET is_used = 1
                                      WHERE license_id = :license_id
                                      AND session_id = :session_id";
            db_query($sql_invalidate_previous, [
                ':license_id' => $license_id,
                ':session_id' => $session_id
            ]);
        }

        $sql = "INSERT INTO download_tokens (
                    license_id, security_token, email_token,
                    is_verified, is_used, attempts, expires_at, session_id
                ) VALUES (
                    :license_id, :security_token, :email_token,
                    0, 0, 0, :expires_at, :session_id
                )";

        $params = [
            ':license_id' => $license_id,
            ':security_token' => $security_token,
            ':email_token' => $email_token,
            ':expires_at' => $expires_at,
            ':session_id' => $session_id
        ];

        if ($admin_override_session) {
            
            
            $sql = str_replace("0, 0, 0, :expires_at, :session_id", "1, 0, 0, :expires_at, :session_id", $sql);
        }

        $result = db_query($sql, $params);
        if ($result === false) {
            throw new Exception("Failed to insert download token");
        }

        $token_id = $pdo->lastInsertId();

        return [
            'id' => $token_id,
            'license_id' => $license_id,
            'security_token' => $security_token,
            'email_token' => $email_token,
            'expires_at' => $expires_at,
            'session_id' => $session_id
        ];
    } catch (Exception $e) {
        return false;
    }
}

function get_active_download_token(int $license_id): array|false
{
    if ($license_id <= 0) return false;

    $session_id = session_id();

    
    
    $sql = "SELECT * FROM download_tokens
            WHERE license_id = :license_id
            AND session_id = :session_id
            AND session_id IS NOT NULL
            AND is_used = 0
            AND expires_at > datetime('now', 'localtime')
            ORDER BY created_at DESC LIMIT 1";

    return db_query($sql, [
        ':license_id' => $license_id,
        ':session_id' => $session_id
    ], true);
}

function verify_security_token(int $license_id, string $token): array
{
    if ($license_id <= 0 || empty($token)) {
        return [
            'valid' => false,
            'message' => 'Código de segurança inválido.'
        ];
    }

    $pdo = get_db_connection();
    if (!$pdo) return [
        'valid' => false,
        'message' => 'Erro de conexão com o banco de dados.'
    ];

    try {

        $session_id = session_id();

        
        $sql = "SELECT * FROM download_tokens
                WHERE license_id = :license_id
                AND security_token = :token
                AND (session_id = :session_id OR session_id IS NULL)
                AND is_used = 0
                AND expires_at > datetime('now', 'localtime')
                ORDER BY CASE WHEN session_id IS NULL THEN 0 ELSE 1 END, created_at DESC -- Prioritize NULL session_id tokens if multiple match
                LIMIT 1";

        $token_data = db_query($sql, [
            ':license_id' => $license_id,
            ':token' => $token,
            ':session_id' => $session_id 
        ], true);

        if (!$token_data) {
            
            
            
            
            
            
            if ($token_data && $token_data['session_id'] !== null) {
                $sql_invalidate_others = "UPDATE download_tokens
                                          SET is_used = 1
                                          WHERE license_id = :license_id
                                            AND session_id = :session_id
                                            AND security_token != :token
                                            AND is_used = 0
                                            AND expires_at > datetime('now', 'localtime')";
                db_query($sql_invalidate_others, [
                    ':license_id' => $license_id,
                    ':session_id' => $session_id,
                    ':token' => $token
                ]);
            }

            return [
                'valid' => false,
                'message' => 'Código de segurança inválido ou expirado. Se desejar, pode solicitar um novo código de segurança ao tentar novamente.',
                'token_invalidated' => true, 
                'license_id' => $license_id
            ];
        }

        
        $license = get_license_by_id($token_data['license_id']);
        if (!$license) {
            return [
                'valid' => false,
                'message' => 'Erro ao verificar detalhes da licença associada ao token de segurança.'
            ];
        }

        if ((int)$token_data['attempts'] >= (int)$license['download_limit']) {
            
            db_query("UPDATE download_tokens SET is_used = 1 WHERE id = :id", [':id' => $token_data['id']]);
            return [
                'valid' => false,
                'message' => 'Este código de segurança atingiu o número máximo de utilizações (' . $license['download_limit'] . ').',
                'license_id' => $license_id
            ];
        }

        $sql = "UPDATE download_tokens
                SET is_verified = 1
                WHERE id = :id";

        db_query($sql, [':id' => $token_data['id']]);

        return [
            'valid' => true,
            'message' => 'Código de segurança verificado com sucesso.',
            'token_data' => $token_data
        ];
    } catch (Exception $e) {
        return [
            'valid' => false,
            'message' => 'Erro ao verificar o código de segurança.'
        ];
    }
}

function create_license_verification_token(int $license_id, int $expiry_minutes = 15): array|false
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $session_id = session_id();

        
        $cleanup_sql = "DELETE FROM license_verification_tokens
                       WHERE license_id = :license_id
                       AND session_id = :session_id";
        db_query($cleanup_sql, [
            ':license_id' => $license_id,
            ':session_id' => $session_id
        ]);

        
        $verification_code = str_pad((string)mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
        $expires_at = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));

        $sql = "INSERT INTO license_verification_tokens
                (license_id, verification_code, session_id, expires_at)
                VALUES (:license_id, :verification_code, :session_id, :expires_at)";

        $params = [
            ':license_id' => $license_id,
            ':verification_code' => $verification_code,
            ':session_id' => $session_id,
            ':expires_at' => $expires_at
        ];

        $result = db_query($sql, $params);

        if ($result) {
            return [
                'verification_code' => $verification_code,
                'expires_at' => $expires_at,
                'license_id' => $license_id
            ];
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

function verify_license_verification_token(int $license_id, string $verification_code): array
{
    if ($license_id <= 0 || empty($verification_code)) {
        return [
            'valid' => false,
            'message' => 'Código de verificação inválido.'
        ];
    }

    $session_id = session_id();

    $sql = "SELECT * FROM license_verification_tokens
            WHERE license_id = :license_id
            AND verification_code = :verification_code
            AND session_id = :session_id
            AND expires_at > datetime('now', 'localtime')
            AND is_verified = 0
            ORDER BY created_at DESC LIMIT 1";

    $token_data = db_query($sql, [
        ':license_id' => $license_id,
        ':verification_code' => $verification_code,
        ':session_id' => $session_id
    ], true);

    if (!$token_data) {
        return [
            'valid' => false,
            'message' => 'Código de verificação inválido ou expirado.'
        ];
    }

    
    $update_sql = "UPDATE license_verification_tokens
                   SET is_verified = 1
                   WHERE id = :id";
    db_query($update_sql, [':id' => $token_data['id']]);

    return [
        'valid' => true,
        'message' => 'Código de verificação válido.',
        'token_data' => $token_data
    ];
}

function verify_email_download_token(string $email_token): array
{
    if (empty($email_token)) {
        return [
            'valid' => false,
            'message' => 'Token de download inválido.'
        ];
    }

    $session_id = session_id();

    $sql = "SELECT dt.*, l.id as license_id, l.license_code
            FROM download_tokens dt
            JOIN licenses l ON dt.license_id = l.id
            WHERE dt.email_token = :email_token
            AND dt.is_used = 0
            AND dt.expires_at > datetime('now', 'localtime')
            LIMIT 1";

    $token_data = db_query($sql, [':email_token' => $email_token], true);

    if (!$token_data) {
        return [
            'valid' => false,
            'message' => 'Token de download inválido ou expirado.'
        ];
    }

    
    $license = get_license_by_id($token_data['license_id']);
    if (!$license) {
        return [
            'valid' => false,
            'message' => 'Erro ao verificar detalhes da licença associada ao token.'
        ];
    }

    if ((int)$token_data['attempts'] >= (int)$license['download_limit']) {
        
        db_query("UPDATE download_tokens SET is_used = 1 WHERE id = :id", [':id' => $token_data['id']]);
        return [
            'valid' => false,
            'message' => 'Este token de download atingiu o número máximo de utilizações (' . $license['download_limit'] . ').',
            'license_code' => $token_data['license_code']
        ];
    }

    if ($token_data['session_id'] !== $session_id) {
        $sql = "UPDATE download_tokens
                SET session_id = :session_id
                WHERE id = :id";

        db_query($sql, [
            ':session_id' => $session_id,
            ':id' => $token_data['id']
        ]);

        $token_data['session_id'] = $session_id;
    }

    $sql = "UPDATE download_tokens
            SET is_verified = 1
            WHERE id = :id";

    db_query($sql, [':id' => $token_data['id']]);

    return [
        'valid' => true,
        'message' => 'Token de download verificado com sucesso.',
        'license_id' => $token_data['license_id'],
        'license_code' => $token_data['license_code'],
        'token_data' => $token_data
    ];
}

function send_security_token_email(array $license, array $token_data): bool
{
    if (empty($license) || empty($token_data)) return false;

    $customer_name = get_decrypted_license_name($license);
    $customer_email = get_decrypted_license_email($license);

    if (empty($customer_email)) return false;

    $store_name = get_setting('store_name', 'A Nossa Loja');

    $base_url = BASE_URL;

    $subject = "Seu código de segurança para download - {$store_name}";

    $message = "<html><body>";
    $message .= "<h2>Código de Segurança para autorização de Download</h2>";
    $message .= "<p>Olá {$customer_name},</p>";
    $message .= "<p>está a receber este email porque alguém (esperamos que tenha sido você) solicitou um novo código de segurança para download dos seus produtos digitais.</p>";
    $message .= "<br><p>Assim sendo, aqui está o seu código de segurança para download dos produtos digitais:</p>";
    $message .= "<div style='background-color: #f5f5f5; padding: 15px; font-size: 24px; text-align: center; letter-spacing: 5px; font-weight: bold; margin: 20px 0;'>";
    $message .= $token_data['security_token'];
    $message .= "</div>";
    $message .= "<p>Note que ete código é válido por " . ceil((strtotime($token_data['expires_at']) - time()) / 60) . " minutos.</p><br>";
    $message .= "<p>De igual forma, também pode usar o link direto abaixo para fazer o download sem precisar inserir o código:  ";

    $download_url = $base_url . '/index.php?view=download&email_token=' . urlencode($token_data['email_token']);

    $download_url = add_session_param_to_url($download_url);

    $message .= "<a href=\"{$download_url}\" style='display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Download Direto</a></p>";
    $message .= "<br><p>Se o botão acima não funcionar, copie e cole o link abaixo no seu navegador:</p>";
    $message .= "<p>{$download_url}</p>";
    $message .= "<br><p>Se não foi você que solicitou este código, por favor ignore este email! Sem este código é impossivel recuperar os ficheiros associados. Mas se voltar a receber emails com mais pedidos de código de segurança, por favor entre em contato (basta responder a este email pois ele é monitorizado)..</p><br>";
    $message .= "<br><p>Atenciosamente,<br>{$store_name}</p>";
    $message .= "</body></html>";

    return send_email($customer_email, $customer_name, $subject, $message);
}

function reset_license_downloads(int $license_id): bool
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $sql = "UPDATE licenses SET
                    downloads_used = 0,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $result = db_query($sql, [':id' => $license_id]);
        if ($result === false) {
            throw new Exception("Failed to reset download count");
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function update_license_expiry_date(int $license_id, string $expiry_date): bool
{
    if ($license_id <= 0 || empty($expiry_date)) return false;

    $formatted_date = date('Y-m-d 23:59:59', strtotime($expiry_date));

    $sql = "UPDATE licenses SET
                expiry_date = :expiry_date,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";

    return db_query($sql, [
        ':id' => $license_id,
        ':expiry_date' => $formatted_date
    ]) !== false;
}

function delete_license_download_history(int $license_id, ?int $download_id = null): bool
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        if ($download_id) {

            $sql = "DELETE FROM downloads
                    WHERE id = :download_id AND license_id = :license_id";
            $params = [
                ':download_id' => $download_id,
                ':license_id' => $license_id
            ];
        } else {

            $sql = "DELETE FROM downloads WHERE license_id = :license_id";
            $params = [':license_id' => $license_id];
        }

        $result = db_query($sql, $params);
        if ($result === false) {
            throw new Exception("Failed to delete download history");
        }

        if (!$download_id) {
            $sql = "UPDATE licenses SET
                        downloads_used = 0,
                        updated_at = datetime('now', 'localtime')
                    WHERE id = :id";

            $result = db_query($sql, [':id' => $license_id]);
            if ($result === false) {
                throw new Exception("Failed to reset download count");
            }
        } else {

            $sql = "UPDATE licenses SET
                        downloads_used = CASE WHEN downloads_used > 0 THEN downloads_used - 1 ELSE 0 END,
                        updated_at = datetime('now', 'localtime')
                    WHERE id = :id";

            $result = db_query($sql, [':id' => $license_id]);
            if ($result === false) {
                throw new Exception("Failed to update download count");
            }
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function toggle_license_status(int $license_id, ?string $new_status = null): bool
{
    if ($license_id <= 0) return false;

    $license = get_license_by_id($license_id);
    if (!$license) return false;

    if ($new_status !== null) {
        $status = $new_status;
    } else {
        $status = ($license['status'] === 'active') ? 'disabled' : 'active';
    }

    $sql = "UPDATE licenses SET
                status = :status,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";

    return db_query($sql, [
        ':id' => $license_id,
        ':status' => $status
    ]) !== false;
}

function delete_download_record(int $download_id): bool
{
    if ($download_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $download = db_query(
            "SELECT license_id FROM downloads WHERE id = :id",
            [':id' => $download_id],
            true
        );

        if (!$download) {
            throw new Exception("Download record not found");
        }

        $license_id = $download['license_id'];

        $sql = "DELETE FROM downloads WHERE id = :id";
        $result = db_query($sql, [':id' => $download_id]);

        if ($result === false) {
            throw new Exception("Failed to delete download record");
        }

        $sql = "UPDATE licenses SET
                    downloads_used = CASE WHEN downloads_used > 0 THEN downloads_used - 1 ELSE 0 END,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $result = db_query($sql, [':id' => $license_id]);
        if ($result === false) {
            throw new Exception("Failed to update download count");
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function request_license_extension(int $license_id, int $days_to_add = 5, string $reason = ''): array
{
    if ($license_id <= 0 || $days_to_add <= 0) {
        return [
            'success' => false,
            'message' => 'Parâmetros inválidos para extensão de licença.'
        ];
    }

    $license = get_license_by_id($license_id);
    if (!$license) {
        return [
            'success' => false,
            'message' => 'Licença não encontrada.'
        ];
    }

    $admin_email = get_setting('admin_email', '');
    if (empty($admin_email)) {
        return [
            'success' => false,
            'message' => 'Erro ao processar solicitação. Email de administrador não configurado.'
        ];
    }

    $store_name = get_setting('store_name', 'Minha Loja');
    $subject = "Solicitação de Extensão de Licença - {$store_name}";

    $message = "<html><body>";
    $message .= "<h2>Solicitação de Extensão de Licença</h2>";
    $message .= "<p>Um cliente solicitou uma extensão para sua licença de download.</p>";
    $message .= "<p><strong>Detalhes da Licença:</strong></p>";
    $message .= "<ul>";
    $message .= "<li>Código de Licença: {$license['license_code']}</li>";
    $message .= "<li>Cliente: {$license['customer_name']} ({$license['customer_email']})</li>";
    $message .= "<li>Data de Expiração Atual: " . date('d/m/Y H:i', strtotime($license['expiry_date'])) . "</li>";
    $message .= "<li>Dias Solicitados: {$days_to_add}</li>";
    $message .= "</ul>";

    if (!empty($reason)) {
        $message .= "<p><strong>Motivo da Solicitação:</strong></p>";
        $message .= "<p>" . nl2br(htmlspecialchars($reason)) . "</p>";
    }

    $message .= "<p>Para aprovar esta solicitação, acesse o painel de administração e atualize a data de expiração da licença.</p>";
    $message .= "</body></html>";

    require_once __DIR__ . '/email_functions.php';
    $email_sent = send_email($admin_email, 'Administrador', $subject, $message);

    $customer_subject = "Solicitação de Extensão Recebida - {$store_name}";
    $customer_message = "<html><body>";
    $customer_message .= "<h2>Solicitação de Extensão Recebida</h2>";
    $customer_message .= "<p>Olá {$license['customer_name']},</p>";
    $customer_message .= "<p>Recebemos sua solicitação de extensão para a licença <strong>{$license['license_code']}</strong>.</p>";
    $customer_message .= "<p>Nossa equipe irá analisar sua solicitação e entraremos em contato em breve.</p>";
    $customer_message .= "<p>Atenciosamente,<br>{$store_name}</p>";
    $customer_message .= "</body></html>";

    $customer_email_sent = send_email($license['customer_email'], $license['customer_name'], $customer_subject, $customer_message);

    if ($email_sent && $customer_email_sent) {
        return [
            'success' => true,
            'message' => 'Solicitação de extensão enviada com sucesso. Nossa equipe irá analisar e entrar em contato em breve.'
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Erro ao enviar solicitação de extensão. Por favor, entre em contato com o suporte.'
        ];
    }
}

function request_additional_downloads(int $license_id, int $additional_downloads = 3, string $reason = ''): array
{
    if ($license_id <= 0 || $additional_downloads <= 0) {
        return [
            'success' => false,
            'message' => 'Parâmetros inválidos para solicitação de downloads adicionais.'
        ];
    }

    $license = get_license_by_id($license_id);
    if (!$license) {
        return [
            'success' => false,
            'message' => 'Licença não encontrada.'
        ];
    }

    $admin_email = get_setting('admin_email', '');
    if (empty($admin_email)) {
        return [
            'success' => false,
            'message' => 'Erro ao processar solicitação. Email de administrador não configurado.'
        ];
    }

    $store_name = get_setting('store_name', 'Minha Loja');
    $subject = "Solicitação de Downloads Adicionais - {$store_name}";

    $message = "<html><body>";
    $message .= "<h2>Solicitação de Downloads Adicionais</h2>";
    $message .= "<p>Um cliente solicitou downloads adicionais para sua licença.</p>";
    $message .= "<p><strong>Detalhes da Licença:</strong></p>";
    $message .= "<ul>";
    $message .= "<li>Código de Licença: {$license['license_code']}</li>";
    $message .= "<li>Cliente: {$license['customer_name']} ({$license['customer_email']})</li>";
    $message .= "<li>Limite Atual: {$license['download_limit']}</li>";
    $message .= "<li>Downloads Utilizados: {$license['downloads_used']}</li>";
    $message .= "<li>Downloads Adicionais Solicitados: {$additional_downloads}</li>";
    $message .= "</ul>";

    if (!empty($reason)) {
        $message .= "<p><strong>Motivo da Solicitação:</strong></p>";
        $message .= "<p>" . nl2br(htmlspecialchars($reason)) . "</p>";
    }

    $message .= "<p>Para aprovar esta solicitação, acesse o painel de administração e atualize o limite de downloads da licença.</p>";
    $message .= "</body></html>";

    require_once __DIR__ . '/email_functions.php';
    $email_sent = send_email($admin_email, 'Administrador', $subject, $message);

    $customer_subject = "Solicitação de Downloads Adicionais Recebida - {$store_name}";
    $customer_message = "<html><body>";
    $customer_message .= "<h2>Solicitação de Downloads Adicionais Recebida</h2>";
    $customer_message .= "<p>Olá {$license['customer_name']},</p>";
    $customer_message .= "<p>Recebemos sua solicitação de downloads adicionais para a licença <strong>{$license['license_code']}</strong>.</p>";
    $customer_message .= "<p>Nossa equipe irá analisar sua solicitação e entraremos em contato em breve.</p>";
    $customer_message .= "<p>Atenciosamente,<br>{$store_name}</p>";
    $customer_message .= "</body></html>";

    $customer_email_sent = send_email($license['customer_email'], $license['customer_name'], $customer_subject, $customer_message);

    if ($email_sent && $customer_email_sent) {
        return [
            'success' => true,
            'message' => 'Solicitação de downloads adicionais enviada com sucesso. Nossa equipe irá analisar e entrar em contato em breve.'
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Erro ao enviar solicitação de downloads adicionais. Por favor, entre em contato com o suporte.'
        ];
    }
}

function delete_multiple_download_records(array $download_ids): array
{
    if (empty($download_ids)) {
        return ['success' => 0, 'error' => 0];
    }

    $pdo = get_db_connection();
    if (!$pdo) return ['success' => 0, 'error' => count($download_ids)];

    $success_count = 0;
    $error_count = 0;

    try {
        $pdo->beginTransaction();

        foreach ($download_ids as $download_id) {

            $download = db_query(
                "SELECT license_id FROM downloads WHERE id = :id",
                [':id' => $download_id],
                true
            );

            if (!$download) {
                $error_count++;
                continue;
            }

            $license_id = $download['license_id'];

            $sql = "DELETE FROM downloads WHERE id = :id";
            $result = db_query($sql, [':id' => $download_id]);

            if ($result === false) {
                $error_count++;
                continue;
            }

            $sql = "UPDATE licenses SET
                        downloads_used = CASE WHEN downloads_used > 0 THEN downloads_used - 1 ELSE 0 END,
                        updated_at = datetime('now', 'localtime')
                    WHERE id = :id";

            $result = db_query($sql, [':id' => $license_id]);
            if ($result === false) {
                $error_count++;
                continue;
            }

            $success_count++;
        }

        $pdo->commit();
        return ['success' => $success_count, 'error' => $error_count];
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => $success_count, 'error' => (count($download_ids) - $success_count)];
    }
}

function update_license_status(int $license_id, string $status): bool
{
    if ($license_id <= 0 || empty($status)) return false;

    $sql = "UPDATE licenses SET
                status = :status,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";

    return db_query($sql, [
        ':id' => $license_id,
        ':status' => $status
    ]) !== false;
}

function remove_license_download_tokens(int $license_id): bool
{
    if ($license_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {

        $sql = "DELETE FROM download_tokens WHERE license_id = :license_id";
        $result = db_query($sql, [':license_id' => $license_id]);

        return $result !== false;
    } catch (Exception $e) {
        return false;
    }
}

function remove_order_download_tokens(int $order_id): bool
{
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {

        $licenses = db_query(
            "SELECT id FROM licenses WHERE order_id = :order_id",
            [':order_id' => $order_id],
            false, true
        );

        if (empty($licenses)) {
            return true;
        }

        $success = true;
        foreach ($licenses as $license) {

            $result = remove_license_download_tokens($license['id']);
            if (!$result) {
                $success = false;
            }
        }

        return $success;
    } catch (Exception $e) {
        return false;
    }
}

function resend_license_email(int $license_id): bool
{
    if ($license_id <= 0) return false;

    $license = get_license_by_id($license_id);
    if (!$license) return false;

    require_once __DIR__ . '/digital_order_functions.php';

    if (!empty($license['order_id'])) {

        return send_license_order_email($license);
    } else {

        return send_license_download_email($license);
    }
}

function cleanup_expired_download_tokens(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['deleted' => 0, 'success' => false];

    try {

        $sql = "DELETE FROM download_tokens
                WHERE expires_at < datetime('now', 'localtime')";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $deleted_count = $stmt->rowCount();

        return [
            'deleted' => $deleted_count,
            'success' => true,
            'message' => "Removidos {$deleted_count} tokens expirados."
        ];
    } catch (Exception $e) {
        return [
            'deleted' => 0,
            'success' => false,
            'message' => 'Erro ao limpar tokens expirados: ' . $e->getMessage()
        ];
    }
}

function request_new_security_token(int $license_id): array
{
    if ($license_id <= 0) {
        return [
            'success' => false,
            'message' => 'ID de licença inválido.'
        ];
    }

    $license = get_license_by_id($license_id);
    if (!$license) {
        return [
            'success' => false,
            'message' => 'Licença não encontrada.'
        ];
    }

    $token_data = create_download_token($license_id);
    if (!$token_data) {
        return [
            'success' => false,
            'message' => 'Erro ao gerar novo código de segurança.'
        ];
    }

    $email_sent = send_security_token_email($license, $token_data);
    if (!$email_sent) {
        return [
            'success' => false,
            'message' => 'Erro ao enviar email com o código de segurança.'
        ];
    }

    return [
        'success' => true,
        'message' => 'Um novo código de segurança foi enviado para o seu email.',
        'token_data' => $token_data
    ];
}

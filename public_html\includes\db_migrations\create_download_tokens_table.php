<?php

function create_download_tokens_table(PDO $pdo): bool
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='download_tokens'");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            
            $pdo->exec("CREATE TABLE download_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                security_token TEXT NOT NULL,
                email_token TEXT NOT NULL,
                is_verified INTEGER NOT NULL DEFAULT 0,
                is_used INTEGER NOT NULL DEFAULT 0,
                attempts INTEGER NOT NULL DEFAULT 0,
                expires_at TEXT NOT NULL,
                session_id TEXT NOT NULL DEFAULT '',
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
            )");

            
            $pdo->exec("CREATE INDEX idx_download_tokens_license_id ON download_tokens (license_id)");
            $pdo->exec("CREATE INDEX idx_download_tokens_security_token ON download_tokens (security_token)");
            $pdo->exec("CREATE INDEX idx_download_tokens_email_token ON download_tokens (email_token)");
            $pdo->exec("CREATE INDEX idx_download_tokens_expires_at ON download_tokens (expires_at)");
            
            return true;
        }
        
        return true; 
    } catch (Exception $e) {
        return false;
    }
}

if (basename(__FILE__) === basename($_SERVER['SCRIPT_FILENAME'])) {
    require_once __DIR__ . '/../../config.php';
    require_once __DIR__ . '/../../includes/db.php';
    
    $pdo = get_db_connection();
    if (!$pdo) {
        die("Failed to connect to the database.");
    }
    
    if (create_download_tokens_table($pdo)) {
        echo "Download tokens table created successfully.\n";
    } else {
        echo "Error creating download tokens table.\n";
    }
}

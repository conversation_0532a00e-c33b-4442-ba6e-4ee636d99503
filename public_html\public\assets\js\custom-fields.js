

async function addToCart(productId, variationId, quantity, customFieldData = []) {
    if (!productId || isNaN(quantity) || quantity < 1) {
        return { success: false, error: 'Dados de produto inválidos' };
    }

    
    const productElement = document.querySelector(`[data-product-id="${productId}"]`);
    const isDigitalProduct = productElement && productElement.getAttribute('data-product-type') === 'digital';

    
    if (isDigitalProduct) {
        try {
            
            const cartResponse = await fetch(`${window.eshopBaseUrl || ''}/index.php?action=get_cart_preview`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Session-UUID': window.eshopSessionId || ''
                }
            });

            if (cartResponse.ok) {
                const cartData = await cartResponse.json();
                if (cartData.success && cartData.digital_products_count >= 10) {
                    return {
                        success: false,
                        error: 'Não é possível adicionar mais de 10 produtos digitais ao carrinho. Por favor, finalize a compra atual antes de adicionar mais produtos digitais.'
                    };
                }
            }
        } catch (error) {
        }
    }

    const isSimpleProduct = !variationId;

    let addToCartBtn;
    if (isSimpleProduct) {

        addToCartBtn = document.querySelector(`[data-product-id="${productId}"]:not([data-variation-id])`);
        if (!addToCartBtn) {

            addToCartBtn = document.getElementById('addToCartDetail');
        }
    } else {

        addToCartBtn = document.querySelector(`[data-product-id="${productId}"][data-variation-id="${variationId}"]`);
    }

    const originalContent = addToCartBtn ? addToCartBtn.innerHTML : null;

    if (addToCartBtn) {
        addToCartBtn.disabled = true;
        addToCartBtn.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>';
    }

    try {

        const formData = new FormData();
        formData.append('product_id', productId);

        if (!isSimpleProduct && variationId) {
            formData.append('variation_id', variationId);
        }

        formData.append('quantity', quantity);

        if (customFieldData && customFieldData.length > 0) {

            const nonFileFields = customFieldData.filter(field => field.type !== 'file-upload' || !field.file);
            if (nonFileFields.length > 0) {
                formData.append('custom_fields', JSON.stringify(nonFileFields));
            }

            customFieldData.forEach((field, index) => {
                if (field.type === 'file-upload' && field.file) {
                    formData.append(`custom_field_file_${field.field_id}`, field.file);
                }
            });
        }
        for (let pair of formData.entries()) {
            if (pair[0] === 'custom_fields') {
            } else if (pair[1] instanceof File) {
            } else {
            }
        }

        let url = (window.eshopBaseUrl || '') + '/index.php?action=add_to_cart';

        if (window.eshopSessionId && window.eshopSessionParam) {
            url += (url.includes('?') ? '&' : '?') + `${window.eshopSessionParam}=${window.eshopSessionId}`;
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-Session-UUID': window.eshopSessionId || ''
            },
            body: formData,
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {

            const badge = document.getElementById('cart-item-count');
            if (badge) {
                badge.textContent = result.item_count;
                badge.style.display = result.item_count > 0 ? 'inline-block' : 'none';
            }

            localStorage.setItem('eshop_cart_count', result.item_count);

            if (typeof showToast === 'function') {
                showToast('Sucesso', 'Produto adicionado ao carrinho!', 'ri-check-line', 'border-green-500');
            } else {
                alert('Produto adicionado ao carrinho!');
            }

            if (typeof fetchCartPreview === 'function') {
                fetchCartPreview();
            }
        } else {

            const errorMessage = result.error || 'Erro ao adicionar ao carrinho.';

            if (typeof showToast === 'function') {
                showToast('Erro', errorMessage, 'ri-error-warning-line', 'border-red-500');
            } else {
                alert('Erro ao adicionar ao carrinho: ' + errorMessage);
            }

            if (errorMessage.includes('obrigatório') && errorMessage.includes('não preenchido')) {

                const fieldNameMatch = errorMessage.match(/'([^']+)'/);
                if (fieldNameMatch && fieldNameMatch[1]) {
                    const fieldName = fieldNameMatch[1];

                    // Find all custom field elements
                    document.querySelectorAll('.custom-field-item').forEach(item => {
                        const label = item.querySelector('label');
                        if (label && label.textContent.includes(fieldName)) {

                            // Highlight the field
                            const textField = item.querySelector('textarea');
                            if (textField) {
                                textField.classList.add('border-red-500');
                                textField.focus();
                            }

                            const fileField = item.querySelector('input[type="file"]');
                            if (fileField) {
                                const uploadLabel = fileField.closest('.custom-field-upload-label');
                                if (uploadLabel) {
                                    uploadLabel.classList.add('border-red-500');
                                    uploadLabel.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                }
                            }
                        }
                    });
                }
            }
        }

        return result;
    } catch (error) {

        // Show error message
        if (typeof showToast === 'function') {
            showToast('Erro', 'Erro de comunicação ao adicionar ao carrinho.', 'ri-error-warning-line', 'border-red-500');
        } else {
            alert('Erro de comunicação ao adicionar ao carrinho.');
        }

        return { success: false, error: error.message };
    } finally {

        if (addToCartBtn) {
            addToCartBtn.disabled = false;
            addToCartBtn.innerHTML = originalContent;
        }
    }
}
